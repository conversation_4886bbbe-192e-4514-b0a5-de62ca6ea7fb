<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>举报管理</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/access/asset/layui/css/layui.css">
    <style>
        .status-pending { color: #FF5722; }
        .status-processed { color: #009688; }
        .status-rejected { color: #607D8B; }
        .evidence-img { max-width: 50px; max-height: 50px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="layui-container" style="margin-top: 20px;">
        <div class="layui-row">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <h2>举报管理</h2>
                        <div class="layui-row layui-col-space10" style="margin-top: 10px;">
                            <div class="layui-col-md3">
                                <div class="layui-card">
                                    <div class="layui-card-body">
                                        <div class="layui-text-center">
                                            <div class="layui-font-20 status-pending">{$stats.pending}</div>
                                            <div>待处理</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md3">
                                <div class="layui-card">
                                    <div class="layui-card-body">
                                        <div class="layui-text-center">
                                            <div class="layui-font-20 status-processed">{$stats.processed}</div>
                                            <div>已处理</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md3">
                                <div class="layui-card">
                                    <div class="layui-card-body">
                                        <div class="layui-text-center">
                                            <div class="layui-font-20 status-rejected">{$stats.rejected}</div>
                                            <div>已拒绝</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="layui-card-body">
                        <!-- 筛选表单 -->
                        <form class="layui-form" method="get">
                            <input type="hidden" name="c" value="Report">
                            <input type="hidden" name="m" value="index">
                            <div class="layui-row layui-col-space10">
                                <div class="layui-col-md2">
                                    <select name="status" lay-search>
                                        <option value="">全部状态</option>
                                        <option value="pending" {if $status=='pending'}selected{/if}>待处理</option>
                                        <option value="processed" {if $status=='processed'}selected{/if}>已处理</option>
                                        <option value="rejected" {if $status=='rejected'}selected{/if}>已拒绝</option>
                                    </select>
                                </div>
                                <div class="layui-col-md2">
                                    <input type="text" name="reporter_uid" placeholder="举报人ID" value="{$reporter_uid}" class="layui-input">
                                </div>
                                <div class="layui-col-md2">
                                    <input type="text" name="reported_uid" placeholder="被举报人ID" value="{$reported_uid}" class="layui-input">
                                </div>
                                <div class="layui-col-md2">
                                    <input type="text" name="activity_id" placeholder="活动ID" value="{$activity_id}" class="layui-input">
                                </div>
                                <div class="layui-col-md2">
                                    <button type="submit" class="layui-btn">搜索</button>
                                    <a href="?c=Report&m=index" class="layui-btn layui-btn-primary">重置</a>
                                </div>
                            </div>
                        </form>
                        
                        <!-- 数据表格 -->
                        <table class="layui-table" lay-skin="line">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>举报人</th>
                                    <th>被举报人</th>
                                    <th>活动</th>
                                    <th>举报理由</th>
                                    <th>惩罚类型</th>
                                    <th>状态</th>
                                    <th>举报时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {foreach $data.data as $item}
                                <tr>
                                    <td>{$item.id}</td>
                                    <td>
                                        {$item.reporter_nickname}<br>
                                        <small class="layui-text-muted">ID: {$item.reporter_uid}</small>
                                    </td>
                                    <td>
                                        {$item.reported_nickname}<br>
                                        <small class="layui-text-muted">ID: {$item.reported_uid}</small>
                                    </td>
                                    <td>
                                        <a href="?c=Huodong&m=edit&id={$item.activity_id}" target="_blank">
                                            {$item.activity_name}
                                        </a><br>
                                        <small class="layui-text-muted">ID: {$item.activity_id}</small>
                                    </td>
                                    <td>{$item.report_reason}</td>
                                    <td>
                                        {if $item.penalty_type == 'points'}
                                            扣分 {$item.penalty_value}
                                        {elseif $item.penalty_type == 'freeze'}
                                            冻结 {$item.penalty_value} 天
                                        {/if}
                                    </td>
                                    <td>
                                        {if $item.status == 'pending'}
                                            <span class="layui-badge layui-bg-orange">待处理</span>
                                        {elseif $item.status == 'processed'}
                                            <span class="layui-badge layui-bg-green">已处理</span>
                                        {elseif $item.status == 'rejected'}
                                            <span class="layui-badge layui-bg-gray">已拒绝</span>
                                        {/if}
                                    </td>
                                    <td>{$item.created_at}</td>
                                    <td>
                                        <a href="?c=Report&m=view&id={$item.id}" class="layui-btn layui-btn-xs">查看</a>
                                        {if $item.status == 'pending'}
                                            <button class="layui-btn layui-btn-xs layui-btn-normal" onclick="processReport({$item.id}, 'approve')">批准</button>
                                            <button class="layui-btn layui-btn-xs layui-btn-danger" onclick="processReport({$item.id}, 'reject')">拒绝</button>
                                        {/if}
                                    </td>
                                </tr>
                                {/foreach}
                            </tbody>
                        </table>
                        
                        <!-- 分页 -->
                        {if $data.total > 0}
                        <div class="layui-box layui-laypage layui-laypage-default">
                            {$data.page_html}
                        </div>
                        {/if}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/access/asset/layui/layui.js"></script>
    <script>
        layui.use(['form', 'layer'], function(){
            var form = layui.form;
            var layer = layui.layer;
            
            // 处理举报
            window.processReport = function(id, action) {
                var title = action === 'approve' ? '批准举报' : '拒绝举报';
                var content = '<div style="padding: 20px;">' +
                    '<div class="layui-form-item">' +
                    '<label class="layui-form-label">处理备注：</label>' +
                    '<div class="layui-input-block">' +
                    '<textarea id="admin_note" placeholder="请输入处理备注" class="layui-textarea"></textarea>' +
                    '</div>' +
                    '</div>' +
                    '</div>';
                
                layer.open({
                    type: 1,
                    title: title,
                    content: content,
                    area: ['500px', '300px'],
                    btn: ['确定', '取消'],
                    yes: function(index) {
                        var admin_note = document.getElementById('admin_note').value;
                        
                        // 提交处理
                        var form = document.createElement('form');
                        form.method = 'POST';
                        form.action = '?c=Report&m=process';
                        
                        var fields = {
                            'id': id,
                            'action': action,
                            'admin_note': admin_note
                        };
                        
                        for (var key in fields) {
                            var input = document.createElement('input');
                            input.type = 'hidden';
                            input.name = key;
                            input.value = fields[key];
                            form.appendChild(input);
                        }
                        
                        document.body.appendChild(form);
                        form.submit();
                        
                        layer.close(index);
                    }
                });
            };
        });
    </script>
</body>
</html>
