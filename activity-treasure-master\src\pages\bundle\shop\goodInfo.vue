<script setup>
import { defineComponent, watch, ref, reactive } from "vue";
import {
  goodsget_goods_info,
  goodsadd_car,
  goodsget_car_list,
  goodsget_goods_pingjia,
  userfenxiang_event,
} from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
  onShareAppMessage,
  onShareTimeline,
} from "@dcloudio/uni-app";
import myLine from "@/components/myLine.vue";
import { store } from "@/store";
import { navto, getListHeight } from "@/utils";

const gapHeight = ref(0);
const current = ref(0); // 轮播图指示器
const show = ref(false);
const info = ref({});
const number = ref(1); // 选择的数量
const form = ref({});
const badgeNum = ref(0);
const pingjiaList = ref([]);
const vip = ref(false); // 是否是购买礼包
const type = ref("guigeInfo"); // guiguiInfo是规格详情，goodsList是商品列表

onShareTimeline(async () => {
  const res = await userfenxiang_event({ type: 3, item_id: info.value.id });
  return {
    title: info.value.title,
    path: vip.value
      ? `/pages/bundle/shop/goodInfo?vip=true`
      : `/pages/bundle/shop/goodInfo?id=${info.value.id}&guige=${JSON.stringify(
          info.value.guige_info.guige
        )}&pid=${store().$state.userInfo.uid}`,
    imageUrl: info.value.img,
  };
});
onShareAppMessage(async () => {
  const res = await userfenxiang_event({ type: 3, item_id: info.value.id });
  return {
    title: info.value.title,
    path: vip.value
      ? `/pages/bundle/shop/goodInfo?vip=true`
      : `/pages/bundle/shop/goodInfo?id=${info.value.id}&guige=${JSON.stringify(
          info.value.guige_info.guige
        )}&pid=${store().$state.userInfo.uid}`,
    imageUrl: info.value.img,
  };
});
onLoad((e) => {
  if (e.id)
    form.value = {
      goods_id: e.id,
      guige: JSON.parse(e.guige),
    };
  if (e?.pid) store().changePid(e.pid);
  if (e.vip) vip.value = true;
});
onReady(async () => {
  const res = await getListHeight("bottomBox");
  gapHeight.value = res.height;
});
onShow(() => {
  if (!vip.value) getCarList();
  get();
});

const getCarList = async () => {
  const res = await goodsget_car_list();
  if (res.status === "ok") badgeNum.value = res.data.length;
  else badgeNum.value = 0;
};
const get = async () => {
  let res;
  const api = goodsget_goods_info({
    goods_id: form.value.goods_id,
    guige: JSON.stringify(form.value.guige),
  });
  res = await api;
  if (res.status === "ok") {
    info.value = res.data;
    if (!vip.value) {
      const pingjiaRes = await goodsget_goods_pingjia({
        page: 1,
        page_size: 3,
        goods_id: form.value.goods_id,
        guige_id: info.value.guige_info.id,
        has_img: 1,
      });
      pingjiaList.value = pingjiaRes.data || [];
    }
  }
};
// 改变规格
const changeGuige = (i, v) => {
  form.value.guige[i] = v;
  info.value.guige_info[i] = v;
  get();
};
// 加入购物车
const addCart = async () => {
  const res = await goodsadd_car({
    goods_id: form.value.goods_id,
    num: number.value,
    guige_id: info.value.guige_info.id,
  });
  if (res.status === "ok") {
    uni.$u.toast("添加成功");
    getCarList();
  }
};
const back = () =>
  uni.navigateBack({
    fail: (err) => uni.switchTab({ url: "/pages/index" }),
  });
</script>
<template>
  <view class="page">
    <view class="pa z20" style="top: 20rpx; left: 20rpx">
      <u-status-bar></u-status-bar>
      <u-icon
        name="arrow-left"
        size="60rpx"
        @click="back"
        :color="vip ? '#fff' : ''"
      ></u-icon>
    </view>
    <u-swiper
      :list="vip ? info.imgs : info.guige_info?.imgs"
      key-name="img_url"
      :circular="true"
      height="750rpx"
      :indicator="true"
      @change="current = $event.current"
    >
      <template #indicator>
        <view class="df aic jcsb w200">
          <view
            class="h5"
            v-for="(val, i) in vip ? info.imgs : info.guige_info?.imgs"
            :key="i"
            :style="{
              width: vip
                ? 160 / info?.imgs?.length + 'rpx'
                : 160 / info?.guige_info?.imgs?.length + 'rpx',
              background: current === i ? '#EF6227' : '#fff',
            }"
          >
          </view>
        </view>
      </template>
    </u-swiper>
    <view class="pr w">
      <view
        class="pa z20 w b6f"
        style="top: -28rpx; border-radius: 30rpx 30rpx 0rpx 0rpx"
      >
        <u-image
          v-if="!vip && info.guige_info?.is_shiyong"
          width="130rpx"
          height="42rpx"
          :src="`${store().$state.url}huiyuanzhidingchanpin.png`"
        ></u-image>
        <view class="px20">
          <u-text
            margin="21rpx 0 30rpx"
            color="#333333"
            size="32rpx"
            :text="info.name"
          ></u-text>
          <view class="df">
            <u-text
              color="#EF6227"
              size="32rpx"
              bold
              margin="0 0 20rpx"
              prefixIcon="rmb"
              :icon-style="{ top: '4rpx', fontSize: '26rpx', color: '#EF6227' }"
              :text="$u.priceFormat(info.guige_info?.price || info.price, 2)"
            ></u-text>
            <u-text
              v-if="store().$state.userInfo.is_huiyuan"
              align="right"
              color="#EF6227"
              size="28rpx"
              bold
              margin="0 0 20rpx"
              suffix-icon="%"
              :icon-style="{ fontSize: '28rpx', color: '#EF6227' }"
              :text="`会员佣金比例：${$u.priceFormat(info.yongjin_bili)}`"
            ></u-text>
          </view>
          <view class="mb30 df aic jcsb" v-if="!vip">
            <u-text
              color="#AAAAAA"
              size="24rpx"
              :text="`销量：${info.sell_num}`"
            ></u-text>
            <u-text
              align="right"
              color="#AAAAAA"
              size="24rpx"
              :text="`库存：${info.guige_info?.kucun}`"
            ></u-text>
          </view>
          <view class="mb30 df aic jcsb">
            <view
              class="df aic x24 f1"
              @click="
                show = true;
                type = vip ? 'goodsList' : 'guigeInfo';
              "
            >
              <view :class="[vip ? 'f1' : '']">
                <u-text :text="vip ? '商品内容' : '规格：'"></u-text>
              </view>
              <u-text
                v-for="(val, i) in info.guige_info?.guige"
                :key="i"
                :text="val + ';'"
              >
              </u-text>
            </view>
            <view class="df aic">
              <u-text
                margin="0 26rpx"
                color="#AAAAAA"
                size="24rpx"
                :text="`已选择${number}个`"
              ></u-text>
              <u-number-box
                v-model="number"
                :longPress="true"
                min="1"
                :max="vip ? 1 : info.guige_info?.kucun"
              >
                <template #minus>
                  <u-image
                    width="44rpx"
                    height="44rpx"
                    :src="`${store().$state.url}jianshuliang.png`"
                    @click="$u.toast('最少选一个')"
                  ></u-image>
                </template>
                <template #input>
                  <u-text margin="0 10rpx" :text="number"></u-text>
                </template>
                <template #plus>
                  <u-image
                    width="44rpx"
                    height="44rpx"
                    :src="`${store().$state.url}jiashuliang.png`"
                    @click="vip ? $u.toast('礼包最多选一个') : ''"
                  ></u-image>
                </template>
              </u-number-box>
            </view>
          </view>
        </view>
        <template v-if="!vip">
          <u-gap bg-color="#EEEEEE" height="10rpx"></u-gap>
          <view class="p30">
            <myLine bg="#EF6227" w="4" h="20" mr="18" title="商品评价" size="28rpx">
              <u-text
                align="right"
                size="22rpx"
                color="#aaa"
                icon-style="font-size:22rpx;color:#aaa"
                suffix-icon="arrow-right"
                text="查看更多"
                @click="
                  navto(
                    `/pages/bundle/shop/evaluateList?goods_id=${info.id}&guige_id=${info.guige_info.id}`
                  )
                "
              ></u-text>
            </myLine>
            <u-gap height="10rpx"></u-gap>
            <view class="df aic" v-for="(val, i) in pingjiaList" :key="i">
              <u-avatar size="80rpx" :src="val.user?.avatar" mode="aspectFill"></u-avatar>
              <view class="ml20 f1">
                <u-text
                  margin="0 10rpx 0"
                  :text="val.user?.nickname"
                  lines="1"
                  bold
                  size="28rpx"
                ></u-text>
                <u-rate
                  size="12"
                  active-color="#EF6227"
                  :value="val.star_num"
                  :readonly="true"
                ></u-rate>
                <u-text
                  margin="0 10rpx 0"
                  :text="val.contents"
                  lines="2"
                  size="24rpx"
                ></u-text>
              </view>
            </view>
          </view>
        </template>
        <u-gap bg-color="#EEEEEE" height="10rpx"></u-gap>
        <view class="p30">
          <myLine
            bg="#EF6227"
            w="4"
            h="20"
            mr="18"
            title="商品描述"
            size="28rpx"
          ></myLine>
          <u-gap height="30rpx"></u-gap>
          <u-parse :content="info.jieshao || info.contents"></u-parse>
        </view>
        <u-gap :height="gapHeight"></u-gap>
      </view>
    </view>
    <view class="pfx bottom0 p30 z20 b6f w bottomBox">
      <view class="df aic">
        <button class="u-reset-button" open-type="share">
          <u-icon size="44rpx" name="share"></u-icon>
        </button>
        <view class="df aic mr20 x28 c6a f1">
          合计：
          <u-text
            mode="price"
            color="#EF6227"
            size="30rpx"
            :text="$u.priceFormat(info.guige_info?.price || info.price * number, 2)"
          ></u-text>
        </view>
        <u-button
          v-if="!vip"
          color="#FAD000"
          shape="circle"
          :customStyle="{
            margin: '0 20rpx',
            width: '200rpx',
            height: '60rpx',
            fontSize: '28rpx',
            color: '#000',
          }"
          text="加入购物车"
          @click="addCart"
        ></u-button>
        <u-button
          color="#FAD000"
          shape="circle"
          :customStyle="{
            width: !vip ? '160rpx' : '420rpx',
            height: '60rpx',
            fontSize: '28rpx',
            color: '#000',
          }"
          text="去支付"
          @click="
            store().changeGoods(
              [
                {
                  goods_id: vip ? info.id : info.guige_info?.goods_id,
                  guige_id: vip ? '' : info.guige_info?.id,
                  num: number,
                  money:
                    (Math.round(vip ? info.price * 100 : info.guige_info?.price * 100) *
                      number) /
                    100,
                  img: vip ? info.imgs[0].img_url : info.img_url,
                  name: info.name,
                  guige: vip ? info.goods_json : info.guige_info?.guige,
                  is_shiyong: info.guige_info?.is_shiyong,
                },
              ],
              vip ? true : false
            )
          "
        ></u-button>
      </view>
      <u-safe-bottom></u-safe-bottom>
    </view>
    <u-popup
      :show="show"
      mode="bottom"
      round="12"
      :closeOnClickOverlay="true"
      @close="show = false"
    >
      <view class="p20">
        <template v-if="type === 'guigeInfo'">
          <view class="mb50 x32 fb">选择规格：</view>
          <scroll-view class="mb50" scroll-y="true" style="max-height: 500rpx">
            <view class="mb50 x28" v-for="(item, index) in info.guige" :key="index">
              <text class="fb">{{ index }}</text>
              <view class="df aic fw mt30 mb30 r12">
                <view
                  class="p10 x28 mr20"
                  v-for="(val, i) in item"
                  :key="i"
                  :class="[form.guige[index] == val ? 'ball r12 c63' : '']"
                  @tap="changeGuige(index, val)"
                >
                  {{ val }}
                </view>
              </view>
            </view>
          </scroll-view>
          <view class="df mb50">
            <u-text color="#AAAAAA" size="24rpx" :text="`已选择${number}个`"></u-text>
            <u-number-box
              v-model="number"
              :longPress="true"
              min="1"
              :max="info.guige_info?.kucun"
            >
              <template #minus>
                <u-image
                  width="44rpx"
                  height="44rpx"
                  :src="`${store().$state.url}jianshuliang.png`"
                ></u-image>
              </template>
              <template #input>
                <u-text margin="0 10rpx" :text="number"></u-text>
              </template>
              <template #plus>
                <u-image
                  width="44rpx"
                  height="44rpx"
                  :src="`${store().$state.url}jiashuliang.png`"
                ></u-image>
              </template>
            </u-number-box>
          </view>
          <u-button
            class="ball mt50 mb50"
            shape="circle"
            :customStyle="{
              background: 'linear-gradient(142deg, #8efffe 0%, #c6e538 100%)',
              color: '#fff',
              size: '30rpx',
              color: '#333',
            }"
            text="完成"
            @click="show = false"
          ></u-button>
        </template>
        <template v-else>
          <scroll-view class="mb50" scroll-y="true" style="max-height: 500rpx">
            <u-text v-for="(val, i) in info.goods_json" :key="i" :text="val"></u-text>
          </scroll-view>
        </template>
      </view>
    </u-popup>
    <view
      v-if="!vip"
      class="pfx z20"
      style="right: 50rpx; bottom: 180rpx"
      @click="navto('/pages/bundle/shop/shopCart')"
    >
      <u-icon
        :name="`${store().$state.url}shopcart.png`"
        size="50rpx"
        space="10rpx"
        color="#333333"
      ></u-icon>
      <u-badge type="error" max="99" :value="badgeNum"></u-badge>
    </view>
  </view>
</template>

<style scoped lang="less"></style>
<style scoped lang="scss">
.indicator {
  @include flex(row);
  justify-content: center;

  &__dot {
    height: 6px;
    width: 6px;
    border-radius: 100px;
    background-color: rgba(255, 255, 255, 0.35);
    margin: 0 5px;
    transition: background-color 0.3s;

    &--active {
      background-color: #ffffff;
    }
  }
}

.indicator-num {
  padding: 2px 0;
  background-color: rgba(0, 0, 0, 0.35);
  border-radius: 100px;
  width: 35px;
  @include flex;
  justify-content: center;

  &__text {
    color: #ffffff;
    font-size: 12px;
  }
}
.page ::v-deep .u-cell__left-icon-wrap {
  margin-right: 0;
}
.page ::v-deep .u-cell__body {
  padding: 0;
}
</style>
