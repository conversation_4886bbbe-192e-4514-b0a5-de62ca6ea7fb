<script setup>
import { ref, onMounted } from 'vue';
import { getSourceDetail } from '@/api/index.js';
import { store } from '@/store';
import customNavbar from '@/components/customNavbar.vue';

// {{ AURA-X: Add - 出处详情页面. Confirmed via 寸止 }}
const sourceInfo = ref(null);
const loading = ref(true);
const error = ref('');

// 获取页面参数
const pages = getCurrentPages();
const currentPageInstance = pages[pages.length - 1];
const sourceId = currentPageInstance.options.id;

// 获取出处详情
const fetchSourceDetail = async () => {
  if (!sourceId) {
    error.value = '出处ID无效';
    loading.value = false;
    return;
  }

  try {
    loading.value = true;
    const params = {
      uid: store().$state.userInfo?.uid || 0,
      token: store().$state.userInfo?.token || '',
      source_id: sourceId
    };

    const response = await getSourceDetail(params);
    
    if (response.status === 'ok') {
      sourceInfo.value = response.data.source;
    } else {
      error.value = response.msg || '获取出处详情失败';
    }
  } catch (err) {
    console.error('获取出处详情失败:', err);
    error.value = '网络错误，请稍后重试';
  } finally {
    loading.value = false;
  }
};

// 打开网址
const openUrl = (url) => {
  if (!url) return;
  
  // 确保URL有协议前缀
  let fullUrl = url;
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    fullUrl = 'https://' + url;
  }
  
  uni.showModal({
    title: '打开链接',
    content: `是否要打开链接：${fullUrl}`,
    success: (res) => {
      if (res.confirm) {
        // 在小程序中复制链接到剪贴板
        uni.setClipboardData({
          data: fullUrl,
          success: () => {
            uni.showToast({ title: '链接已复制到剪贴板', icon: 'success' });
          }
        });
      }
    }
  });
};

// 页面加载
onMounted(() => {
  fetchSourceDetail();
});
</script>

<template>
  <view class="source-detail-page">
    <!-- 统一导航栏 -->
    <customNavbar 
      :title="sourceInfo?.name || '出处详情'" 
      backIcon="arrow-left"
    />
    
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <u-loading-icon mode="spinner" color="#6AC086" size="40"></u-loading-icon>
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 错误状态 -->
    <view v-else-if="error" class="error-container">
      <u-icon name="error-circle" size="60" color="#ff4757"></u-icon>
      <text class="error-text">{{ error }}</text>
      <view class="retry-btn" @click="fetchSourceDetail">
        <text>重试</text>
      </view>
    </view>
    
    <!-- 出处详情内容 -->
    <view v-else-if="sourceInfo" class="detail-container">
      <!-- 出处基本信息 -->
      <view class="source-header">
        <view class="cover-section">
          <image 
            v-if="sourceInfo.cover_image" 
            :src="sourceInfo.cover_image" 
            class="source-cover"
            mode="aspectFill"
          />
          <view v-else class="default-cover">
            <u-icon name="bookmark-fill" size="60" color="#ccc"></u-icon>
          </view>
        </view>
        
        <view class="info-section">
          <view class="source-name">{{ sourceInfo.name }}</view>
          <view v-if="sourceInfo.category" class="source-category">{{ sourceInfo.category }}</view>
          <view v-if="sourceInfo.publisher" class="source-publisher">{{ sourceInfo.publisher }}</view>
          <view v-if="sourceInfo.publish_year" class="source-year">{{ sourceInfo.publish_year }}年</view>
        </view>
      </view>
      
      <!-- 统计信息 -->
      <view class="stats-section">
        <view class="stat-item">
          <view class="stat-number">{{ sourceInfo.quote_count || 0 }}</view>
          <view class="stat-label">摘录引用</view>
        </view>
      </view>
      
      <!-- 出处简介 -->
      <view v-if="sourceInfo.description" class="description-section">
        <view class="section-title">简介</view>
        <view class="description-content">{{ sourceInfo.description }}</view>
      </view>
      
      <!-- 详细信息 -->
      <view class="details-section">
        <view class="section-title">详细信息</view>
        
        <view v-if="sourceInfo.publisher" class="detail-item">
          <view class="detail-label">出版社</view>
          <view class="detail-value">{{ sourceInfo.publisher }}</view>
        </view>
        
        <view v-if="sourceInfo.publish_year" class="detail-item">
          <view class="detail-label">出版年份</view>
          <view class="detail-value">{{ sourceInfo.publish_year }}</view>
        </view>
        
        <view v-if="sourceInfo.isbn" class="detail-item">
          <view class="detail-label">ISBN</view>
          <view class="detail-value">{{ sourceInfo.isbn }}</view>
        </view>
        
        <view v-if="sourceInfo.category" class="detail-item">
          <view class="detail-label">类别</view>
          <view class="detail-value">{{ sourceInfo.category }}</view>
        </view>
        
        <view v-if="sourceInfo.url" class="detail-item clickable" @click="openUrl(sourceInfo.url)">
          <view class="detail-label">网址</view>
          <view class="detail-value url-value">{{ sourceInfo.url }}</view>
          <u-icon name="arrow-right" size="16" color="#6AC086"></u-icon>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.source-detail-page {
  min-height: 100vh;
  background-color: #f8f9fa;
  
  .loading-container, .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 32rpx;
    
    .loading-text, .error-text {
      margin-top: 24rpx;
      font-size: 28rpx;
      color: #666;
    }
    
    .retry-btn {
      margin-top: 32rpx;
      padding: 16rpx 32rpx;
      background-color: #6AC086;
      border-radius: 50rpx;
      color: white;
      font-size: 28rpx;
    }
  }
  
  .detail-container {
    padding: 32rpx;
    
    .source-header {
      background-color: white;
      border-radius: 20rpx;
      padding: 40rpx;
      margin-bottom: 32rpx;
      display: flex;
      align-items: center;
      
      .cover-section {
        margin-right: 32rpx;
        
        .source-cover {
          width: 120rpx;
          height: 160rpx;
          border-radius: 12rpx;
        }
        
        .default-cover {
          width: 120rpx;
          height: 160rpx;
          border-radius: 12rpx;
          background-color: #f5f5f5;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      
      .info-section {
        flex: 1;
        
        .source-name {
          font-size: 36rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 12rpx;
          line-height: 1.4;
        }
        
        .source-category {
          font-size: 28rpx;
          color: #6AC086;
          margin-bottom: 8rpx;
        }
        
        .source-publisher, .source-year {
          font-size: 26rpx;
          color: #666;
          margin-bottom: 6rpx;
        }
      }
    }
    
    .stats-section {
      background-color: white;
      border-radius: 20rpx;
      padding: 32rpx;
      margin-bottom: 32rpx;
      display: flex;
      justify-content: center;
      
      .stat-item {
        text-align: center;
        
        .stat-number {
          font-size: 48rpx;
          font-weight: 600;
          color: #6AC086;
          margin-bottom: 8rpx;
        }
        
        .stat-label {
          font-size: 26rpx;
          color: #666;
        }
      }
    }
    
    .description-section, .details-section {
      background-color: white;
      border-radius: 20rpx;
      padding: 32rpx;
      margin-bottom: 32rpx;
      
      .section-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 24rpx;
      }
      
      .description-content {
        font-size: 30rpx;
        color: #666;
        line-height: 1.6;
      }
      
      .detail-item {
        display: flex;
        align-items: center;
        padding: 20rpx 0;
        border-bottom: 1rpx solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        &.clickable {
          cursor: pointer;
          
          &:active {
            background-color: #f8f9fa;
          }
        }
        
        .detail-label {
          width: 160rpx;
          font-size: 30rpx;
          color: #666;
          flex-shrink: 0;
        }
        
        .detail-value {
          flex: 1;
          font-size: 30rpx;
          color: #333;
          
          &.url-value {
            color: #6AC086;
            text-decoration: underline;
          }
        }
      }
    }
  }
}
</style>
