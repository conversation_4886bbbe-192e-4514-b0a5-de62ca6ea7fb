# 数据缓存策略分析 (data_caching_strategy.md)

## 项目概述
- **项目名称**: 活动宝数据管理系统
- **缓存技术**: Redis + 应用层缓存
- **数据库**: MySQL
- **缓存框架**: 自定义Cache类 (`\core\Cache`)

---

## 数据库表结构分析

### 🔐 用户相关表 (高频读取)

| 表名 | 主要字段 | 数据特征 | 缓存适用性 |
|------|----------|----------|-----------|
| **user** | uid, nickname, avatar, mobile, role_type, branch_id, is_huiyuan | 用户基础信息，读多写少 | ⭐⭐⭐⭐⭐ 强烈推荐 |
| **user_bank** | id, uid, bank_name, bank_num, is_default | 银行卡信息，读多写少 | ⭐⭐⭐⭐ 推荐 |
| **user_addr** | id, uid, sheng, shi, qu, addr, is_default | 收货地址，读多写少 | ⭐⭐⭐⭐ 推荐 |
| **user_label** | id, uid, label | 用户标签，读多写少 | ⭐⭐⭐ 适合 |
| **user_img** | id, uid, img_url | 用户照片，读多写少 | ⭐⭐⭐ 适合 |
| **user_guanzhu** | id, uid, to_uid | 关注关系，读写频繁 | ⭐⭐ 谨慎使用 |
| **user_notifications** | id, user_id, title, content, is_read | 通知信息，写多读少 | ⭐ 不推荐 |

### 🎯 活动相关表 (混合读写)

| 表名 | 主要字段 | 数据特征 | 缓存适用性 |
|------|----------|----------|-----------|
| **huodong** | id, name, title, img_url, money, date, shi_id | 活动基础信息，读多写少 | ⭐⭐⭐⭐⭐ 强烈推荐 |
| **huodong_type** | id, name, icon | 活动分类，几乎不变 | ⭐⭐⭐⭐⭐ 强烈推荐 |
| **huodong_order** | id, uid, huodong_id, status, pay_time | 报名订单，写多读少 | ⭐⭐ 谨慎使用 |
| **huodong_pingjia** | id, uid, huodong_id, contents, time | 活动评价，读写平衡 | ⭐⭐⭐ 适合 |
| **huodong_shoucang** | id, uid, huodong_id | 收藏记录，读写频繁 | ⭐⭐ 谨慎使用 |
| **huodong_zan** | id, uid, huodong_id | 点赞记录，读写频繁 | ⭐⭐ 谨慎使用 |
| **activity_photos** | id, activity_id, user_id, photo_url | 活动相册，读多写少 | ⭐⭐⭐⭐ 推荐 |

### 🛒 商品相关表 (电商特征)

| 表名 | 主要字段 | 数据特征 | 缓存适用性 |
|------|----------|----------|-----------|
| **goods** | id, name, price, img_url, kucun, is_tuijian | 商品信息，读多写少 | ⭐⭐⭐⭐⭐ 强烈推荐 |
| **goods_type** | id, name, icon | 商品分类，几乎不变 | ⭐⭐⭐⭐⭐ 强烈推荐 |
| **goods_guige** | id, goods_id, name, price, kucun | 商品规格，读多写少 | ⭐⭐⭐⭐ 推荐 |
| **goods_car** | id, uid, goods_id, num | 购物车，读写频繁 | ⭐⭐ 谨慎使用 |
| **goods_order** | id, uid, order_id, status, pay_time | 商品订单，写多读少 | ⭐ 不推荐 |
| **goods_pingjia** | id, uid, goods_id, star_nums, contents | 商品评价，读写平衡 | ⭐⭐⭐ 适合 |

### 🌍 社交相关表 (高并发)

| 表名 | 主要字段 | 数据特征 | 缓存适用性 |
|------|----------|----------|-----------|
| **world_feed** | id, uid, content, images, time | 动态内容，读写频繁 | ⭐⭐⭐ 适合 |
| **world_card** | id, card_date, description, author | 每日卡片，读多写少 | ⭐⭐⭐⭐ 推荐 |
| **world_quote** | id, uid, content, author, source | 摘录内容，读多写少 | ⭐⭐⭐⭐ 推荐 |
| **world_comment** | id, user_id, target_id, content, time | 评论内容，写多读少 | ⭐⭐ 谨慎使用 |
| **world_zan** | id, uid, target_id, target_type | 点赞记录，读写频繁 | ⭐⭐ 谨慎使用 |
| **world_shoucang** | id, uid, target_id, target_type | 收藏记录，读写频繁 | ⭐⭐ 谨慎使用 |

### 💰 财务相关表 (敏感数据)

| 表名 | 主要字段 | 数据特征 | 缓存适用性 |
|------|----------|----------|-----------|
| **commission_log** | id, uid, money, type, status | 佣金记录，写多读少 | ⭐ 不推荐 |
| **activity_income_log** | id, activity_id, publisher_uid, total_amount | 活动收入，写多读少 | ⭐ 不推荐 |
| **user_tixian** | id, uid, money, status, time | 提现申请，写多读少 | ⭐ 不推荐 |
| **user_zhangdan** | id, uid, money, type, time | 账单记录，写多读少 | ⭐ 不推荐 |
| **huiyuan_order** | id, uid, money, status, pay_time | 会员订单，写多读少 | ⭐ 不推荐 |

### ⚙️ 系统配置表 (静态数据)

| 表名 | 主要字段 | 数据特征 | 缓存适用性 |
|------|----------|----------|-----------|
| **config** | name, val, shuoming | 系统配置，几乎不变 | ⭐⭐⭐⭐⭐ 强烈推荐 |
| **img_config** | mark, name, img_url | 图片配置，几乎不变 | ⭐⭐⭐⭐⭐ 强烈推荐 |
| **media_config** | mark, media_type, media_url | 媒体配置，几乎不变 | ⭐⭐⭐⭐⭐ 强烈推荐 |
| **lunbotu** | id, title, img_url, position | 轮播图，读多写少 | ⭐⭐⭐⭐⭐ 强烈推荐 |
| **gaode_district** | adcode, name, level, parent_adcode | 地区数据，几乎不变 | ⭐⭐⭐⭐⭐ 强烈推荐 |
| **china** | id, name, deep, pid | 中国地区，几乎不变 | ⭐⭐⭐⭐⭐ 强烈推荐 |
| **html** | id, type, title, contents | 静态页面，几乎不变 | ⭐⭐⭐⭐⭐ 强烈推荐 |

---

## 前端数据对象分析

### 📱 小程序页面数据 (activity-treasure-master)

#### 首页数据 (index.vue)
```javascript
// 高频访问数据 - 强烈推荐缓存
{
  cityInfo: { name: "北京市", adcode: "110100" },    // 城市信息
  bannerList: [...],                                // 轮播图列表
  activityTypes: [...],                            // 活动分类
  hotActivities: [...],                            // 热门活动
  nearbyActivities: [...]                          // 附近活动
}

// 用户相关数据 - 推荐缓存
{
  userInfo: { uid, nickname, avatar, is_huiyuan },  // 用户基础信息
  unreadCount: 5,                                   // 未读通知数
  locationInfo: { lat, lng, address }               // 定位信息
}
```

#### 活动详情页 (huodong-detail.vue)
```javascript
// 活动详情 - 推荐缓存
{
  activityInfo: {
    id, name, title, img_url, money, date,
    start_time, end_time, addr, contents, user
  },
  registrationList: [...],                         // 报名列表
  commentList: [...],                              // 评论列表
  photoList: [...]                                 // 活动相册
}

// 用户状态 - 谨慎缓存
{
  isRegistered: true,                              // 是否已报名
  isFavorited: false,                              // 是否已收藏
  isLiked: true                                    // 是否已点赞
}
```

#### 个人中心 (profile.vue)
```javascript
// 用户详细信息 - 推荐缓存
{
  userProfile: {
    uid, nickname, avatar, mobile, sex, birthday,
    gexingqianming, is_huiyuan, money, labels, imgs
  },
  addressList: [...],                              // 收货地址
  bankList: [...],                                 // 银行卡列表
  branchInfo: { branch_name, leader_name }         // 分会信息
}

// 统计数据 - 适合缓存
{
  stats: {
    followCount: 10,                               // 关注数
    fansCount: 5,                                  // 粉丝数
    activityCount: 3,                              // 发布活动数
    commissionTotal: "150.00"                      // 总佣金
  }
}
```

---

## API接口数据格式分析

### 🔄 高频API接口

#### 用户登录 (/User/login)
```json
// 响应数据 - 强烈推荐缓存用户基础信息
{
  "status": "ok",
  "data": {
    "uid": 123,
    "mobile": "***********",
    "avatar": "http://example.com/avatar.jpg",
    "nickname": "用户昵称",
    "sex": 1,
    "money": "100.00",
    "token": "abc123...",
    "is_huiyuan": 1,
    "labels": [...],
    "imgs": [...]
  }
}
```

#### 活动列表 (/Huodong/get_list)
```json
// 响应数据 - 推荐缓存活动基础信息
{
  "status": "ok", 
  "data": [
    {
      "id": 1,
      "name": "活动名称",
      "title": "活动标题", 
      "img_url": "封面图",
      "money": "100.00",
      "date": "2025-07-30",
      "start_time": "14:00:00",
      "shi": "北京市",
      "distance": "1.2km",
      "user": { "nickname": "发布者", "avatar": "头像" }
    }
  ],
  "count": 50
}
```

#### 系统配置 (/Config/app)
```json
// 响应数据 - 强烈推荐缓存
{
  "status": "ok",
  "data": {
    "config": {
      "app_name": { "val": "活动宝" },
      "huiyuan_price": { "val": "500" },
      "min_tixian_money": { "val": "10" }
    },
    "img_config": {
      "app_logo": { "img_url": "logo地址" }
    }
  }
}
```

---

## Redis缓存策略建议

### 🚀 强烈推荐缓存 (缓存时间: 1-24小时)

#### 系统配置类
```redis
# 键名格式: config:[类型]
config:app                    # APP配置信息 (24小时)
config:splash_media          # 开屏媒体配置 (12小时)  
config:city_list             # 城市列表 (24小时)
config:activity_types        # 活动分类 (24小时)
config:goods_types           # 商品分类 (24小时)

# 轮播图配置
banner:position:1            # 首页轮播图 (2小时)
banner:position:2            # 其他位置轮播图 (2小时)

# 地区数据
area:gaode_china            # 高德中国地区数据 (24小时)
area:city:[adcode]          # 具体城市信息 (24小时)
```

#### 用户基础信息
```redis
# 键名格式: user:[uid]:[类型]
user:123:info               # 用户基础信息 (2小时)
user:123:labels             # 用户标签 (1小时)
user:123:imgs               # 用户照片 (1小时)
user:123:addresses          # 收货地址 (1小时)
user:123:banks              # 银行卡列表 (1小时)
```

#### 活动基础信息
```redis
# 键名格式: activity:[id]:[类型]
activity:123:info           # 活动详情 (30分钟)
activity:123:photos         # 活动相册 (1小时)
activity:list:city:110100   # 城市活动列表 (15分钟)
activity:list:hot           # 热门活动列表 (30分钟)
```

### ⚡ 推荐缓存 (缓存时间: 5-60分钟)

#### 商品信息
```redis
# 键名格式: goods:[id]:[类型]
goods:123:info              # 商品详情 (30分钟)
goods:123:specs             # 商品规格 (30分钟)
goods:list:type:1           # 分类商品列表 (15分钟)
goods:list:recommend        # 推荐商品 (30分钟)
```

#### 社交内容
```redis
# 键名格式: social:[类型]:[id]
social:feeds:latest         # 最新动态列表 (5分钟)
social:cards:daily          # 每日卡片 (60分钟)
social:quotes:random        # 随机摘录 (30分钟)
```

### ⚠️ 谨慎缓存 (缓存时间: 1-10分钟)

#### 用户状态类
```redis
# 键名格式: status:[uid]:[类型]
status:123:cart             # 购物车状态 (5分钟)
status:123:favorites        # 收藏状态 (10分钟)
status:123:follows          # 关注状态 (10分钟)
```

#### 统计数据
```redis
# 键名格式: stats:[类型]:[id]
stats:activity:123:views    # 活动浏览量 (1分钟)
stats:user:123:fans         # 用户粉丝数 (5分钟)
stats:goods:123:sales       # 商品销量 (5分钟)
```

### ❌ 不推荐缓存

#### 敏感财务数据
- 佣金记录 (commission_log)
- 提现申请 (user_tixian)  
- 账单记录 (user_zhangdan)
- 订单支付状态 (实时性要求高)

#### 高频变更数据
- 通知消息 (user_notifications)
- 实时聊天记录
- 库存数量 (需要实时准确)
- 支付状态 (安全性要求高)

---

## 缓存更新机制

### 🔄 主动更新策略

#### 数据变更触发更新
```php
// 用户信息更新时
public function update($uid, $token) {
    // 更新数据库
    $result = Db()->table("user")->where("uid={$uid}")->update($data);
    
    // 清除相关缓存
    \core\Cache::delCache("user:{$uid}:info");
    \core\Cache::delCache("user:{$uid}:labels");
    
    return $result;
}

// 活动信息更新时  
public function update_huodong($uid, $token, $huodong_id) {
    // 更新数据库
    $result = Db()->table("huodong")->where("id={$huodong_id}")->update($data);
    
    // 清除相关缓存
    \core\Cache::delCache("activity:{$huodong_id}:info");
    \core\Cache::delCache("activity:list:city:" . $data['shi_id']);
    
    return $result;
}
```

#### 定时刷新策略
```php
// 每日定时刷新静态数据
// 凌晨2点刷新系统配置缓存
\core\Cache::delCache("config:app");
\core\Cache::delCache("config:city_list");

// 每小时刷新热门数据
\core\Cache::delCache("activity:list:hot");
\core\Cache::delCache("goods:list:recommend");
```

### ⏰ 缓存失效条件

#### 时间失效
- **系统配置**: 24小时自动失效
- **用户信息**: 2小时自动失效  
- **活动列表**: 15-30分钟自动失效
- **统计数据**: 1-5分钟自动失效

#### 事件失效
- **用户操作**: 立即清除相关用户缓存
- **内容发布**: 立即清除相关列表缓存
- **状态变更**: 立即清除相关状态缓存
- **系统维护**: 清除所有缓存

---

## 缓存性能优化建议

### 🎯 缓存命中率优化

#### 预热策略
```php
// 系统启动时预热热点数据
public function warmupCache() {
    // 预热系统配置
    $this->getAppConfig();
    
    // 预热热门活动
    $this->getHotActivities();
    
    // 预热城市列表
    $this->getCityList();
}
```

#### 分层缓存
```php
// L1: 应用内存缓存 (最快)
// L2: Redis缓存 (快)  
// L3: 数据库 (慢)

public function getUserInfo($uid) {
    // L1缓存检查
    if ($data = $this->memoryCache->get("user:{$uid}")) {
        return $data;
    }
    
    // L2缓存检查
    if ($data = \core\Cache::getCache("user:{$uid}:info")) {
        $this->memoryCache->set("user:{$uid}", $data, 300);
        return $data;
    }
    
    // L3数据库查询
    $data = Db()->table("user")->where("uid={$uid}")->fetch();
    \core\Cache::setCache("user:{$uid}:info", $data, 7200);
    $this->memoryCache->set("user:{$uid}", $data, 300);
    
    return $data;
}
```

### 📊 缓存监控指标

#### 关键指标
- **命中率**: 目标 > 85%
- **响应时间**: 目标 < 50ms
- **内存使用**: 目标 < 80%
- **网络延迟**: 目标 < 10ms

#### 监控方案
```php
// 缓存统计
public function getCacheStats() {
    return [
        'hit_rate' => $this->calculateHitRate(),
        'memory_usage' => $this->getMemoryUsage(),
        'key_count' => $this->getKeyCount(),
        'expired_keys' => $this->getExpiredKeys()
    ];
}
```

---

---

## 前端文件结构分析

### 📱 小程序Vue页面统计 (activity-treasure-master)

#### 主要页面 (8个)
| 页面文件 | 功能描述 | 数据特征 | 缓存建议 |
|----------|----------|----------|----------|
| **App.vue** | 应用入口 | 全局配置、主题设置 | ⭐⭐⭐⭐⭐ 强烈推荐 |
| **index.vue** | 首页 | 活动列表、轮播图、城市信息 | ⭐⭐⭐⭐ 推荐 |
| **world.vue** | 社交世界 | 动态、卡片、摘录列表 | ⭐⭐⭐ 适合 |
| **my.vue** | 个人中心 | 用户信息、统计数据 | ⭐⭐⭐⭐ 推荐 |
| **shop.vue** | 商城 | 商品列表、分类信息 | ⭐⭐⭐⭐ 推荐 |
| **msg.vue** | 消息中心 | 通知列表、聊天记录 | ⭐⭐ 谨慎使用 |
| **addActive.vue** | 发布活动 | 表单数据、地图选择 | ⭐ 不推荐 |
| **splash.vue** | 启动页 | 开屏媒体、配置信息 | ⭐⭐⭐⭐⭐ 强烈推荐 |

#### 功能页面分类统计

##### 🎯 活动相关页面 (2个)
```
src/pages/bundle/activity/
├── albumDetail.vue          # 活动相册详情
└──
src/pages/bundle/index/
├── activeInfo.vue           # 活动详情页
├── addActiveMap.vue         # 活动地图选择
├── advertisement.vue        # 广告页面
├── moreActive.vue          # 更多活动
└── webview.vue             # 内嵌网页
```

##### 👥 用户管理页面 (25个)
```
src/pages/bundle/user/
├── addMoney.vue            # 充值页面
├── addTag.vue              # 添加标签
├── coinInfo.vue            # 积分信息
├── commissionList.vue      # 佣金列表
├── edit.vue                # 编辑资料
├── evaluate.vue            # 评价页面
├── extensionCode.vue       # 推广码
├── extensionRecord.vue     # 推广记录
├── jiesuanList.vue         # 结算列表
├── membership.vue          # 会员中心
├── myActivity.vue          # 我的活动
├── myFans.vue              # 我的粉丝
├── myFollow.vue            # 我的关注
├── myOrder.vue             # 我的订单
├── myOrderInfo.vue         # 订单详情
├── notificationSettings.vue # 通知设置
├── onlyVip.vue             # VIP专享
├── pointsLog.vue           # 积分日志
├── profitList.vue          # 收益列表
├── returnGoods.vue         # 退货申请
├── shareRecord.vue         # 分享记录
├── singUp.vue              # 注册页面
├── topUp.vue               # 充值
├── topUpList.vue           # 充值记录
├── trialClaim.vue          # 试用申请
├── trialGiftList.vue       # 试用礼品
├── userActivity.vue        # 用户活动
├── vip.vue                 # VIP页面
├── withDraw.vue            # 提现
└── withDrawList.vue        # 提现记录
```

##### 🛒 商城相关页面 (3个)
```
src/pages/bundle/shop/
├── evaluateList.vue        # 评价列表
├── goodInfo.vue            # 商品详情
└── shopCart.vue            # 购物车
```

##### 🌍 社交功能页面 (12个)
```
src/pages/bundle/world/
├── card/
│   ├── detail.vue          # 卡片详情
│   └── index.vue           # 卡片列表
├── diary/
│   ├── detail.vue          # 日记详情
│   ├── index.vue           # 日记列表
│   ├── list.vue            # 日记列表页
│   └── post.vue            # 发布日记
├── feed/
│   ├── detail.vue          # 动态详情
│   ├── index.vue           # 动态列表
│   └── post.vue            # 发布动态
└── quote/
    ├── index.vue           # 摘录首页
    ├── list.vue            # 摘录列表
    └── post.vue            # 发布摘录
```

##### 🏢 分会长管理页面 (6个)
```
src/pages/bundle/branch_president/
├── activityManagement.vue  # 活动管理
├── apply.vue               # 申请分会长
├── commissionOverview.vue  # 佣金概览
├── dashboard.vue           # 管理面板
├── memberManagement.vue    # 成员管理
└── statistics.vue          # 统计数据
```

##### 🔧 通用功能页面 (6个)
```
src/pages/bundle/common/
├── addAddr.vue             # 添加地址
├── addressList.vue         # 地址列表
├── confirmOrder.vue        # 确认订单
├── login.vue               # 登录页面
├── pay.vue                 # 支付页面
└── xieyi.vue               # 用户协议
```

##### 💬 消息相关页面 (1个)
```
src/pages/bundle/msg/
└── personage.vue           # 个人消息
```

#### 组件统计 (15个)
```
src/components/
├── customNavbar.vue        # 自定义导航栏
├── CustomTabBar.vue        # 自定义标签栏
├── empty-state.vue         # 空状态组件
├── form-validator.vue      # 表单验证组件
├── iptInfo.vue             # 输入信息组件
├── lazy-image.vue          # 懒加载图片
├── loading-skeleton.vue    # 加载骨架屏
├── modal.vue               # 模态框组件
├── myBottom.vue            # 底部组件
├── myImage.vue             # 图片组件
├── myLine.vue              # 分割线组件
├── myStitle.vue            # 小标题组件
├── myTitle.vue             # 标题组件
├── placeholders/           # 占位符组件 (4个)
└── share-popup/            # 分享弹窗组件 (1个)
```

### 🔧 JavaScript工具文件分析 (18个)

#### 核心工具文件
| 文件名 | 功能描述 | 缓存适用性 | 重要程度 |
|--------|----------|-----------|----------|
| **main.js** | 应用入口文件 | ❌ 不缓存 | ⭐⭐⭐⭐⭐ |
| **index.js** (utils) | 工具函数集合 | ⭐⭐⭐ 适合 | ⭐⭐⭐⭐⭐ |
| **request.js** | HTTP请求封装 | ❌ 不缓存 | ⭐⭐⭐⭐⭐ |
| **auth.js** | 认证授权管理 | ⭐⭐ 谨慎使用 | ⭐⭐⭐⭐⭐ |
| **BaseUrl.js** | API基础地址配置 | ⭐⭐⭐⭐⭐ 强烈推荐 | ⭐⭐⭐⭐ |

#### 数据管理文件
| 文件名 | 功能描述 | 缓存适用性 | 重要程度 |
|--------|----------|-----------|----------|
| **cacheManager.js** | 缓存管理器 | ❌ 不缓存 | ⭐⭐⭐⭐⭐ |
| **defaultData.js** | 默认数据配置 | ⭐⭐⭐⭐⭐ 强烈推荐 | ⭐⭐⭐⭐ |
| **china.js** | 中国地区数据 | ⭐⭐⭐⭐⭐ 强烈推荐 | ⭐⭐⭐⭐ |
| **china_backup.js** | 地区数据备份 | ⭐⭐⭐⭐⭐ 强烈推荐 | ⭐⭐⭐ |

#### 功能工具文件
| 文件名 | 功能描述 | 缓存适用性 | 重要程度 |
|--------|----------|-----------|----------|
| **commonUtils.js** | 通用工具函数 | ⭐⭐⭐ 适合 | ⭐⭐⭐⭐ |
| **errorHandler.js** | 错误处理 | ❌ 不缓存 | ⭐⭐⭐⭐ |
| **permissions.js** | 权限管理 | ⭐⭐ 谨慎使用 | ⭐⭐⭐⭐ |
| **security.js** | 安全工具 | ❌ 不缓存 | ⭐⭐⭐⭐ |
| **systemInfo.js** | 系统信息获取 | ⭐⭐⭐ 适合 | ⭐⭐⭐ |

#### 分享功能文件
| 文件名 | 功能描述 | 缓存适用性 | 重要程度 |
|--------|----------|-----------|----------|
| **shareImage.js** | 图片分享 | ⭐⭐ 谨慎使用 | ⭐⭐⭐ |
| **painterConfig.js** | 画布配置 | ⭐⭐⭐⭐ 推荐 | ⭐⭐⭐ |
| **painterShare.js** | 画布分享 | ⭐⭐ 谨慎使用 | ⭐⭐⭐ |
| **uniShareConfig.js** | uni-app分享配置 | ⭐⭐⭐⭐ 推荐 | ⭐⭐⭐ |

#### 状态管理文件
| 文件名 | 功能描述 | 缓存适用性 | 重要程度 |
|--------|----------|-----------|----------|
| **store/index.js** | Vuex状态管理 | ❌ 不缓存 | ⭐⭐⭐⭐⭐ |
| **store/counter.js** | 计数器状态 | ❌ 不缓存 | ⭐⭐ |

#### API接口文件
| 文件名 | 功能描述 | 缓存适用性 | 重要程度 |
|--------|----------|-----------|----------|
| **api/index.js** | API接口定义 | ⭐⭐⭐ 适合 | ⭐⭐⭐⭐⭐ |

#### 常量配置文件
| 文件名 | 功能描述 | 缓存适用性 | 重要程度 |
|--------|----------|-----------|----------|
| **constants/index.js** | 常量定义 | ⭐⭐⭐⭐⭐ 强烈推荐 | ⭐⭐⭐⭐ |

---

## 前端数据缓存策略详细分析

### 🎯 页面级缓存策略

#### 首页数据缓存 (index.vue)
```javascript
// 强烈推荐缓存 - 15-30分钟
const homePageCache = {
  'home:banner_list': bannerData,           // 轮播图数据
  'home:activity_types': activityTypes,     // 活动分类
  'home:hot_activities': hotActivities,     // 热门活动
  'home:city_info': cityInfo               // 城市信息
}

// 推荐缓存 - 5-10分钟
const homePageDynamic = {
  'home:nearby_activities': nearbyList,     // 附近活动
  'home:user_location': locationInfo,       // 用户位置
  'home:weather_info': weatherData          // 天气信息
}
```

#### 个人中心缓存 (my.vue)
```javascript
// 推荐缓存 - 30分钟-2小时
const profileCache = {
  'profile:user_info': userProfile,         // 用户基础信息
  'profile:statistics': userStats,          // 统计数据
  'profile:vip_status': vipInfo,           // VIP状态
  'profile:commission_total': commissionSum // 佣金总额
}

// 谨慎缓存 - 5-10分钟
const profileDynamic = {
  'profile:unread_count': unreadCount,      // 未读消息数
  'profile:balance': userBalance,           // 账户余额
  'profile:recent_activities': recentList   // 最近活动
}
```

#### 社交世界缓存 (world.vue)
```javascript
// 适合缓存 - 5-15分钟
const worldCache = {
  'world:daily_cards': dailyCards,          // 每日卡片
  'world:hot_feeds': hotFeeds,             // 热门动态
  'world:random_quotes': randomQuotes,      // 随机摘录
  'world:trending_tags': trendingTags       // 热门标签
}

// 谨慎缓存 - 1-5分钟
const worldDynamic = {
  'world:latest_feeds': latestFeeds,        // 最新动态
  'world:user_feeds': userFeeds,           // 用户动态
  'world:follow_feeds': followFeeds         // 关注动态
}
```

### 🔧 工具函数缓存策略

#### 地区数据缓存
```javascript
// 强烈推荐缓存 - 24小时
const locationCache = {
  'location:china_provinces': chinaData,    // 中国省份数据
  'location:city_list': cityList,          // 城市列表
  'location:district_tree': districtTree,   // 地区树形结构
  'location:gaode_mapping': gaodeMapping    // 高德地区映射
}
```

#### 配置数据缓存
```javascript
// 强烈推荐缓存 - 12-24小时
const configCache = {
  'config:api_base_url': baseUrl,          // API基础地址
  'config:app_constants': constants,        // 应用常量
  'config:default_data': defaultData,       // 默认数据
  'config:share_config': shareConfig        // 分享配置
}
```

#### 用户权限缓存
```javascript
// 推荐缓存 - 1-2小时
const permissionCache = {
  'permission:user_roles': userRoles,       // 用户角色
  'permission:feature_flags': featureFlags, // 功能开关
  'permission:access_control': accessList   // 访问控制
}
```

### 📊 组件级缓存策略

#### 导航组件缓存
```javascript
// 强烈推荐缓存 - 2-6小时
const navCache = {
  'nav:tab_config': tabConfig,             // 标签栏配置
  'nav:menu_items': menuItems,             // 菜单项
  'nav:user_avatar': userAvatar            // 用户头像
}
```

#### 表单组件缓存
```javascript
// 适合缓存 - 30分钟-1小时
const formCache = {
  'form:validation_rules': validationRules, // 验证规则
  'form:default_values': defaultValues,     // 默认值
  'form:option_lists': optionLists          // 选项列表
}
```

---

## 前端缓存实现建议

### 🚀 缓存层级设计

#### L1: 内存缓存 (最快 - 毫秒级)
```javascript
// 使用Map或WeakMap实现
class MemoryCache {
  constructor() {
    this.cache = new Map();
    this.maxSize = 100;
  }

  set(key, value, ttl = 300000) { // 5分钟默认
    this.cache.set(key, {
      value,
      expires: Date.now() + ttl
    });
  }

  get(key) {
    const item = this.cache.get(key);
    if (!item || Date.now() > item.expires) {
      this.cache.delete(key);
      return null;
    }
    return item.value;
  }
}
```

#### L2: 本地存储缓存 (快 - 10ms级)
```javascript
// 使用uni.setStorageSync实现
class LocalStorageCache {
  set(key, value, ttl = 1800000) { // 30分钟默认
    const data = {
      value,
      expires: Date.now() + ttl
    };
    uni.setStorageSync(key, JSON.stringify(data));
  }

  get(key) {
    try {
      const data = JSON.parse(uni.getStorageSync(key) || '{}');
      if (!data.expires || Date.now() > data.expires) {
        uni.removeStorageSync(key);
        return null;
      }
      return data.value;
    } catch (e) {
      return null;
    }
  }
}
```

#### L3: 网络请求 (慢 - 100ms+级)
```javascript
// API请求作为最后的数据源
class ApiCache {
  async get(url, params = {}) {
    const cacheKey = this.generateKey(url, params);

    // 检查L1缓存
    let data = memoryCache.get(cacheKey);
    if (data) return data;

    // 检查L2缓存
    data = localStorageCache.get(cacheKey);
    if (data) {
      memoryCache.set(cacheKey, data, 300000); // 5分钟
      return data;
    }

    // 发起网络请求
    data = await this.request(url, params);

    // 根据数据类型设置不同的缓存时间
    const ttl = this.getTTL(url);
    localStorageCache.set(cacheKey, data, ttl);
    memoryCache.set(cacheKey, data, Math.min(ttl, 300000));

    return data;
  }
}
```

### ⚡ 缓存更新策略

#### 主动更新
```javascript
// 用户操作触发更新
const updateUserCache = (userId, newData) => {
  const keys = [
    `user:${userId}:info`,
    `user:${userId}:stats`,
    `profile:user_info`
  ];

  keys.forEach(key => {
    memoryCache.delete(key);
    localStorageCache.delete(key);
  });

  // 预热新数据
  memoryCache.set(`user:${userId}:info`, newData, 7200000);
};
```

#### 被动更新
```javascript
// 定时检查更新
const scheduleUpdate = () => {
  setInterval(() => {
    // 清理过期缓存
    memoryCache.cleanup();
    localStorageCache.cleanup();

    // 预热热点数据
    preloadHotData();
  }, 300000); // 5分钟检查一次
};
```

---

**文档生成时间**: 2025-07-29
**缓存版本**: Redis 6.0+
**维护状态**: 持续优化中
