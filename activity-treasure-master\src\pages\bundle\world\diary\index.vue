<script setup>
import { ref, onMounted, onActivated, onUnmounted } from 'vue';
import { getDiaries } from '@/api/index.js';
import { store } from '@/store';
import { navto } from '@/utils';

console.log('DiaryIndex: 脚本开始执行');

// 状态管理
const diaries = ref([]);
const loading = ref(true); // 恢复正常的加载状态
const refreshing = ref(false);
const loadingMore = ref(false);
const hasMore = ref(true);
const currentPage = ref(1);
const pageSize = 10;

console.log('DiaryIndex: 初始化完成');

// 加载日记列表
const loadDiaries = async (page = 1, isRefresh = false) => {
  try {
    console.log('DiaryIndex: 开始加载日记数据', { page, isRefresh });

    if (isRefresh) {
      refreshing.value = true;
      currentPage.value = 1;
    } else if (page > 1) {
      loadingMore.value = true;
    } else {
      loading.value = true;
    }

    const params = {
      page: page,
      page_size: pageSize,
      uid: store().$state.userInfo?.uid || 0,
      token: store().$state.userInfo?.token || '',
      type: 'diary' // 只获取日记类型
    };

    console.log('DiaryIndex: API请求参数', params);
    const res = await getDiaries(params);
    console.log('DiaryIndex: API响应结果', res);

    if (res.status === 'ok') {
      const newDiaries = res.data?.list || [];
      console.log('DiaryIndex: 获取到日记数据', newDiaries.length, '条');

      if (isRefresh || page === 1) {
        diaries.value = newDiaries;
      } else {
        diaries.value = [...diaries.value, ...newDiaries];
      }

      hasMore.value = newDiaries.length === pageSize;
      currentPage.value = page;
    } else if (res.status === 'empty') {
      console.log('DiaryIndex: 服务器返回空数据');
      if (isRefresh || page === 1) {
        diaries.value = [];
      }
      hasMore.value = false;
    } else {
      console.warn('DiaryIndex: API返回错误状态', res.status, res.msg);
      uni.showToast({ title: res.msg || '加载失败', icon: 'none' });
    }
  } catch (error) {
    console.error('DiaryIndex: 加载日记失败:', error);
    uni.showToast({ title: '加载失败', icon: 'none' });
  } finally {
    loading.value = false;
    refreshing.value = false;
    loadingMore.value = false;
  }
};

// 下拉刷新
const onRefresh = () => {
  loadDiaries(1, true);
};

// 上拉加载更多
const onLoadMore = () => {
  if (!loadingMore.value && hasMore.value) {
    loadDiaries(currentPage.value + 1);
  }
};

// {{ AURA-X: Modify - 修改日记详情页跳转路径，使用独立的diary/detail.vue页面. Confirmed via 寸止 }}
// 查看日记详情
const viewDiary = (diary) => {
  navto(`/pages/bundle/world/diary/detail?id=${diary.id}`);
};

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '';

  // 修复iOS日期格式问题
  const formattedTimeStr = timeStr.replace(/-/g, '/');
  const time = new Date(formattedTimeStr);
  const now = new Date();
  const diff = now - time;

  // 1小时内显示xx分钟前
  if (diff < 3600000) { // 1小时 = 3600000毫秒
    const minutes = Math.floor(diff / 60000);
    return minutes <= 0 ? '刚刚' : `${minutes}分钟前`;
  }

  // 1天内显示xx小时前
  if (diff < 86400000) { // 1天 = 86400000毫秒
    const hours = Math.floor(diff / 3600000);
    return `${hours}小时前`;
  }

  // 超过1天显示具体日期
  const year = time.getFullYear();
  const month = String(time.getMonth() + 1).padStart(2, '0');
  const day = String(time.getDate()).padStart(2, '0');
  const hours = String(time.getHours()).padStart(2, '0');
  const minutes = String(time.getMinutes()).padStart(2, '0');

  // 判断是否是今年
  if (year === now.getFullYear()) {
    return `${month}-${day} ${hours}:${minutes}`;
  } else {
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }
};

// {{ AURA-X: Modify - 修复位置信息JSON解析错误，添加HTML实体解码处理. Confirmed via 寸止. }}
// 安全解析位置信息
const getLocationName = (locationStr) => {
  if (!locationStr) return '';

  try {
    // 检查是否是JSON格式（以{开头）
    if (typeof locationStr === 'string' && locationStr.trim().startsWith('{')) {
      // 先进行HTML实体解码处理
      let decodedStr = locationStr
        .replace(/&quot;/g, '"')
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&#39;/g, "'");

      const locationObj = JSON.parse(decodedStr);
      return locationObj.name || locationObj.address || locationStr;
    }
    // 如果不是JSON格式，直接返回原字符串（纯文本位置信息）
    return locationStr;
  } catch (error) {
    // 如果解析失败，直接返回原字符串
    console.warn('位置信息JSON解析失败:', error, '原始数据:', locationStr);
    return locationStr;
  }
};

// {{ AURA-X: Add - 监听发布成功事件，自动刷新列表. Confirmed via 寸止 }}
// 监听发布成功事件
const handleRefreshList = () => {
  console.log('DiaryIndex: 收到刷新事件，重新加载数据');
  loadDiaries(1, true);
};

// {{ AURA-X: Modify - 修改为按需加载，不在挂载时自动加载. Confirmed via 寸止. }}
// 组件挂载时不自动加载数据
onMounted(() => {
  console.log('DiaryIndex: 组件挂载，等待按需加载');

  // 监听发布成功事件
  uni.$on('refreshFeedList', handleRefreshList);
});

// {{ AURA-X: Add - 添加外部调用接口，供父组件按需加载数据. Confirmed via 寸止. }}
/**
 * 外部调用接口：加载日记数据
 * 供父组件在切换到日记tab时调用
 */
const loadDiaryData = () => {
  console.log('DiaryIndex loadDiaryData - 被父组件调用');
  loadDiaries();
};

// 暴露给父组件的方法
defineExpose({
  loadDiaryData
});

// 组件激活时也加载数据（用于tab切换场景）
onActivated(() => {
  console.log('DiaryIndex: 组件激活，重新加载数据');
  if (diaries.value.length === 0) {
    loadDiaries();
  }
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  uni.$off('refreshFeedList', handleRefreshList);
});
</script>

<template>
  <view class="diary-container">


    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <u-loading-icon mode="circle" size="30" color="#6AC086"></u-loading-icon>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 日记列表 -->
    <scroll-view 
      v-else
      class="diary-scroll"
      scroll-y
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="onLoadMore"
    >
      <!-- 空状态 -->
      <view v-if="diaries.length === 0" class="empty-container">
        <image src="/static/empty.png" class="empty-image" mode="aspectFit"></image>
        <text class="empty-text">还没有日记哦</text>
        <text class="empty-desc">记录生活的点点滴滴</text>
      </view>

      <!-- 日记列表 -->
      <view v-else class="diary-list">
        <view 
          v-for="diary in diaries" 
          :key="diary.id" 
          class="diary-item"
          @click="viewDiary(diary)"
        >
          <!-- 用户信息 -->
          <view class="diary-header">
            <image 
              :src="diary.user?.avatar_url || '/static/default-avatar.png'" 
              class="user-avatar"
              mode="aspectFill"
            ></image>
            <view class="user-info">
              <text class="user-nickname">{{ diary.user?.nickname || '匿名用户' }}</text>
              <text class="diary-time">{{ formatTime(diary.created_at) }}</text>
            </view>
            <!-- 私密标识 -->
            <view v-if="diary.privacy === 'private'" class="privacy-badge">
              <u-icon name="lock" size="12" color="#999"></u-icon>
              <text class="privacy-text">私密</text>
            </view>
          </view>

          <!-- 日记内容 -->
          <view class="diary-content">
            <text class="diary-text">{{ diary.content }}</text>
          </view>

          <!-- 图片展示 -->
          <view v-if="diary.images && diary.images.length > 0" class="diary-images">
            <image 
              v-for="(img, index) in diary.images.slice(0, 3)" 
              :key="index"
              :src="img"
              class="diary-image"
              mode="aspectFill"
            ></image>
            <view v-if="diary.images.length > 3" class="more-images">
              <text class="more-text">+{{ diary.images.length - 3 }}</text>
            </view>
          </view>

          <!-- 位置信息 -->
          <view v-if="diary.location" class="diary-location">
            <u-icon name="map" size="12" color="#999"></u-icon>
            <text class="location-text">{{ getLocationName(diary.location) }}</text>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="loadingMore" class="loading-more">
        <u-loading-icon mode="circle" size="20" color="#6AC086"></u-loading-icon>
        <text class="loading-more-text">加载更多...</text>
      </view>

      <!-- 没有更多 -->
      <view v-if="!hasMore && diaries.length > 0" class="no-more">
        <text class="no-more-text">没有更多了</text>
      </view>
    </scroll-view>
  </view>
</template>

<style lang="scss" scoped>
.diary-container {
  height: 100%;
  background-color: #f8f9fa;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  
  .loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #666;
  }
}

.diary-scroll {
  height: 100%;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  
  .empty-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 40rpx;
  }
  
  .empty-text {
    font-size: 32rpx;
    color: #333;
    margin-bottom: 16rpx;
  }
  
  .empty-desc {
    font-size: 28rpx;
    color: #999;
  }
}

.diary-list {
  padding: 20rpx;
}

.diary-item {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.diary-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  
  .user-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    margin-right: 24rpx;
  }
  
  .user-info {
    flex: 1;
    
    .user-nickname {
      display: block;
      font-size: 30rpx;
      font-weight: 500;
      color: #333;
      margin-bottom: 8rpx;
    }
    
    .diary-time {
      font-size: 24rpx;
      color: #999;
    }
  }
  
  .privacy-badge {
    display: flex;
    align-items: center;
    padding: 8rpx 16rpx;
    background: #f5f5f5;
    border-radius: 20rpx;
    
    .privacy-text {
      font-size: 20rpx;
      color: #999;
      margin-left: 8rpx;
    }
  }
}

.diary-content {
  margin-bottom: 24rpx;
  
  .diary-text {
    font-size: 30rpx;
    line-height: 1.6;
    color: #333;
  }
}

.diary-images {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 24rpx;
  
  .diary-image {
    width: 200rpx;
    height: 200rpx;
    border-radius: 12rpx;
  }
  
  .more-images {
    width: 200rpx;
    height: 200rpx;
    border-radius: 12rpx;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    
    .more-text {
      color: #fff;
      font-size: 28rpx;
      font-weight: 500;
    }
  }
}

.diary-location {
  display: flex;
  align-items: center;
  
  .location-text {
    font-size: 24rpx;
    color: #999;
    margin-left: 8rpx;
  }
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  
  .loading-more-text {
    margin-left: 16rpx;
    font-size: 28rpx;
    color: #666;
  }
}

.no-more {
  display: flex;
  justify-content: center;
  padding: 40rpx;
  
  .no-more-text {
    font-size: 28rpx;
    color: #999;
  }
}
</style>
