/* 设计令牌系统 */
:root {
  /* 颜色系统 */
  --color-primary: #6AC086;
  --color-background: #f8f9fa;
  --color-surface: #ffffff;
  --color-text-primary: #212529;
  --color-text-secondary: #6c757d;
  --color-border: #dee2e6;
  --color-shadow: rgba(0, 0, 0, 0.1);

  /* 间距系统 */
  --spacing-xs: 8rpx;
  --spacing-sm: 16rpx;
  --spacing-md: 20rpx;
  --spacing-lg: 24rpx;
  --spacing-xl: 30rpx;
  --spacing-xxl: 40rpx;

  /* 圆角系统 */
  --radius-sm: 8rpx;
  --radius-md: 16rpx;
  --radius-lg: 24rpx;
  --radius-xl: 32rpx;

  /* 字体系统 */
  --font-size-xs: 20rpx;
  --font-size-sm: 24rpx;
  --font-size-md: 28rpx;
  --font-size-lg: 32rpx;
  --font-size-xl: 36rpx;

  /* 阴影系统 */
  --shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

/* {{ AURA-X: Modify - 修复小程序不支持标签选择器问题，改为class选择器. Confirmed via 寸止. }} */
/* 全局样式 - 使用class选择器替代标签选择器 */
.uni-page-body,
.html-element,
.body-element {
  height: 100%;
}
.uni-page-body,
.html-element,
.body-element,
.page {
  margin: 0;
  padding: 0;
  background-color: #f8f9fa; /* 统一背景色 */
  min-height: 100vh;

  /* 修复小程序不支持标签选择器问题 */
  .u-image,
  .uni-image {
    width: 100%;
    height: 100%;
  }
}
/* 修复小程序不支持标签选择器问题 - 改为通用类选择器 */
.box-sizing-border {
  box-sizing: border-box;
  /* 字体栈已在App.vue中统一声明，此处移除避免冲突 */
}

/* 为常用uni-app组件添加box-sizing */
.uni-view,
.uni-image,
.uni-button,
.uni-icon,
.uni-text,
.uni-navigator {
  box-sizing: border-box;
}

// 定位
.pr {
  position: relative;
}

.pa {
  position: absolute;
}

.pfx {
  position: fixed !important;
}

.z20 {
  z-index: 20;
}

.top0 {
  top: 0;
}

.bottom0 {
  bottom: 0;
}

.bottom120 {
  bottom: 120rpx;
}

.left0 {
  left: 0;
}

.left50 {
  left: 50%;
}

.tl50 {
  transform: translate(-50%);
}

.right0 {
  right: 0rpx;
}

.right50 {
  right: 50rpx;
}

// flex布局
.df {
  display: flex;
}

.aic {
  align-items: center;
}

.ais {
  align-items: flex-start;
}

.aie {
  align-items: flex-end;
}

.fdc {
  flex-direction: column;
}

.fdr {
  flex-direction: row !important;
}

.jcsb {
  justify-content: space-between;
}

.jcsa {
  justify-content: space-around;
}

.jcl {
  justify-content: flex-start;
}
.jcc {
  justify-content: center;
}
.jcr {
  justify-content: flex-end;
}
.tac {
  text-align: center;
}
.tal {
  text-align: left;
}

.tar {
  text-align: right;
}

.fw {
  flex-wrap: wrap;
}

.f1 {
  flex: 1;
}
.fs {
  flex-shrink: 0;
}
// 居中及边距
.ma {
  margin: auto;
}
.m10 {
  margin: 10rpx;
}
.m20 {
  margin: 20rpx;
}
.m30 {
  margin: 30rpx;
}
.m40 {
  margin: 40rpx;
}
.m50 {
  margin: 50rpx;
}
.mt1 {
  margin-top: 1rpx;
}

.mt5 {
  margin-top: 5rpx;
}

.mt10 {
  margin-top: 10rpx;
}

.mt20 {
  margin-top: 20rpx;
}

.mt30 {
  margin-top: 30rpx;
}

.mt40 {
  margin-top: 40rpx;
}

.mt50 {
  margin-top: 50rpx;
}

.mt60 {
  margin-top: 60rpx;
}

.ml1 {
  margin-left: 1rpx;
}

.ml5 {
  margin-left: 5rpx;
}

.ml10 {
  margin-left: 10rpx;
}

.ml20 {
  margin-left: 20rpx;
}

.ml30 {
  margin-left: 30rpx;
}

.ml40 {
  margin-left: 40rpx;
}

.ml50 {
  margin-left: 50rpx;
}

.mr1 {
  margin-right: 1rpx;
}

.mr5 {
  margin-right: 5rpx;
}

.mr10 {
  margin-right: 10rpx;
}

.mr20 {
  margin-right: 20rpx;
}

.mr30 {
  margin-right: 30rpx;
}

.mr40 {
  margin-right: 40rpx;
}

.mr50 {
  margin-right: 50rpx;
}

.mb1 {
  margin-bottom: 1rpx;
}

.mb5 {
  margin-bottom: 5rpx;
}

.mb10 {
  margin-bottom: 10rpx;
}

.mb20 {
  margin-bottom: 20rpx;
}

.mb30 {
  margin-bottom: 30rpx;
}

.mb40 {
  margin-bottom: 40rpx;
}

.mb50 {
  margin-bottom: 50rpx;
}

.mx10 {
  margin: 0 10rpx;
}

.mx20 {
  margin: 0 20rpx;
}

.mx30 {
  margin: 0 30rpx;
}

.mx40 {
  margin: 0 40rpx;
}

.mx50 {
  margin: 0 50rpx;
}

.my10 {
  margin: 10rpx 0;
}

.my20 {
  margin: 20rpx 0;
}

.my30 {
  margin: 30rpx 0;
}

.my40 {
  margin: 40rpx 0;
}

.my50 {
  margin: 50rpx 0;
}

.px10 {
  padding: 0 10rpx;
}

.px20 {
  padding: 0 20rpx;
}

.px30 {
  padding: 0 30rpx;
}

.px40 {
  padding: 0 40rpx;
}

.px50 {
  padding: 0 50rpx;
}

.py10 {
  padding: 10rpx 0;
}

.py20 {
  padding: 20rpx 0;
}

.py30 {
  padding: 30rpx 0;
}

.py40 {
  padding: 40rpx 0;
}

.py50 {
  padding: 50rpx 0;
}

.pt1 {
  padding-top: 1rpx;
}

.pt5 {
  padding-top: 5rpx;
}

.pt10 {
  padding-top: 10rpx;
}

.pt20 {
  padding-top: 20rpx;
}

.pt30 {
  padding-top: 30rpx;
}

.pt40 {
  padding-top: 40rpx;
}

.pt50 {
  padding-top: 50rpx;
}

.pt60 {
  padding-top: 60rpx;
}

.pl1 {
  padding-left: 1rpx;
}

.pl5 {
  padding-left: 5rpx;
}

.pl10 {
  padding-left: 10rpx;
}

.pl20 {
  padding-left: 20rpx;
}

.pl30 {
  padding-left: 30rpx;
}

.pl40 {
  padding-left: 40rpx;
}

.pl50 {
  padding-left: 50rpx;
}

.pr1 {
  padding-right: 1rpx;
}

.pr5 {
  padding-right: 5rpx;
}

.pr10 {
  padding-right: 10rpx;
}

.pr20 {
  padding-right: 20rpx;
}

.pr30 {
  padding-right: 30rpx;
}

.pr40 {
  padding-right: 40rpx;
}

.pr50 {
  padding-right: 50rpx;
}

.pb1 {
  padding-bottom: 1rpx;
}

.pb5 {
  padding-bottom: 5rpx;
}

.pb10 {
  padding-bottom: 10rpx;
}

.pb20 {
  padding-bottom: 20rpx;
}

.pb30 {
  padding-bottom: 30rpx;
}

.pb40 {
  padding-bottom: 40rpx;
}

.pb50 {
  padding-bottom: 50rpx;
}
.p10 {
  padding: 10rpx;
}
.p20 {
  padding: 20rpx;
}
.p30 {
  padding: 30rpx;
}
.p40 {
  padding: 40rpx;
}
.p50 {
  padding: 50rpx;
}
// 宽高

.w {
  width: 100%;
}

.w20 {
  width: 20rpx;
}

.w30 {
  width: 30rpx;
}

.w50 {
  width: 50rpx;
}

.w88 {
  width: 88rpx;
}

.w100 {
  width: 100rpx;
}

.w150 {
  width: 150rpx;
}
.w200 {
  width: 200rpx;
}

.w690 {
  width: 690rpx;
}

.h {
  height: 100%;
}

.h5 {
  height: 5rpx;
}

.h25 {
  height: 25rpx;
}

.h30 {
  height: 30rpx;
}

.h50 {
  height: 50rpx;
}
.h88 {
  height: 88rpx;
}
.h100 {
  height: 100rpx;
}

.h150 {
  height: 150rpx;
}
.h200 {
  height: 200rpx;
}

// 颜色
.c63 {
  color: #333333;
}
.c6a {
  color: #aaaaaa;
}
.c6c {
  color: #ccc;
}
.c6f {
  color: #ffffff;
}
.c69 {
  color: #999999;
}
.c60 {
  color: #000000;
}
.call {
  color: #ff5700;
}
.b6f {
  background-color: #ffffff;
}
.b6e {
  background-color: #ffa0e8;
}
.ball {
  background: linear-gradient(142deg, #8efffe 0%, #c6e538 100%);
}
.fx {
  font-style: italic;
}
.fb {
  font-weight: bold;
}
// 圆角
.r5 {
  border-radius: 5rpx;
}

.r10 {
  border-radius: 10rpx;
}

.r12 {
  border-radius: 12rpx;
}

.r20 {
  border-radius: 20rpx;
}

.r30 {
  border-radius: 30rpx;
}

.r50p {
  border-radius: 50rpx;
}

.r50 {
  border-radius: 50%;
}

// 阴影
.bxsd {
  box-shadow: 0rpx 5rpx 20rpx rgba(0, 0, 0, 0.08);
}

// 卡片阴影
.judu-shadow-sm {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.judu-shadow-md {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.judu-shadow-lg {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}

.border {
  border: 2rpx solid #e0ddd3;
}

.borderBottom {
  border-bottom: 2rpx solid #e0ddd3;
}

// 分隔线
.judu-divider {
  height: 1px;
  background: linear-gradient(to right, transparent, #e0ddd3, transparent);
  margin: 16px 0;
}

// 溢出隐藏
.h1 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden; /*超出部分隐藏*/
  text-overflow: ellipsis; /* 超出部分显示省略号 */
  white-space: nowrap; /*规定段落中的文本不进行换行 */
}
.h2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  /*! autoprefixer: off */
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  word-break: break-all; /*追加这一行代码*/
}
.h3 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  word-break: break-all; /*追加这一行代码*/
}

.h4 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
  overflow: hidden;
  word-break: break-all; /*追加这一行代码*/
}

// 字体大小
.m {
  font-size: 24rpx;
  scale: 0.8;
  transform-origin: left center;
}

.x24 {
  font-size: 24rpx;
}

.x26 {
  font-size: 26rpx;
}

.x28 {
  font-size: 28rpx;
}

.x30 {
  font-size: 30rpx;
}

.x32 {
  font-size: 32rpx;
}

.x34 {
  font-size: 34rpx;
}

.x36 {
  font-size: 36rpx;
}

.x38 {
  font-size: 38rpx;
}

.x40 {
  font-size: 40rpx;
}

.x42 {
  font-size: 42rpx;
}

.x44 {
  font-size: 44rpx;
}

.x46 {
  font-size: 46rpx;
}

.x48 {
  font-size: 48rpx;
}

.x50 {
  font-size: 50rpx;
}

// 字体风格
.tc {
  text-decoration: line-through;
  background-color: transparent;
}

// 字体
.judu-font-serif {
  font-family: 'Noto Serif SC', 'Source Han Serif SC', 'Source Han Serif', serif;
}

.judu-font-sans {
  font-family: 'Noto Sans SC', 'Source Han Sans SC', 'Source Han Sans', sans-serif;
}

// 卡片
.judu-card {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s, box-shadow 0.3s;

  &:hover, &:active {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }
}

// 纸张纹理
.judu-paper {
  background-color: #ffffff;
  background-image: linear-gradient(to right, rgba(232, 230, 225, 0.1) 0%, rgba(232, 230, 225, 0.05) 50%, rgba(232, 230, 225, 0.1) 100%);
}

// 动画
.judu-fade-in {
  animation: juduFadeIn 0.3s ease forwards;
}

@keyframes juduFadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.judu-ink-spread {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background-color: #333;
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    opacity: 0.8;
    animation: juduInkSpread 0.5s ease-out forwards;
  }
}

@keyframes juduInkSpread {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0.8;
  }
  100% {
    transform: translate(-50%, -50%) scale(100);
    opacity: 0;
  }
}

/* {{ AURA-X: Modify - 修复小程序不支持标签选择器问题，改为class选择器. Confirmed via 寸止. }} */
.uni-button::after,
.button-element::after {
  border: none !important;
}

// 设置按钮
.btnp {
  padding: 0;
}

.btnm {
  margin: 0;
}

// 按钮
.judu-btn {
  background-color: #e8e6e1;
  color: #333333;
  border-radius: 4px;
  padding: 8px 16px;
  font-family: 'Noto Sans SC', 'Source Han Sans SC', 'Source Han Sans', sans-serif;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s;

  &:hover, &:active {
    background-color: #d9d4c5;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }
}

/* 统一加载状态样式 - 修复颜色和遮挡问题 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  background-color: #f8f9fa;
  min-height: 400rpx;
  position: relative;
  z-index: 1;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e5e5e5;
  border-top: 4rpx solid #88D7A0; /* 使用正确的绿色 */
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #88D7A0; /* 修复颜色问题 */
  text-align: center;
  line-height: 1.5;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 修复数据被遮挡的问题 */
.content-container {
  position: relative;
  z-index: 10;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.overlay-fix {
  position: relative;
  z-index: 100;
}

/* 确保重要内容不被遮挡 */
.important-content {
  position: relative;
  z-index: 1000;
}

/* 修复加载状态颜色问题 */
.loading-success {
  color: #88D7A0 !important;
}

.loading-error {
  color: #ff4757 !important;
}

.loading-warning {
  color: #ffa726 !important;
}
