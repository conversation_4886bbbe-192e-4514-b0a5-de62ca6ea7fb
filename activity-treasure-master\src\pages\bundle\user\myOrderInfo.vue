<script setup>
import { defineComponent, watch, ref, reactive } from "vue";
import {
  goodscancel_order,
  goodsyanchi_shouhuo,
  goodsqueren_shouhuo,
  payyue_pay,
  payweixin_pay,
  payget_weixinpay_sign,
} from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
  onHide,
  onUnload,
} from "@dcloudio/uni-app";
import { store } from "@/store";
import { navto, getListHeight, getItem, getDifferTime, pay, back } from "@/utils";

const gapHeight = ref(0);
const info = ref({});
const msg = ref();
const show = ref(false);
const actionsList = ref([
  {
    name: "余额支付",
    color: "#ffaa7f",
    fontSize: "18",
  },
  {
    name: "微信支付",
    color: "#ffaa7f",
    fontSize: "18",
  },
]);
const payQuery = ref({
  order_id: null,
  money: null,
});
const vip = ref(false);
let timer;

onLoad(async (e) => {
  if (e?.vip) vip.value = e.vip;
  info.value = store().$state.goodInfo;
  payQuery.value.order_id = info.value.order_id;
  payQuery.value.money = info.value.money;
  let title = "";
  switch (Number(info.value.status)) {
    case 0:
      title = "待付款";
      break;
    case 1:
      title = "待发货";
      break;
    case 2:
      title = "待收货";
      break;
    case 3:
      title = "已完成";
      break;
    case 4:
      title = "订单关闭";
      break;
    case 5:
      title = "退款中";
      break;
    case 6:
      title = "退款成功";
      break;
    case 7:
      title = "退款失败";
      break;
  }
  uni.setNavigationBarTitle({ title });
});
onShow(() => {
  // 清理之前的定时器，避免重复创建
  if (timer) {
    clearInterval(timer);
    timer = null;
  }

  if (info.value.status == 0) {
    let { d, h, m, s } = getDifferTime(
      vip.value
        ? new Date(info.value.create_time).setMinutes(
            new Date(info.value.create_time).getMinutes() +
              Number(store().$state.config.config.goods_nopay_cancel_minute.val)
          )
        : new Date(info.value.auto_cancel_time)
    );

    // 添加边界检查，避免无效计算
    if (m > 0 || s > 0) {
      timer = setInterval(() => {
        if (s <= 0) {
          s = 60;
          if (m > 0) {
            m--;
          } else {
            // 时间到了，清理定时器
            clearInterval(timer);
            timer = null;
            msg.value = "订单已超时";
            return;
          }
        } else {
          s--;
        }
        msg.value = `订单剩余${m}分${s}秒自动关闭`;
      }, 1000);
    } else {
      msg.value = "订单已超时";
    }
  } else if (info.value.status == 2) {
    let { d, h, m, s } = getDifferTime(new Date(info.value.auto_queren_time));
    msg.value = `还剩${d}天${h}时自动确认`;
  }
});
onReady(async () => {
  const res = await getListHeight("bottomBox");
  gapHeight.value = res.height;
});
onHide(() => {
  // 页面隐藏时清理定时器，释放内存
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
});
onUnload(() => {
  // 页面卸载时确保定时器被清理
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
});

const goNext = () => {
  let obj = {};
  for (let i in e.guige) {
    obj[i] = e.guige[i][0];
  }
  navto(`/pages/bundle/shop/goodInfo?id=${e.id}&guige=${JSON.stringify(obj)}`);
};
// 前置按钮
const delay = async () => {
  const api =
    info.value.status === 0
      ? vip.value
        ? dalibaocancel_order
        : goodscancel_order
      : goodsyanchi_shouhuo;
  const res = await api({ order_id: info.value.order_id });
  if (res.status === "ok")
    back({
      tip: `${info.value.status === 0 ? "取消" : "确认"}成功！即将返回上级页面`,
      time: 2000,
    });
  else uni.$u.toast(res.msg);
};
// 后置按钮
const sure = async () => {
  if (info.value.status === 0) show.value = true;
  else queren();
  // else if (info.value.status === 1) {
  //   uni.showModal({
  //     title: "提示",
  //     content: "当前订单还在待发货中，是否确认收货？",
  //     success: async (res) => {
  //       if (res.confirm) queren();
  //       else uni.$u.toast("您已取消");
  //     },
  //   });
  // }
};
const queren = async () => {
  const api = vip.value ? dalibaoqueren_shouhuo : goodsqueren_shouhuo;
  const res = await api({ order_id: info.value.order_id });
  if (res.status === "ok")
    back({
      tip: "确认成功！即将返回上级页面",
      time: 2000,
    });
  else uni.$u.toast(res.msg);
};
// 支付
const selectClick = async (e) => {
  let payRes;
  if (e.name === "余额支付") {
    payRes = await payyue_pay({ ...payQuery.value, type: vip.value ? 4 : 1 });
  } else {
    const wxRes = await payweixin_pay({ ...payQuery.value, type: vip.value ? 5 : 1 });
    if (wxRes.status === "ok") {
      const signRes = await payget_weixinpay_sign({ prepay_id: wxRes.prepay_id });
      payRes = await pay(signRes);
      if (payRes.errMsg === "requestPayment:ok") {
        payRes.status = "ok";
        payRes.msg = "支付成功";
      } else payRes.msg = "支付失败";
    }
  }
  if (payRes.status === "ok") back({ tip: "支付成功，2秒后返回上级页面", time: 2000 });
  else uni.$u.toast(payRes.msg);
};
</script>
<template>
  <view class="page" style="background-color: #f7f7f7">
    <view class="px40 pt30 pb30 df aic">
      <view class="f1">
        <u-text
          margin="0 0 20rpx 0"
          size="34rpx"
          color="#333"
          bold
          :text="
            getItem(
              [
                '等待买家付款',
                '等待卖家发货',
                '卖家已发货',
                '交易已完成',
                '交易关闭',
                '待退款',
                '退款成功',
                '退款失败',
              ],
              info.status
            )
          "
        ></u-text>
        <u-text
          size="22rpx"
          color="#333"
          :text="
            getItem(
              [
                msg,
                '正在打包发货中',
                msg,
                '交易成功',
                '交易关闭',
                '正在审核',
                '退款成功',
                '退款失败',
              ],
              info.status
            )
          "
        ></u-text>
      </view>
      <!-- <u-icon name="arrow-right" size="44rpx"></u-icon> -->
    </view>
    <view class="box" v-if="info.status === 0">
      <template v-if="info.status === 0">
        <view class="df aic p10">
          <view class="df fdc f1">
            <view class="df aic mb20">
              <view class="mr50">
                <u-text
                  color="#2B2B2B"
                  size="28rpx"
                  :text="info.addr_info.username"
                ></u-text>
              </view>
              <u-text color="#2B2B2B" size="28rpx" :text="info.addr_info.mobile"></u-text>
            </view>
            <u-text
              color="#7A7A7A"
              size="24rpx"
              :text="
                info.addr_info.sheng +
                info.addr_info.shi +
                info.addr_info.qu +
                info.addr_info.addr
              "
            ></u-text>
          </view>
          <u-icon name="arrow-right" size="44rpx" color="#9d9d9d"></u-icon>
        </view>
      </template>
      <!-- <template v-else>
        <u-steps
          :current="info.wuliu_info.length || 0"
          direction="column"
          dot
          active-color="#FF5102"
        >
          <template v-if="info.wuliu_info.time">
            <u-steps-item title="运输中" desc="11:40"></u-steps-item>
            <u-steps-item
              title="送至  河南省郑州市金水区东风路街道"
              desc="姓名 12345678910"
            ></u-steps-item>
          </template>
          <template v-else>
            <u-steps-item title="正在打包" :desc="info.create_time"></u-steps-item>
          </template>
        </u-steps>
      </template> -->
    </view>
    <view class="box">
      <u-text margin="0 0 30rpx 0" text="订单信息"></u-text>
      <template v-if="vip">
        <view class="df aic">
          <view>
            <u-text size="28rpx" text="商品名称："></u-text>
          </view>
          <u-text size="28rpx" :text="info.goods_name"></u-text>
          <view class="df aic">
            <u-text
              mode="price"
              color="#EF6227"
              size="24rpx"
              align="right"
              :text="info.money"
            ></u-text>
            <u-text size="18rpx" color="#AAAAAA" align="right" text="x1"></u-text>
          </view>
        </view>
        <view class="df ais">
          <u-text size="28rpx" text="包含内容："></u-text>
          <view class="df fdc">
            <u-text
              size="24rpx"
              align="right"
              v-for="(val, i) in info.goods_json"
              :key="i"
              :text="val"
            ></u-text>
          </view>
        </view>
      </template>
      <template v-else>
        <view
          class="df py10 borderBottom"
          v-for="(val, i) in info.goods_info"
          :key="i"
          @click="
            navto(
              `/pages/bundle/shop/goodInfo?id=${val.goods_id}&guige=${JSON.stringify(
                val.guige_info
              )}`
            )
          "
        >
          <u-image
            width="160rpx"
            height="160rpx"
            radius="10rpx"
            :src="val.img_url"
          ></u-image>
          <view class="ml10 df fdc jcsa f1">
            <view>
              <u-text size="28rpx" color="#2C2C2C" :text="val.goods_name"></u-text>
            </view>
            <view class="df aic">
              <view>
                <u-text size="22rpx" color="#AAAAAA" text="规格："></u-text>
              </view>
              <view v-for="(value, index) in val.guige_info" :key="index">
                <u-text
                  size="22rpx"
                  color="#AAAAAA"
                  :text="`${index}:${value};`"
                ></u-text>
              </view>
            </view>
            <view class="df aic jcsb">
              <u-text
                mode="price"
                color="#FF3333"
                size="28rpx"
                :text="`${$u.priceFormat(val.money, 2)}`"
              ></u-text>
              <u-text align="right" size="#FF3333" :text="`x${val.num}`"></u-text>
            </view>
          </view>
        </view>
        <view class="df aic jcr">
          <u-button
            v-if="
              info.status != 0 &&
              info.status != 3 &&
              info.status != 4 &&
              info.status != 5 &&
              info.status != 6 &&
              info.status != 7
            "
            shape="circle"
            plain
            :disabled="info.is_shenqing_tuikuan"
            :text="info.is_shenqing_tuikuan ? '退款中' : '申请退款'"
            :customStyle="{
              margin: '10rpx 0 0',
              width: '130rpx',
              height: '56rpx',
              fontSize: '24rpx',
            }"
            @click="navto(`/pages/bundle/user/returnGood?order_id=${info.order_id}`)"
          ></u-button>
        </view>
      </template>
    </view>
    <view class="box">
      <u-cell
        :border="false"
        title="商品："
        title-style="font-size:28rpx;color:#676767"
        :value="`共${vip ? 1 : info.goods_info?.length}件商品`"
      ></u-cell>
      <u-cell :border="false" title="合计：" title-style="font-size:28rpx;color:#676767">
        <template #value>
          <u-text
            align="right"
            size="28rpx"
            mode="price"
            color="#FF4444"
            :text="$u.priceFormat(vip ? info.money : info.order_money, 2)"
          ></u-text>
        </template>
      </u-cell>
      <u-cell
        v-if="info.status != 0 && info.status != 1"
        :border="false"
        title="物流单号："
        title-style="font-size:28rpx;color:#676767"
        :value="info.wuliu_danhao || '正在打包'"
      ></u-cell>
      <u-cell
        v-if="info.status != 0"
        :border="false"
        title="付款时间："
        title-style="font-size:28rpx;color:#676767"
        :value="info.pay_time"
      ></u-cell>
      <u-cell
        v-if="info.status != 0 && info.status != 1"
        :border="false"
        title="发货时间："
        title-style="font-size:28rpx;color:#676767"
        :value="info.fahuo_time || '正在打包'"
      ></u-cell>
      <u-cell
        v-if="info.status != 0"
        :border="false"
        title="订单编号："
        title-style="font-size:28rpx;color:#676767"
        :value="info.order_id"
      ></u-cell>
    </view>
    <u-gap :height="gapHeight"></u-gap>
    <view
      class="pfx bottom0 w b6f bottomBox"
      v-if="info.status == 0 || info.status == 2 || info.status == 3"
    >
      <view class="df aic jcr">
        <u-button
          v-if="info.status == 0 || info.status == 2"
          color="#AAAAAA"
          shape="circle"
          plain
          :text="info.status === 0 ? '取消订单' : '延长收货'"
          :customStyle="{
            margin: '0 30rpx',
            width: '190rpx',
            height: '68rpx',
            color: '#000',
            fontSize: '34rpx',
          }"
          @click="delay"
        ></u-button>
        <u-button
          v-if="info.is_pingjia != 1 && info.status == 3"
          color="#AAAAAA"
          shape="circle"
          plain
          text="去评价"
          :customStyle="{
            margin: '0 30rpx',
            width: '190rpx',
            height: '68rpx',
            color: '#000',
            fontSize: '34rpx',
          }"
          @click="store().setGoodInfo(info, true)"
        ></u-button>
        <u-button
          v-if="info.status === 0 || info.status === 2"
          color="#FA8700"
          shape="circle"
          plain
          :text="info.status === 0 ? '继续付款' : '确认收货'"
          :customStyle="{
            margin: '0 30rpx 0 0',
            width: '190rpx',
            height: '68rpx',
            color: '#FA8700',
            fontSize: '34rpx',
          }"
          @click="sure"
        ></u-button>
      </view>
      <u-safe-bottom></u-safe-bottom>
    </view>
    <u-action-sheet
      title="支付方式选择"
      :close-on-click-action="true"
      :actions="actionsList"
      :show="show"
      @close="show = false"
      @select="selectClick"
    ></u-action-sheet>
  </view>
</template>

<style lang="less">
.page {
  .box {
    margin: 10rpx 10rpx;
    padding: 20rpx;
    background-color: #fff;
    border-radius: 20rpx;
  }
  .box::v-deep .u-cell__body {
    padding: 0;
  }
  .data-v-0b01d81e::v-deep .u-cell__value {
    padding: 0;
    color: #333 !important;
  }
  .bottomBox {
    padding-top: 14rpx;
    box-shadow: 0rpx -1rpx 0rpx 0rpx #eeeeee;
  }
}
</style>
