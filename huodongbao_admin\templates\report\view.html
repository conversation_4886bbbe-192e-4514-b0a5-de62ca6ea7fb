<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>举报详情</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/access/asset/layui/css/layui.css">
    <style>
        .info-item { margin-bottom: 15px; }
        .info-label { font-weight: bold; color: #666; }
        .evidence-img { max-width: 200px; max-height: 200px; margin: 5px; cursor: pointer; }
        .status-pending { color: #FF5722; }
        .status-processed { color: #009688; }
        .status-rejected { color: #607D8B; }
    </style>
</head>
<body>
    <div class="layui-container" style="margin-top: 20px;">
        <div class="layui-row">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <h2>举报详情 #{{$report.id}}</h2>
                        <div class="layui-btn-group">
                            <a href="?c=Report&m=index" class="layui-btn layui-btn-primary">返回列表</a>
                            {if $report.status == 'pending'}
                                <button class="layui-btn layui-btn-normal" onclick="processReport({$report.id}, 'approve')">批准举报</button>
                                <button class="layui-btn layui-btn-danger" onclick="processReport({$report.id}, 'reject')">拒绝举报</button>
                            {/if}
                        </div>
                    </div>
                    
                    <div class="layui-card-body">
                        <div class="layui-row layui-col-space20">
                            <!-- 基本信息 -->
                            <div class="layui-col-md6">
                                <div class="layui-card">
                                    <div class="layui-card-header">基本信息</div>
                                    <div class="layui-card-body">
                                        <div class="info-item">
                                            <div class="info-label">举报状态：</div>
                                            {if $report.status == 'pending'}
                                                <span class="layui-badge layui-bg-orange">待处理</span>
                                            {elseif $report.status == 'processed'}
                                                <span class="layui-badge layui-bg-green">已处理</span>
                                            {elseif $report.status == 'rejected'}
                                                <span class="layui-badge layui-bg-gray">已拒绝</span>
                                            {/if}
                                        </div>
                                        
                                        <div class="info-item">
                                            <div class="info-label">举报时间：</div>
                                            <div>{$report.created_at}</div>
                                        </div>
                                        
                                        {if $report.processed_at}
                                        <div class="info-item">
                                            <div class="info-label">处理时间：</div>
                                            <div>{$report.processed_at}</div>
                                        </div>
                                        {/if}
                                        
                                        <div class="info-item">
                                            <div class="info-label">举报理由：</div>
                                            <div>{$report.report_reason}</div>
                                            {if $report.report_description}
                                                <div class="layui-text-muted">{$report.report_description}</div>
                                            {/if}
                                        </div>
                                        
                                        <div class="info-item">
                                            <div class="info-label">惩罚类型：</div>
                                            <div>
                                                {if $report.penalty_type == 'points'}
                                                    <span class="layui-badge layui-bg-red">扣分 {$report.penalty_value}</span>
                                                {elseif $report.penalty_type == 'freeze'}
                                                    <span class="layui-badge layui-bg-blue">冻结 {$report.penalty_value} 天</span>
                                                {/if}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 人员信息 -->
                            <div class="layui-col-md6">
                                <div class="layui-card">
                                    <div class="layui-card-header">人员信息</div>
                                    <div class="layui-card-body">
                                        <div class="info-item">
                                            <div class="info-label">举报人：</div>
                                            <div>
                                                <a href="?c=User&m=edit&uid={$report.reporter_uid}" target="_blank">
                                                    {$report.reporter_nickname}
                                                </a>
                                                <span class="layui-text-muted">(ID: {$report.reporter_uid})</span>
                                            </div>
                                            {if $report.reporter_mobile}
                                                <div class="layui-text-muted">手机：{$report.reporter_mobile}</div>
                                            {/if}
                                        </div>
                                        
                                        <div class="info-item">
                                            <div class="info-label">被举报人：</div>
                                            <div>
                                                <a href="?c=User&m=edit&uid={$report.reported_uid}" target="_blank">
                                                    {$report.reported_nickname}
                                                </a>
                                                <span class="layui-text-muted">(ID: {$report.reported_uid})</span>
                                            </div>
                                            {if $report.reported_mobile}
                                                <div class="layui-text-muted">手机：{$report.reported_mobile}</div>
                                            {/if}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 活动信息 -->
                        <div class="layui-card" style="margin-top: 20px;">
                            <div class="layui-card-header">相关活动</div>
                            <div class="layui-card-body">
                                <div class="info-item">
                                    <div class="info-label">活动名称：</div>
                                    <div>
                                        <a href="?c=Huodong&m=edit&id={$report.activity_id}" target="_blank">
                                            {$report.activity_name}
                                        </a>
                                        <span class="layui-text-muted">(ID: {$report.activity_id})</span>
                                    </div>
                                </div>
                                
                                {if $report.activity_start_time}
                                <div class="info-item">
                                    <div class="info-label">活动时间：</div>
                                    <div>{$report.activity_start_time}</div>
                                </div>
                                {/if}
                            </div>
                        </div>
                        
                        <!-- 举报详情 -->
                        <div class="layui-card" style="margin-top: 20px;">
                            <div class="layui-card-header">举报详情</div>
                            <div class="layui-card-body">
                                {if $report.report_detail}
                                <div class="info-item">
                                    <div class="info-label">详细描述：</div>
                                    <div style="white-space: pre-wrap; background: #f8f8f8; padding: 10px; border-radius: 4px;">
                                        {$report.report_detail}
                                    </div>
                                </div>
                                {/if}
                                
                                {if $report.evidence_images}
                                <div class="info-item">
                                    <div class="info-label">证据图片：</div>
                                    <div>
                                        {foreach $report.evidence_images as $img}
                                            <img src="{$img}" class="evidence-img" onclick="viewImage('{$img}')">
                                        {/foreach}
                                    </div>
                                </div>
                                {/if}
                            </div>
                        </div>
                        
                        <!-- 管理员备注 -->
                        {if $report.admin_note}
                        <div class="layui-card" style="margin-top: 20px;">
                            <div class="layui-card-header">管理员备注</div>
                            <div class="layui-card-body">
                                <div style="white-space: pre-wrap; background: #f0f9ff; padding: 10px; border-radius: 4px; border-left: 4px solid #1890ff;">
                                    {$report.admin_note}
                                </div>
                            </div>
                        </div>
                        {/if}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/access/asset/layui/layui.js"></script>
    <script>
        layui.use(['layer'], function(){
            var layer = layui.layer;
            
            // 查看图片
            window.viewImage = function(src) {
                layer.photos({
                    photos: {
                        data: [{src: src}]
                    },
                    anim: 5
                });
            };
            
            // 处理举报
            window.processReport = function(id, action) {
                var title = action === 'approve' ? '批准举报' : '拒绝举报';
                var content = '<div style="padding: 20px;">' +
                    '<div class="layui-form-item">' +
                    '<label class="layui-form-label">处理备注：</label>' +
                    '<div class="layui-input-block">' +
                    '<textarea id="admin_note" placeholder="请输入处理备注" class="layui-textarea"></textarea>' +
                    '</div>' +
                    '</div>' +
                    '</div>';
                
                layer.open({
                    type: 1,
                    title: title,
                    content: content,
                    area: ['500px', '300px'],
                    btn: ['确定', '取消'],
                    yes: function(index) {
                        var admin_note = document.getElementById('admin_note').value;
                        
                        // 提交处理
                        var form = document.createElement('form');
                        form.method = 'POST';
                        form.action = '?c=Report&m=process';
                        
                        var fields = {
                            'id': id,
                            'action': action,
                            'admin_note': admin_note
                        };
                        
                        for (var key in fields) {
                            var input = document.createElement('input');
                            input.type = 'hidden';
                            input.name = key;
                            input.value = fields[key];
                            form.appendChild(input);
                        }
                        
                        document.body.appendChild(form);
                        form.submit();
                        
                        layer.close(index);
                    }
                });
            };
        });
    </script>
</body>
</html>
