<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { searchSources, createSource } from '@/api/index.js';
import { store } from '@/store';
import customNavbar from '@/components/customNavbar.vue';

// {{ AURA-X: Add - 创建出处搜索选择页面. Confirmed via 寸止 }}
const searchKeyword = ref('');
const sourceList = ref([]);
const isLoading = ref(false);
const isLoadingMore = ref(false);
const hasMore = ref(true);
const currentPage = ref(1);
const pageSize = 20;

// 获取页面参数
const pages = getCurrentPages();
const currentPageInstance = pages[pages.length - 1];
const isSelectMode = currentPageInstance.options.type === 'select';

// 搜索出处
const searchSourceList = async (isLoadMore = false) => {
  if (isLoading.value || (isLoadMore && isLoadingMore.value)) return;
  
  if (isLoadMore) {
    isLoadingMore.value = true;
  } else {
    isLoading.value = true;
    currentPage.value = 1;
    sourceList.value = [];
    hasMore.value = true;
  }
  
  try {
    const response = await searchSources({
      uid: store.userInfo.uid,
      token: store.userInfo.token,
      keyword: searchKeyword.value,
      page: currentPage.value,
      page_size: pageSize
    });
    
    if (response.status === 'ok') {
      const newSources = response.data.sources || [];
      
      if (isLoadMore) {
        sourceList.value = [...sourceList.value, ...newSources];
      } else {
        sourceList.value = newSources;
      }
      
      hasMore.value = newSources.length === pageSize;
      if (hasMore.value) {
        currentPage.value++;
      }
    } else {
      uni.showToast({ title: response.msg || '搜索失败', icon: 'none' });
    }
  } catch (error) {
    console.error('搜索出处失败:', error);
    uni.showToast({ title: '搜索失败，请稍后重试', icon: 'none' });
  } finally {
    isLoading.value = false;
    isLoadingMore.value = false;
  }
};

// 选择出处
const selectSource = (source) => {
  if (isSelectMode) {
    // 发送选择结果给父页面
    uni.$emit('sourceSelected', source);
    uni.navigateBack();
  } else {
    // 跳转到出处详情页
    uni.navigateTo({
      url: `/pages/bundle/world/source/detail?id=${source.id}`
    });
  }
};

// 创建新出处
const createNewSource = () => {
  if (isSelectMode) {
    uni.navigateTo({
      url: `/pages/bundle/world/source/create?type=select&keyword=${encodeURIComponent(searchKeyword.value)}`
    });
  } else {
    uni.navigateTo({
      url: '/pages/bundle/world/source/create'
    });
  }
};

// 搜索防抖
let searchTimer = null;
const handleSearch = () => {
  clearTimeout(searchTimer);
  searchTimer = setTimeout(() => {
    searchSourceList();
  }, 500);
};

// 加载更多
const loadMore = () => {
  if (hasMore.value && !isLoadingMore.value) {
    searchSourceList(true);
  }
};

// 页面加载
onMounted(() => {
  searchSourceList();
});

// 监听创建出处成功事件
uni.$on('sourceCreated', (newSource) => {
  if (isSelectMode) {
    selectSource(newSource);
  } else {
    // 刷新列表
    searchSourceList();
  }
});

// 组件销毁时移除事件监听
onUnmounted(() => {
  uni.$off('sourceCreated');
});
</script>

<template>
  <view class="source-search-page">
    <!-- 统一导航栏 -->
    <customNavbar 
      :title="isSelectMode ? '选择出处' : '搜索出处'" 
      backIcon="arrow-left"
    />
    
    <!-- 搜索框 -->
    <view class="search-section">
      <view class="search-box">
        <u-icon name="search" size="20" color="#999" class="search-icon"></u-icon>
        <input 
          v-model="searchKeyword" 
          @input="handleSearch"
          placeholder="搜索书名、期刊、网站..."
          class="search-input"
          confirm-type="search"
          @confirm="searchSourceList"
        />
        <view v-if="searchKeyword" @click="searchKeyword = ''; searchSourceList()" class="clear-btn">
          <u-icon name="close-circle-fill" size="18" color="#ccc"></u-icon>
        </view>
      </view>
    </view>
    
    <!-- 出处列表 -->
    <view class="source-list">
      <view v-if="isLoading && sourceList.length === 0" class="loading-wrapper">
        <u-loading-icon mode="spinner" color="#6AC086" size="40"></u-loading-icon>
        <text class="loading-text">搜索中...</text>
      </view>
      
      <view v-else-if="sourceList.length === 0 && !isLoading" class="empty-wrapper">
        <u-icon name="bookmark" size="80" color="#ddd"></u-icon>
        <text class="empty-text">暂无相关出处</text>
        <view class="create-btn" @click="createNewSource">
          <u-icon name="plus" size="16" color="#6AC086" style="margin-right: 8rpx;"></u-icon>
          <text class="create-text">创建新出处</text>
        </view>
      </view>
      
      <view v-else>
        <view 
          v-for="source in sourceList" 
          :key="source.id"
          class="source-item"
          @click="selectSource(source)"
        >
          <image 
            v-if="source.cover_image" 
            :src="source.cover_image" 
            class="cover"
            mode="aspectFill"
          ></image>
          <view v-else class="cover-placeholder">
            <u-icon name="bookmark" size="32" color="#6AC086"></u-icon>
          </view>
          
          <view class="source-info">
            <text class="name">{{ source.name }}</text>
            <text v-if="source.publisher" class="publisher">{{ source.publisher }}</text>
            <text v-else-if="source.category" class="category">{{ source.category }}</text>
            <text v-if="source.description" class="description">{{ source.description }}</text>
            <view class="stats">
              <text class="quote-count">{{ source.quote_count || 0 }} 条摘录</text>
            </view>
          </view>
          
          <u-icon name="arrow-right" size="16" color="#ccc" class="arrow"></u-icon>
        </view>
        
        <!-- 加载更多 -->
        <view v-if="hasMore" class="load-more" @click="loadMore">
          <u-loading-icon v-if="isLoadingMore" mode="spinner" color="#6AC086" size="20"></u-loading-icon>
          <text class="load-more-text">{{ isLoadingMore ? '加载中...' : '加载更多' }}</text>
        </view>
        
        <!-- 创建新出处按钮 -->
        <view class="create-new-section">
          <view class="create-btn" @click="createNewSource">
            <u-icon name="plus" size="16" color="#6AC086" style="margin-right: 8rpx;"></u-icon>
            <text class="create-text">创建新出处</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.source-search-page {
  min-height: 100vh;
  background-color: #f8f9fa;
  
  .search-section {
    padding: 32rpx;
    background-color: white;
    border-bottom: 1rpx solid #f0f0f0;
    
    .search-box {
      display: flex;
      align-items: center;
      background-color: #f8f9fa;
      border-radius: 50rpx;
      padding: 24rpx 32rpx;
      
      .search-icon {
        margin-right: 16rpx;
        flex-shrink: 0;
      }
      
      .search-input {
        flex: 1;
        font-size: 32rpx;
        color: #333;
        
        &::placeholder {
          color: #999;
        }
      }
      
      .clear-btn {
        margin-left: 16rpx;
        flex-shrink: 0;
      }
    }
  }
  
  .source-list {
    flex: 1;
    
    .loading-wrapper,
    .empty-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 120rpx 32rpx;
      
      .loading-text,
      .empty-text {
        margin-top: 32rpx;
        font-size: 28rpx;
        color: #999;
      }
      
      .create-btn {
        margin-top: 48rpx;
        display: flex;
        align-items: center;
        padding: 24rpx 48rpx;
        background-color: #6AC086;
        border-radius: 50rpx;
        
        .create-text {
          font-size: 28rpx;
          color: white;
          font-weight: 500;
        }
      }
    }
    
    .source-item {
      display: flex;
      align-items: center;
      padding: 32rpx;
      background-color: white;
      border-bottom: 1rpx solid #f0f0f0;
      min-height: 88rpx;
      
      &:active {
        background-color: #f8f9fa;
      }
      
      .cover {
        width: 80rpx;
        height: 100rpx;
        border-radius: 8rpx;
        margin-right: 32rpx;
        flex-shrink: 0;
        background-color: #f5f5f5;
      }
      
      .cover-placeholder {
        width: 80rpx;
        height: 100rpx;
        border-radius: 8rpx;
        background-color: #f8f9fa;
        border: 2rpx solid #6AC086;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 32rpx;
        flex-shrink: 0;
      }
      
      .source-info {
        flex: 1;
        
        .name {
          display: block;
          font-size: 32rpx;
          color: #333;
          font-weight: 500;
          line-height: 1.4;
          margin-bottom: 8rpx;
        }
        
        .publisher,
        .category {
          display: block;
          font-size: 24rpx;
          color: #6AC086;
          margin-bottom: 8rpx;
        }
        
        .description {
          display: block;
          font-size: 24rpx;
          color: #666;
          line-height: 1.4;
          margin-bottom: 8rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        
        .stats {
          .quote-count {
            font-size: 24rpx;
            color: #999;
          }
        }
      }
      
      .arrow {
        margin-left: 16rpx;
        flex-shrink: 0;
      }
    }
    
    .load-more {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 48rpx 32rpx;
      background-color: white;
      border-bottom: 1rpx solid #f0f0f0;
      
      .load-more-text {
        margin-left: 16rpx;
        font-size: 28rpx;
        color: #666;
      }
    }
    
    .create-new-section {
      padding: 48rpx 32rpx;
      background-color: white;
      display: flex;
      justify-content: center;
      
      .create-btn {
        display: flex;
        align-items: center;
        padding: 24rpx 48rpx;
        background-color: #6AC086;
        border-radius: 50rpx;
        
        .create-text {
          font-size: 28rpx;
          color: white;
          font-weight: 500;
        }
      }
    }
  }
}
</style>
