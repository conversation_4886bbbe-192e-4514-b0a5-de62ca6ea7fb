<script setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import {
  lunbotuindex,
  huodongget_type,
  huodongget_list,
  getAddr
} from "@/api";

import {
  onLoad,
  onShow,
  onHide,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
  onShareAppMessage,
  onShareTimeline,
  onUnload
} from "@dcloudio/uni-app";
import { store } from "@/store";
import { navto, pxToRpx } from "@/utils";
import MyTitle from "../components/myTitle.vue";
import CustomTabBar from "../components/CustomTabBar.vue";
import china_area from "@/utils/china.js";

const isMounted = ref(false);
const label = ref("全城"); // 设置默认城市显示
const info = ref({});
const tabList = ref([
  // { name: "分会活动" },
  { 
    name: "全部活动",
    icon: "/static/quanbuhuodong.svg"
  },
  { 
    name: "线上活动",
    icon: "/static/xianshanghuodong.svg"
  },
  { 
    name: "历史活动",
    icon: "/static/lishihuodong.svg"
  }
  // { name: "会员中心" },
]);
const dayList = ref([]); // 日期列表
const dayItem = ref(0); // 日期选中项
const nearly = ref(true); // 是否近距离
const pickerShow = ref(false); // 选择器是否显示
const columns = ref([[]]); // 选择器的数据
const list = ref([]);
const form = reactive({
  page: 1,
  page_size: 20,
  type_id: 0,
  is_tuijian: 1,
  keyword: "",
  shi_id: "",
  qu_id: "",
  sort: 1,
  lng: "",
  lat: "",
  huodong_date: "",
  list_type: 1 // 修改这里：使用list_type替代is_online和is_history
});
const stop = ref(true);
const scrollTop = ref(0);
const place = ref("全城");
const iptWidth = ref("500rpx"); // 设置默认宽度，确保搜索栏正常显示
const tabIndex = ref(0);
const sort = ref("时间优先");
const sortColumns = ref([["时间优先", "距离优先"]]);
const pickerShows = ref(false);
const loading = ref(false);
const finished = ref(false); // 添加finished变量声明
const isPageActive = ref(true); // 添加页面激活状态标志

onShareTimeline(() => {
  return {
    title: store().$state.config.config?.app_name?.val,
    imageUrl: store().$state.config.img_config?.app_logo?.val,
    path: `/pages/index?pid=${store().$state.userInfo?.uid}`
  };
});
onShareAppMessage(() => {
  return {
    title: store().$state.config.config?.app_name?.val,
    imageUrl: store().$state.config.img_config?.app_logo?.val,
    path: `/pages/index?pid=${store().$state.userInfo?.uid}`
  };
});
onPageScroll((e) => {
    if (!isPageActive.value) return; // 检查页面是否激活
    if (scrollTop && typeof scrollTop.value !== 'undefined') {
      scrollTop.value = e.scrollTop;
    }
});
onPullDownRefresh(() => {
    if (!isMounted.value) return;
    // 下拉刷新时直接获取位置，不使用默认位置
    getCurrentLocation();
});
onReachBottom(() => {
    if (!isMounted.value) return;
    if (stop && stop.value) {
        form.page++;
        getList();
    } else uni.$u.toast("暂无更多");
});

onLoad(async (e) => {
    if (e?.pid) store().changePid(e.pid);
    try {
        const activeRes = await huodongget_type();
        if(info) info.value.active = activeRes.data;
        store().changeActiveTypeList(activeRes.data);
        getWeek(30);
        // 优化搜索栏宽度计算，添加错误处理
        try {
            const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
            if (menuButtonInfo && menuButtonInfo.left > 0) {
                const calculatedWidth = pxToRpx(menuButtonInfo.left - 39);
                if (calculatedWidth > 200) { // 确保宽度合理
                    iptWidth.value = calculatedWidth + "rpx";
                } else {
                    iptWidth.value = "500rpx"; // fallback 宽度
                }
            }
        } catch (error) {
            console.warn('搜索栏宽度计算失败，使用默认宽度:', error);
            iptWidth.value = "500rpx";
        }
    } catch (error) {
    }
});
onHide(() => {
  isPageActive.value = false;
});
onShow(async () => {
    isPageActive.value = true; // Set page as active

    // 确保搜索栏宽度正确初始化
    if (iptWidth.value === "500rpx") {
        try {
            const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
            if (menuButtonInfo && menuButtonInfo.left > 0) {
                const calculatedWidth = pxToRpx(menuButtonInfo.left - 39);
                if (calculatedWidth > 200) {
                    iptWidth.value = calculatedWidth + "rpx";
                }
            }
        } catch (error) {
            console.warn('onShow中搜索栏宽度计算失败:', error);
        }
    }

    // {{ AURA-X: Add - 恢复已选择的城市状态. Confirmed via 寸止. }}
    // 检查并恢复已选择的城市状态
    try {
        const selectedCity = uni.getStorageSync('selectedCity');
        if (selectedCity && selectedCity.adcode && selectedCity.name) {
            // 只有在当前没有设置城市或城市不匹配时才更新
            if (!form.shi_id || form.shi_id !== selectedCity.adcode) {
                form.shi_id = selectedCity.adcode;
                label.value = selectedCity.name;
                console.log('恢复已选择的城市:', selectedCity.name, 'adcode:', selectedCity.adcode);
            }
        }
    } catch (error) {
        console.warn('恢复城市状态失败:', error);
    }

    // 获取轮播图数据 - 参考成功实现的简化方式
    const lunbotuRes = await lunbotuindex({ type: 1 });
    info.value.lunbotu = lunbotuRes?.data || [];
    console.log('轮播图加载完成，数量:', info.value.lunbotu.length);

    // 获取活动类型数据（如果还没有）
    if (!info.value?.active || !info.value.active.length) {
        try {
            const activeRes = await huodongget_type();
            if (activeRes?.status === 'ok') {
                info.value.active = activeRes.data;
                store().changeActiveTypeList(activeRes.data);
            }
        } catch (error) {
            console.warn('获取活动类型失败:', error);
        }
    }

    // 如果没有活动列表数据，开始获取位置和活动列表
    if (!list.value?.length) {
        +store().$state.config.config.is_shenhe.val
            ? getCurrentLocation()
            : getAddress({
                location: "116.39747,39.908823",
                lng: "116.39747",
                lat: "39.908823"
            });
    }
});
onMounted(() => {
    isMounted.value = true;
    // 页面加载完成后立即开始获取位置和活动列表
    getCurrentLocation();

    // {{ AURA-X: Add - 监听城市选择事件. Confirmed via 寸止. }}
    // 监听城市选择事件
    uni.$on('citySelected', handleCitySelected);
});
onUnload(() => {
    isMounted.value = false;
    // 移除事件监听
    uni.$off('citySelected', handleCitySelected);
});

onShow(() => {
    // 检查是否有选择的城市
    const selectedCity = uni.getStorageSync('selectedCity');
    if (selectedCity && selectedCity.adcode) {
        handleCitySelected(selectedCity);
        // 清除存储的城市信息，避免重复处理
        uni.removeStorageSync('selectedCity');
    }
});

// 异步获取位置信息（不阻塞主流程）
const getLocationAsync = () => {
  return new Promise((resolve) => {
    if (!isMounted.value) {
      resolve({ success: false, reason: '页面未挂载' });
      return;
    }

    if (form.shi_id) {
      resolve({ success: true, reason: '已有城市ID' });
      return;
    }

    // 异步获取位置，不阻塞主流程
    setTimeout(() => {
      getCurrentLocation();
      resolve({ success: true, reason: '位置获取已启动' });
    }, 0);
  });
};

// 检测是否有定位权限
// {{ AURA-X: Modify - 修改城市选择功能，跳转到城市选择页面. Confirmed via 寸止. }}
// 城市选择功能 - 跳转到城市选择页面
const getLonLat = () => {
  if (!isMounted.value) return;

  // 跳转到城市选择页面
  uni.navigateTo({
    url: '/pages/city-select'
  });
};

// {{ AURA-X: Modify - 优化城市选择事件处理，确保状态一致性. Confirmed via 寸止. }}
// 处理城市选择事件
const handleCitySelected = (city) => {
  if (!isMounted.value || !city) return;

  // 更新城市显示
  if (label) label.value = city.name;

  // 更新表单数据
  form.shi_id = city.adcode;
  form.lng = "";
  form.lat = "";

  // 如果当前在线上活动或历史活动tab，切换回全部活动tab
  if (tabIndex.value === 1 || tabIndex.value === 2) {
    tabIndex.value = 0;
    form.list_type = 1;
    console.log('城市选择后自动切换到全部活动tab');
  }

  // 重新加载活动列表
  resetList();

  console.log('城市已切换:', city.name, 'adcode:', city.adcode);
};

// 检测是否有定位权限（重命名为获取当前位置）
const getCurrentLocation = () => {
  if (!isMounted.value) return;

  if (form.shi_id) {
    resetList();
  } else {
    uni.getSetting({
      success(res) {
        if (!res.authSetting["scope.userLocation"]) {
          uni.authorize({
            scope: "scope.userLocation",
            success() {
              getLocation();
            },
            fail(error) {
              uni.showModal({
                title: "提示",
                content: "若点击不授权，将无法使用位置功能",
                cancelText: "不授权",
                cancelColor: "#999",
                confirmText: "授权",
                confirmColor: "#f94218",
                success(res) {
                  if (res.confirm) {
                    uni.openSetting({
                      success: (res) => {
                        getLocation();
                      },
                      fail: (err) => {
                        getLocation();
                      }
                    });
                  } else if (res.cancel) {
                    getLocation();
                  }
                }
              });
            }
          });
        } else {
          getLocation();
        }
      },
      fail(error) {
        console.error('获取设置失败:', error);
        getLocation();
      }
    });
  }
};
// 获取经纬度
const getLocation = async () => {
  if (!isMounted.value) return;

  uni.getLocation({
    type: "wgs84",
    isHighAccuracy: true,
    success: async (res) => {
      if (isMounted.value) {
        const locationParam = {
          location: `${res.longitude},${res.latitude}`,
          lng: res.longitude.toString(),
          lat: res.latitude.toString()
        };
        await getAddress(locationParam);
      }
    },
    fail: async (error) => {
      console.error('获取位置失败:', error);
      if (isMounted.value) {
        uni.$u.toast("获取位置失败，请检查定位权限设置");
        // 不使用默认位置，直接设置为全城显示所有活动
        form.lng = "";
        form.lat = "";
        form.shi_id = "";
        if(label) label.value = "全城";
        // 直接加载活动列表
        resetList();
      }
    }
  });
};
// 根据经纬度获取地址
const getAddress = async (e) => {
  if (!isMounted.value) return;

  // 设置经纬度
  form.lng = e.lng;
  form.lat = e.lat;

  try {
    const addrRes = await getAddr(e);

    if (addrRes && addrRes.status == 1) {
      const add = addrRes.regeocode.addressComponent;
      if (!isMounted.value || !label) return;

      const cityName = uni.$u.test.isEmpty(add.city) ? add.province : add.city;
      if(label) label.value = cityName;

      // {{ AURA-X: Modify - 统一使用市级adcode格式，包括直辖市. Confirmed via 寸止. }}
      // 统一使用市级adcode格式，确保与数据库和城市选择页面一致
      if (add.adcode && add.adcode.length >= 4) {
        const provinceCode = add.adcode.substring(0, 2) + '0000';
        // 直辖市映射到对应的市级adcode
        const municipalityMap = {
          '110000': '110100', // 北京市 → 北京城区
          '120000': '120100', // 天津市 → 天津城区
          '310000': '310100', // 上海市 → 上海城区
          '500000': '500100'  // 重庆市 → 重庆城区
        };

        if (municipalityMap[provinceCode]) {
          // 直辖市使用对应的市级adcode
          form.shi_id = municipalityMap[provinceCode];
        } else {
          // 其他城市使用市级adcode（前4位+00）
          form.shi_id = add.adcode.substring(0, 4) + '00';
        }
        console.log('成功设置城市adcode:', form.shi_id, '城市名称:', cityName, '原始adcode:', add.adcode);

        if(!isMounted.value || !columns || !columns.value) return;
        china_area.forEach((val) =>
            val.children.findIndex((value) =>
                value.id === `${form.shi_id}`
                    ? (columns.value[0] = [{name: "全城", id: 0}, ...value.children])
                    : ""
            )
        );
      } else {
        console.log('未获取到adcode，使用默认设置');
        form.shi_id = "";
        if(label) label.value = "全城";
      }
    } else {
      console.log('地址解析失败，使用默认设置');
      form.shi_id = "";
      if(label) label.value = "全城";
      uni.$u.toast("定位失败，显示全部活动");
    }
  } catch (error) {
    console.error('地址解析异常:', error);
    form.shi_id = "";
    if(label) label.value = "全城";
  }

  // 无论地址解析是否成功，都要加载活动列表
  if (isMounted.value) {
    resetList();
  }
};
// {{ AURA-X: Delete - 移除不再使用的getAddressId函数，已改用高德adcode. Confirmed via 寸止. }}
// 获取数据（增强错误处理）
const getList = async (retryCount = 0) => {
  if (!isMounted.value) return;

  try {
    // 检查API函数是否存在
    if (typeof huodongget_list !== 'function') {
      console.error('huodongget_list 函数不存在');
      uni.$u.toast('系统错误，请稍后重试');
      return;
    }

    const res = await huodongget_list(form);

    if (!isMounted.value) {
      return;
    }

    if (res === "n") {
      // 空数据处理
      console.log('服务器返回空数据');
      if (form.page === 1) {
        list.value = []; // 如果是第一页，清空列表
      }
      stop.value = false;
    } else if (res && res.status === "ok" && Array.isArray(res.data)) {
      // 成功获取数据
      const newData = res.data || [];

      if (form.page === 1) {
        // 第一页时直接替换
        list.value = newData;

        // 差异化缓存策略：只对历史活动进行缓存
        if (form.list_type === 3) {
          const cacheKey = generateCacheKey();
          cacheManager.setCache(cacheKey, newData);
          console.log(`缓存第一页数据 [历史活动]:`, newData.length, '条记录，缓存键:', cacheKey);
        } else {
          const activityTypeName = form.list_type === 1 ? '全部活动' : '线上活动';
          console.log(`${activityTypeName}不使用缓存，跳过缓存设置`);
        }
      } else {
        // 后续页面时合并数据，优化内存使用
        const combinedList = list.value.concat(newData);
        const MAX_LIST_SIZE = 100; // 减少内存占用，提升性能

        if (combinedList.length > MAX_LIST_SIZE) {
          // 保留最新的数据，移除旧数据释放内存
          const oldData = combinedList.slice(0, combinedList.length - MAX_LIST_SIZE);
          // 清理旧数据的图片引用
          oldData.forEach(item => {
            if (item.img_url_original) item.img_url_original = null;
            if (item.img_url) item.img_url = null;
          });
          list.value = combinedList.slice(-MAX_LIST_SIZE);
        } else {
          list.value = combinedList;
        }
      }

      // 强制触发响应式更新
      nextTick(() => {
        // 响应式更新完成
      });

      if (res.data.length < form.page_size) {
        stop.value = false;
      }
    } else if (res && res.status === "error") {
      // 服务器返回错误
      console.error('服务器返回错误:', res.msg);
      uni.$u.toast(res.msg || '获取活动列表失败');
      if (retryCount < 2) {
        console.log(`尝试重新获取数据，第${retryCount + 1}次重试`);
        setTimeout(() => getList(retryCount + 1), 1000);
        return;
      }
    } else {
      // 未知响应格式
      console.error('未知响应格式:', res);
      if (retryCount < 2) {
        console.log(`尝试重新获取数据，第${retryCount + 1}次重试`);
        setTimeout(() => getList(retryCount + 1), 1000);
        return;
      }
    }

    // 显示消息提示（如果有）
    if (res && res.msg) uni.$u.toast(res.msg);
  } catch (error) {
    console.error('获取活动列表异常:', error);
    uni.$u.toast('加载失败，请稍后重试');

    // 添加重试机制
    if (retryCount < 2) {
      console.log(`尝试重新获取数据，第${retryCount + 1}次重试`);
      setTimeout(() => getList(retryCount + 1), 1000);
      return;
    }
  } finally {
    if (!isMounted.value) return;
    // 确保loading状态被正确重置
    if(loading) {
      loading.value = false;
      console.log('getList完成，设置loading状态为false');
    }
    uni.stopPullDownRefresh();
  }
};
// 获取指定天数内的日期及周几
const getWeek = (day) => {
  let startDate = new Date();
  let endDate = new Date();
  let num = startDate.getDate();
  endDate.setDate(num + day);
  let weeks = ["日", "一", "二", "三", "四", "五", "六"];
  while (endDate.getTime() - startDate.getTime() >= 0) {
    let year = startDate.getFullYear();
    let mount =
        startDate.getMonth() + 1 < 10
            ? "0" + (startDate.getMonth() + 1)
            : startDate.getMonth() + 1;
    let day =
        startDate.getDate() < 10
            ? "0" + startDate.getDate()
            : startDate.getDate();
    let week = `周${weeks[startDate.getDay()]}`;
    if (dayList.value.length > 0)
      dayList.value.push({week, day: year + "-" + mount + "-" + day});
    else
      dayList.value.push(
          {week: "全部", day: ""},
          {week: "今日", day: year + "-" + mount + "-" + day}
      );
    startDate.setDate(startDate.getDate() + 1);
  }
};
// 点击轮播图跳转
const clickSwiper = (e) => {
  console.log('轮播图点击事件，索引:', e);
  if (info && info.value.lunbotu && info.value.lunbotu[e]) {
    const item = info.value.lunbotu[e];
    console.log('点击的轮播图项目:', item);

    if (item.url_type == 1)
      navto(`/pages/bundle/index/webview?url=${item.url_params}`);
    if (item.url_type == 2)
      navto(`/pages/bundle/index/activeInfo?id=${item.url_params}`);
    if (item.url_type == 3)
      navto(`${item.url_params}`);
  } else {
    console.warn('轮播图点击失败，数据不存在:', { e, lunbotu: info.value?.lunbotu });
  }
};


// {{ AURA-X: Modify - 修复城市筛选逻辑，确保不同tab的筛选规则正确. Confirmed via 寸止. }}
// 切换tab - 修复城市筛选逻辑
const changeTab = (index) => {
  if(tabIndex) tabIndex.value = index;

  // 记录切换前的活动类型，用于调试
  const previousListType = form.list_type;
  form.list_type = index + 1; // 根据tab索引设置list_type

  console.log(`Tab切换: ${previousListType} -> ${form.list_type}`, {
    tabNames: ['全部活动', '线上活动', '历史活动'],
    currentTab: ['全部活动', '线上活动', '历史活动'][index]
  });

  // 根据不同tab设置城市筛选规则
  if (index === 0) {
    // 全部活动：保持当前选择的城市筛选（线下活动需要筛选城市）
    // 恢复已选择的城市状态
    try {
      const selectedCity = uni.getStorageSync('selectedCity');
      if (selectedCity && selectedCity.adcode && selectedCity.name) {
        form.shi_id = selectedCity.adcode;
        if(label) label.value = selectedCity.name;
      }
    } catch (error) {
      console.warn('恢复城市状态失败:', error);
    }
  } else if (index === 1) {
    // 线上活动：清除城市筛选（显示全国线上活动）
    form.shi_id = "";
    form.qu_id = "";
    if(label) label.value = "全城";
    if(place) place.value = "全城";
  } else if (index === 2) {
    // 历史活动：清除城市筛选（显示全国历史活动）
    form.shi_id = "";
    form.qu_id = "";
    if(label) label.value = "全城";
    if(place) place.value = "全城";
  }

  form.huodong_date = ""; // 清除日期筛选
  if(dayItem) dayItem.value = 0; // 重置日期选中项

  // 强制清空当前列表，确保不会显示错误的缓存数据
  if(list && list.value) {
    list.value = [];
  }

  resetList();
};
// 改变日期
const changeDay = (val, i) => {
  form.huodong_date = val.day;
  if(dayItem) dayItem.value = i;
  resetList();
};
// 改变位置排序
const changeNearly = (e) => {
  if(nearly) nearly.value = !nearly.value;
  form.sort = (nearly && nearly.value) ? 2 : 1;
  resetList();
};

// {{ AURA-X: Modify - 修改地址文本截断函数，限制为10个字符确保线下标识位置固定. Confirmed via 寸止. }}
const truncateAddress = (address) => {
  if (!address) return '';

  // 限制地址显示长度，超过10个字符显示省略号，确保"线下"标识位置固定
  const maxLength = 10;
  if (address.length > maxLength) {
    return address.substring(0, maxLength) + '...';
  }
  return address;
};
// 选择器按确定
const confirm = (e) => {
  if (e.value[0].id == 0) form.qu_id = "";
  else form.qu_id = e.value[0].id;
  if(place) place.value = e.value[0].name;
  if(pickerShow) pickerShow.value = false;
  resetList();
};
const sortConfirm = (e) => {
  form.sort = e.indexs[0] + 1;
  if(pickerShows) pickerShows.value = false;
  resetList();
};
// 搜索活动
const search = (e) => {
  resetList();
};
// 重置列表 - 优化：实现差异化缓存策略
const resetList = () => {
  if (!isMounted.value) return;
  console.log('resetList被调用，当前活动类型:', form.list_type);

  // 差异化缓存策略：
  // - 全部活动(type=1)：不使用缓存，实时获取最新数据
  // - 线上活动(type=2)：不使用缓存，实时获取最新数据
  // - 历史活动(type=3)：使用缓存机制
  const shouldUseCache = form.list_type === 3; // 只有历史活动使用缓存

  if (shouldUseCache) {
    // 检查缓存 - 仅对历史活动使用缓存
    const cacheKey = generateCacheKey();
    const cachedData = cacheManager.getCache(cacheKey);

    if (cachedData && cachedData.length > 0) {
      console.log(`使用有效缓存数据 [历史活动]:`, cachedData.length, '条记录');
      if(list) list.value = cachedData;
      if(loading) loading.value = false;
      if(finished) finished.value = cachedData.length < form.page_size;
      return;
    } else {
      console.log(`无有效缓存数据 [历史活动]，开始加载新数据`);
    }
  } else {
    // 全部活动和线上活动：跳过缓存，直接获取最新数据
    const activityTypeName = form.list_type === 1 ? '全部活动' : '线上活动';
    console.log(`${activityTypeName}不使用缓存，直接获取最新数据`);
  }

  form.page = 1;
  if(stop) stop.value = true;

  // 清理列表数据，释放内存
  if(list && list.value) {
    // 清理图片引用，帮助垃圾回收
    list.value.forEach(item => {
      if (item.img_url_original) {
        item.img_url_original = null;
      }
      if (item.img_url) {
        item.img_url = null;
      }
    });
    list.value = [];
  }

  // 设置loading状态
  if(loading) {
    loading.value = true;
  }

  getList();
};

// 生成缓存键 - 修复：添加list_type参数确保不同活动类型独立缓存
const generateCacheKey = () => {
  // 确保list_type参数被包含在缓存键中，避免不同活动类型数据污染
  const cacheKey = `activity_list_type${form.list_type}_${form.type_id}_${form.shi_id}_${form.huodong_date}_${form.sort}_${form.keyword}`;
  return cacheKey;
};

// 缓存管理优化 - 修复：统一缓存时间配置，与store保持一致
const cacheManager = {
  // 缓存过期时间（2分钟，与store中getCachedActivityList保持一致）
  CACHE_EXPIRE_TIME: 2 * 60 * 1000,

  // 检查缓存是否有效
  isCacheValid: (cacheData) => {
    if (!cacheData || !cacheData.timestamp) return false;
    return Date.now() - cacheData.timestamp < cacheManager.CACHE_EXPIRE_TIME;
  },

  // 设置缓存
  setCache: (key, data) => {
    const cacheData = {
      data: data,
      timestamp: Date.now()
    };
    store().cacheActivityList(key, cacheData);
    console.log(`设置缓存 [${key}]:`, data.length, '条记录，过期时间:', cacheManager.CACHE_EXPIRE_TIME / 1000, '秒');
  },

  // 获取缓存
  getCache: (key) => {
    const cacheData = store().getCachedActivityList(key);
    if (cacheManager.isCacheValid(cacheData)) {
      console.log(`缓存命中 [${key}]:`, cacheData.data.length, '条记录');
      return cacheData.data;
    }
    console.log(`缓存失效或不存在 [${key}]`);
    return null;
  }
};

// 智能预加载机制
const smartPreload = {
  // 用户行为追踪
  scrollBehavior: {
    scrollSpeed: 0,
    scrollDirection: 'down',
    lastScrollTop: 0,
    fastScrollCount: 0
  },

  // 预测用户需求
  predictUserNeed: () => {
    const behavior = smartPreload.scrollBehavior;

    // 如果用户快速向下滚动，预加载下一页
    if (behavior.scrollDirection === 'down' && behavior.scrollSpeed > 100) {
      behavior.fastScrollCount++;
      if (behavior.fastScrollCount > 3) {
        return 'preload_next_page';
      }
    }

    // 如果用户在列表底部停留，预加载相关内容
    if (behavior.scrollDirection === 'down' && behavior.scrollSpeed < 10) {
      return 'preload_related';
    }

    return null;
  },

  // 执行预加载
  executePreload: async (type) => {
    if (type === 'preload_next_page' && !finished.value && !loading.value) {
      console.log('智能预加载：下一页数据');
      form.page += 1;
      await getList();
    } else if (type === 'preload_related') {
      console.log('智能预加载：相关内容');
      smartPreload.preloadRelatedContent();
    }
  },

  // 预加载相关内容
  preloadRelatedContent: () => {
    // 预加载活动详情的关键信息
    if (list.value && list.value.length > 0) {
      const recentActivities = list.value.slice(-3); // 最近3个活动
      recentActivities.forEach(activity => {
        if (activity.id) {
          // 缓存活动基本信息
          store().cacheActivityDetail(activity.id, {
            id: activity.id,
            name: activity.name,
            img_url: activity.img_url,
            start_time: activity.start_time,
            cached_at: Date.now()
          });
        }
      });
    }
  }
};

// 滚动事件处理
const handleScroll = (e) => {
  const currentScrollTop = e.scrollTop;
  const behavior = smartPreload.scrollBehavior;

  // 计算滚动速度和方向
  behavior.scrollSpeed = Math.abs(currentScrollTop - behavior.lastScrollTop);
  behavior.scrollDirection = currentScrollTop > behavior.lastScrollTop ? 'down' : 'up';
  behavior.lastScrollTop = currentScrollTop;

  // 预测用户需求并执行预加载
  const prediction = smartPreload.predictUserNeed();
  if (prediction) {
    smartPreload.executePreload(prediction);
  }

  // 重置快速滚动计数
  if (behavior.scrollSpeed < 50) {
    behavior.fastScrollCount = 0;
  }
};

// 判断是否是当天活动
const isToday = (dateString) => {
  if (!dateString) return false;

  // 获取当前日期字符串 YYYY-MM-DD 格式
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, '0');
  const day = String(today.getDate()).padStart(2, '0');
  const todayString = `${year}-${month}-${day}`;

  // 比较日期部分
  return dateString.split(' ')[0] === todayString;
};

// 判断是否是明天活动
const isTomorrow = (dateString) => {
  if (!dateString) return false;

  // 获取明天日期字符串 YYYY-MM-DD 格式
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  const year = tomorrow.getFullYear();
  const month = String(tomorrow.getMonth() + 1).padStart(2, '0');
  const day = String(tomorrow.getDate()).padStart(2, '0');
  const tomorrowString = `${year}-${month}-${day}`;

  // 比较日期部分
  return dateString.split(' ')[0] === tomorrowString;
};

// 已删除未使用的isWithin10Days函数

// 获取星期几
const getWeekDay = (dateString) => {
  if (!dateString) return '';

  const date = new Date(dateString.split(' ')[0]);
  const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  return weekDays[date.getDay()];
};

// 格式化日期显示
const formatDateDisplay = (dateString) => {
  if (!dateString) return '';

  if (isToday(dateString)) {
    return '今天';
  } else if (isTomorrow(dateString)) {
    return '明天';
  } else {
    // 只显示月-日
    const dateParts = dateString.split(' ')[0].split('-');
    return `${dateParts[1]}-${dateParts[2]}`;
  }
};

// 智能图片处理系统
const imageProcessor = {
  // 设备性能评估（修复已废弃API）
  getDevicePerformance() {
    try {
      // 使用新的API替代已废弃的getSystemInfoSync
      const windowInfo = uni.getWindowInfo();
      const deviceInfo = uni.getDeviceInfo();

      const pixelRatio = windowInfo.pixelRatio || 2;
      const platform = deviceInfo.platform || 'unknown';
      const model = deviceInfo.model || 'unknown';

      // 根据设备信息评估性能等级
      let performanceLevel = 'medium';

      if (pixelRatio >= 3 && platform === 'ios') {
        performanceLevel = 'high';
      } else if (pixelRatio >= 2.5) {
        performanceLevel = 'high';
      } else if (pixelRatio <= 1.5) {
        performanceLevel = 'low';
      }

      return { performanceLevel, pixelRatio, platform, model };
    } catch (error) {
      console.error('获取设备信息失败:', error);
      // 降级处理：返回默认值
      return {
        performanceLevel: 'medium',
        pixelRatio: 2,
        platform: 'unknown',
        model: 'unknown'
      };
    }
  },

  // 智能格式选择
  selectOptimalFormat(originalUrl, deviceInfo) {
    const { performanceLevel, platform } = deviceInfo;

    // 检查浏览器支持的格式
    const supportsWebP = platform !== 'devtools'; // 大部分现代设备支持WebP
    const supportsAVIF = false; // 小程序暂不支持AVIF

    if (supportsAVIF && performanceLevel === 'high') {
      return 'avif';
    } else if (supportsWebP) {
      return 'webp';
    } else {
      return 'jpg';
    }
  },

  // 智能质量选择
  selectOptimalQuality(deviceInfo, imageType = 'activity') {
    const { performanceLevel, pixelRatio } = deviceInfo;

    const qualityMap = {
      'activity': {
        'low': 60,
        'medium': 75,
        'high': 85
      },
      'avatar': {
        'low': 70,
        'medium': 80,
        'high': 90
      },
      'banner': {
        'low': 65,
        'medium': 80,
        'high': 90
      }
    };

    return qualityMap[imageType][performanceLevel];
  }
};

// 图片URL优化处理（增强版）
const getOptimizedImageUrl = (originalUrl, isFallback, imageType = 'activity') => {
  try {
    if (isFallback || !originalUrl) {
      return `${store().$state.url}default_activity.png`;
    }

    // 获取设备性能信息（带错误处理）
    const deviceInfo = imageProcessor.getDevicePerformance();

    if (!deviceInfo) {
      console.warn('无法获取设备信息，使用原始URL');
      return originalUrl;
    }

    // 选择最优格式和质量
    const optimalFormat = imageProcessor.selectOptimalFormat(originalUrl, deviceInfo);
    const optimalQuality = imageProcessor.selectOptimalQuality(deviceInfo, imageType);

    // 如果URL支持参数，添加优化参数
    if (originalUrl.includes('linqingkeji.com')) {
      const separator = originalUrl.includes('?') ? '&' : '?';
      return `${originalUrl}${separator}quality=${optimalQuality}&format=${optimalFormat}&optimize=true`;
    }

    return originalUrl;
  } catch (error) {
    console.error('图片URL优化处理失败:', error);
    return originalUrl || `${store().$state.url}default_activity.png`;
  }
};

// 图片预加载队列管理器（小程序兼容版）
const imagePreloader = {
  queue: [],
  loading: new Set(),
  maxConcurrent: 3,

  // 添加到预加载队列
  addToQueue(imageUrl, priority = 1) {
    try {
      if (!imageUrl || this.loading.has(imageUrl)) return;

      this.queue.push({ url: imageUrl, priority, timestamp: Date.now() });
      this.queue.sort((a, b) => b.priority - a.priority); // 按优先级排序

      this.processQueue();
    } catch (error) {
      console.error('添加预加载队列失败:', error);
    }
  },

  // 处理预加载队列（小程序环境兼容）
  processQueue() {
    try {
      if (this.loading.size >= this.maxConcurrent || this.queue.length === 0) return;

      const item = this.queue.shift();
      if (!item) return;

      this.loading.add(item.url);

      // 小程序环境检测
      if (typeof Image !== 'undefined') {
        // H5环境：使用Image对象
        const image = new Image();
        image.onload = () => {
          this.loading.delete(item.url);
          this.processQueue();
        };
        image.onerror = () => {
          this.loading.delete(item.url);
          this.processQueue();
        };
        image.src = item.url;
      } else {
        // 小程序环境：使用uni.downloadFile预加载
        uni.downloadFile({
          url: item.url,
          success: () => {
            this.loading.delete(item.url);
            this.processQueue();
          },
          fail: () => {
            this.loading.delete(item.url);
            this.processQueue();
          }
        });
      }

      // 设置超时
      setTimeout(() => {
        if (this.loading.has(item.url)) {
          this.loading.delete(item.url);
          this.processQueue();
        }
      }, 10000); // 10秒超时
    } catch (error) {
      console.error('处理预加载队列失败:', error);
    }
  },

  // 批量预加载
  preloadBatch(urls, priority = 1) {
    try {
      if (Array.isArray(urls)) {
        urls.forEach(url => this.addToQueue(url, priority));
      }
    } catch (error) {
      console.error('批量预加载失败:', error);
    }
  },

  // 清空队列
  clear() {
    try {
      this.queue = [];
      this.loading.clear();
    } catch (error) {
      console.error('清空预加载队列失败:', error);
    }
  }
};

// 图片加载错误处理（增强版）
const handleImageError = (index, item) => {
  try {
    if (!list.value || !list.value[index]) {
      console.warn('图片错误处理: 无效的索引或列表项', { index, hasItem: !!item });
      return;
    }

    const currentItem = list.value[index];
    const retryCount = currentItem.imageRetryCount || 0;

    console.warn(`图片加载失败 [${index}]:`, {
      url: currentItem.img_url,
      retryCount,
      itemId: currentItem.id
    });

    if (retryCount < 2) {
      // 重试加载
      currentItem.imageRetryCount = retryCount + 1;
      const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 5000); // 指数退避，最大5秒

      setTimeout(() => {
        if (list.value && list.value[index] && list.value[index].id === currentItem.id) {
          // 触发重新加载
          const tempUrl = currentItem.img_url;
          currentItem.img_url = '';
          nextTick(() => {
            if (list.value && list.value[index] && list.value[index].id === currentItem.id) {
              list.value[index].img_url = tempUrl;
            }
          });
        }
      }, retryDelay);
    } else {
      // 重试失败，使用默认图片
      currentItem.img_url_fallback = true;
      currentItem.img_url_original = currentItem.img_url;
      currentItem.img_url = null;
      console.error(`图片加载最终失败 [${index}]:`, currentItem.img_url_original);
    }
  } catch (error) {
    console.error('图片错误处理异常:', error);
  }
};

// 图片加载成功处理（增强版）
const handleImageLoad = (index, item) => {
  try {
    if (!list.value || !list.value[index]) {
      console.warn('图片加载成功处理: 无效的索引或列表项', { index, hasItem: !!item });
      return;
    }

    const currentItem = list.value[index];
    currentItem.img_url_fallback = false;
    currentItem.imageRetryCount = 0;
    currentItem.img_loaded = true;

    // 智能预加载：预加载可视区域附近的图片
    const preloadRange = 3; // 预加载前后3张图片
    const preloadUrls = [];

    for (let i = Math.max(0, index - preloadRange); i <= Math.min(list.value.length - 1, index + preloadRange); i++) {
      if (i !== index && list.value[i] && list.value[i].img_url && !list.value[i].img_preloaded) {
        try {
          const optimizedUrl = getOptimizedImageUrl(list.value[i].img_url, list.value[i].img_url_fallback);
          if (optimizedUrl) {
            preloadUrls.push(optimizedUrl);
            list.value[i].img_preloaded = true;
          }
        } catch (urlError) {
          console.warn('预加载URL处理失败:', urlError);
        }
      }
    }

    // 批量预加载
    if (preloadUrls.length > 0) {
      try {
        imagePreloader.preloadBatch(preloadUrls, 2); // 中等优先级
      } catch (preloadError) {
        console.warn('批量预加载失败:', preloadError);
      }
    }
  } catch (error) {
    console.error('图片加载成功处理异常:', error);
  }
};

// 处理活动点击事件，检查用户登录状态
const handleActivityClick = (activityId) => {
  // 检查用户是否已登录
  const userInfo = store().$state.userInfo;

  if (!userInfo || !userInfo.uid || !userInfo.token) {
    // 用户未登录，跳转到登录注册页面
    uni.showModal({
      title: '提示',
      content: '请先登录后查看活动详情',
      confirmText: '去登录',
      cancelText: '取消',
      success: function(res) {
        if (res.confirm) {
          // 用户点击确定，跳转到登录页面
          navto('/pages/bundle/common/login');
        }
      }
    });
    return;
  }

  // 用户已登录，正常跳转到活动详情页面
  navto(`/pages/bundle/index/activeInfo?id=${activityId}`);
};
</script>
<template>
  <view class="page b6f">
    <u-sticky>
      <myTitle img="bg.png" height="178rpx" :backShow="false">
        <view class="ml30 mr30" style="display: flex; align-items: center;">
          <u-input
              placeholder="搜索感兴趣的项目活动"
              border="none"
              shape="circle"
              placeholder-style="font-size:24rpx;color:#aaa"
              :customStyle="{
              padding: '0 28rpx',
              width: iptWidth,
              height: '64rpx',
              lineHeight: '64rpx',
              background: 'rgba(255, 255, 255, 0.95)',
              boxShadow: '0 12rpx 32rpx rgba(106, 192, 134, 0.15)',
              fontSize: '26rpx'
            }"
              v-model="form.keyword"
              @blur="search"
              @confirm="search"
          >
            <template v-slot:prefix>
              <view class="df aic" @click="getLonLat" style="flex-shrink: 0;">
                <u-text
                    prefix-icon="map-fill"
                    color="#333"
                    size="22rpx"
                    :text="label || '全城'"
                    :icon-style="{
                    margin: '0 8rpx 0 0',
                    fontSize: '20rpx',
                    color: '#6AC086'
                  }"
                    :customStyle="{
                    whiteSpace: 'nowrap',
                    maxWidth: '120rpx',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }"
                    bold
                ></u-text>
                <u-line
                    length="26rpx"
                    color="#E6E6E6"
                    direction="col"
                    margin="0 20rpx"
                ></u-line>
              </view>
            </template>
          </u-input>
        </view>
      </myTitle>
    </u-sticky>
    <u-gap height="30rpx"></u-gap>
    <!--    <u-grid :border="false" col="5">
          <u-grid-item
            v-for="(val, i) in info.active"
            :key="i"
            @click="store().setActiveForm(form, val.name, val.id)"
          >
            <u-image
              :src="val.icon"
              width="90rpx"
              height="90rpx"
              radius="50%"
            ></u-image>
            <u-text
              margin="10rpx auto 15rpx"
              size="26rpx"
              align="center"
              color="#333333"
              bold
              :text="val.name"
            ></u-text>
          </u-grid-item>
        </u-grid>-->
    <!-- 轮播图区域 -->
    <u-gap height="20rpx"></u-gap>

    <view
        class="w690 ma mb20 r30 swiper-container"
        style="box-shadow: 0 12rpx 32rpx rgba(106, 192, 134, 0.15)"
    >
      <!-- 轮播图加载状态 -->
      <view v-if="!info?.lunbotu || info.lunbotu.length === 0" class="swiper-loading">
        <u-loading-icon mode="circle" size="30" color="#6AC086"></u-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 轮播图内容 -->
      <u-swiper
          v-else
          height="280rpx"
          radius="30rpx"
          key-name="img_url"
          :list="info.lunbotu"
          :indicator="true"
          indicator-mode="circle"
          :circular="true"
          :autoplay="true"
          @click="clickSwiper"
      ></u-swiper>
    </view>

    <!-- 活动分类标签 - 重构：图标独立化设计 -->
    <u-gap height="20rpx"></u-gap>
    <view class="activity-tabs-container">
      <view
          v-for="(val, i) in tabList"
          :key="i"
          class="activity-tab-wrapper"
          @click="changeTab(i)"
      >
        <!-- 按钮组件 - 包含图标和文字的水平布局 -->
        <view class="activity-tab-item" :class="{ 'activity-tab-active': tabIndex === i }">
          <!-- 独立的图标组件 - 在文字左前方 -->
          <view class="tab-icon-container">
            <image :src="val.icon" mode="aspectFit" class="tab-icon-independent"></image>
          </view>
          <!-- 文字组件 -->
          <text class="tab-text" :class="{ 'text-active': tabIndex === i }">{{ val.name }}</text>
        </view>
      </view>
    </view>

    <!-- 活动列表区域 -->
    <u-gap height="20rpx"></u-gap>
    <!-- 日期以及二级分类 -->
    <u-sticky offset-top="176rpx" bg-color="#fff">
      <u-gap height="10rpx"></u-gap>
      <!-- 日期选择器组件 - 暂时注释掉
      <u-scroll-list :indicator="false">
        <view
            class="ml30 fs"
            v-for="(val, i) in dayList"
            :key="i"
            @click="changeDay(val, i)"
        >
          <u-text
              :text="val.week"
              bold
              align="center"
              color="#aaa"
              size="30rpx"
              margin="0 auto 10rpx"
          ></u-text>
          <view
              class="mb30"
              :style="{
              width: '88rpx',
              backgroundColor: dayItem === i ? '#C6E538' : '',
              borderRadius: dayItem === i ? '30rpx' : ''
            }"
          >
            <u-text
                :text="val.day ? val.day.slice(-2) : '全部'"
                bold
                align="center"
                :color="dayItem === i ? '#333' : '#AAA'"
                size="30rpx"
            ></u-text>
          </view>
        </view>
      </u-scroll-list>
      -->
      <view class="px30 pb30 df aic jcsb">
        <!-- <view class="f1">
          <view v-show="!tabIndex">
            <u-text
              text="距离优先"
              size="28rpx"
              :bold="true"
              :color="nearly ? '#FAD000' : ''"
              :icon-style="{
                margin: '0 10rpx',
                color: nearly ? '#FAD000' : '',
                fontSize: '20rpx',
              }"
              :suffix-icon="nearly ? 'arrow-up-fill' : 'arrow-down-fill'"
              @click="changeNearly"
            ></u-text>
          </view>
        </view> -->
        <!--        <view class="f1">
                  <view v-show="!tabIndex">
                    <u-text
                        align="left"
                        :text="sort"
                        size="28rpx"
                        :icon-style="{
                        margin: '0 10rpx',
                        color: '#909090',
                        fontSize: '20rpx'
                      }"
                        color="#909090"
                        suffix-icon="arrow-down-fill"
                        @click="pickerShows = true"
                    ></u-text>
                  </view>
                </view>-->
        <!--        <u-text
                    class="pr"
                    align="center"
                    :text="place"
                    size="28rpx"
                    :icon-style="{
                    margin: '0 10rpx',
                    color: '#909090',
                    fontSize: '20rpx'
                  }"
                    color="#909090"
                    suffix-icon="arrow-down-fill"
                    @click="pickerShow = true"
                ></u-text>-->
        <!--        <view class="f1">
                  <u-icon
                    label="查看更多"
                    size="20rpx"
                    label-size="22rpx"
                    label-pos="left"
                    label-color="#aaa"
                    :bold="true"
                    color="#aaa"
                    name="arrow-right"
                    @click="store().setActiveForm(form, '活动列表')"
                  ></u-icon>
                </view>-->
      </view>
    </u-sticky>
    <!-- 活动列表 -->
    <!-- 加载中状态显示 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-animation">
        <view class="loading-circle"></view>
        <view class="loading-circle"></view>
        <view class="loading-circle"></view>
      </view>
      <text class="loading-text">正在加载活动信息...</text>
    </view>

    <!-- 无数据状态显示 -->
    <view v-if="!loading && (!list || list.length === 0)" class="empty-container">
      <image :src="`${store().$state.url}empty.png`" mode="aspectFit" class="empty-image"></image>
      <text class="empty-text">暂无活动信息</text>
    </view>

    <!-- 活动列表 -->
    <view v-else-if="!loading && list && list.length > 0">
      <view v-for="(val, i) in list" :key="val.id || i">
      <!-- 添加日期分隔线 -->
      <view v-if="i === 0 || val.start_time.split(' ')[0] !== list[i-1].start_time.split(' ')[0]"
            class="date-separator">
        <text class="separator-text">
          {{ formatDateDisplay(val.start_time) }}
          <text class="weekday-text">{{ getWeekDay(val.start_time) }}</text>
        </text>
      </view>

      <view class="activity-card-enhanced"
            @click="handleActivityClick(val.id)">
        <!-- 图片容器 -->
        <view class="activity-image-wrapper-enhanced">
          <image
              class="activity-image-enhanced"
              :src="getOptimizedImageUrl(val.img_url, val.img_url_fallback)"
              mode="aspectFill"
              lazy-load
              :fade-show="true"
              :webp="true"
              @error="handleImageError(i, val)"
              @load="handleImageLoad(i, val)"
          ></image>
          <!-- 添加活动状态标签 - 只显示已结束状态 -->
          <view
              v-if="Date.now() * 1 > new Date(val.start_time.replaceAll('-', '/')) * 1"
              class="activity-status-tag-enhanced status-ended"
          >
            已结束
          </view>
        </view>

        <view class="activity-content-enhanced">
          <view class="activity-header-enhanced">
            <u-avatar
                :src="val.user.avatar"
                mode="aspectFill"
                size="48rpx"
            ></u-avatar>
            <u-text
                margin="0 0 0 12rpx"
                color="#666666"
                size="24rpx"
                lines="1"
                :text="val.user.nickname"
            ></u-text>
          </view>
          <u-text
              class="activity-title-enhanced"
              size="32rpx"
              :bold="true"
              lines="2"
              :text="val.name"
              color="#1a1a1a"
          ></u-text>
          <u-text
              prefix-icon="account-fill"
              :icon-style="{
                marginRight: '8rpx',
                fontSize: '24rpx',
                color: '#999999'
              }"
              :text="`${val.baoming_num}人报名`"
              color="#999999"
              size="24rpx"
              margin="8rpx 0"
          ></u-text>

          <!-- {{ AURA-X: Modify - 修复线下活动图标显示. Confirmed via 寸止. }} -->
          <!-- 地点信息 - 根据活动类型显示不同内容 -->
          <view class="location-info-enhanced">
            <u-text
                v-if="val.is_online"
                prefix-icon="wifi"
                :icon-style="{
                  marginRight: '8rpx',
                  fontSize: '24rpx',
                  color: '#999999'
                }"
                text="线上活动"
                color="#999999"
                size="24rpx"
                lines="1"
            ></u-text>
            <!-- {{ AURA-X: Modify - 使用新的位置图标并修复地址文本长度限制. Confirmed via 寸止. }} -->
            <view v-else class="offline-location-info">
              <image
                class="offline-icon"
                src="/static/location-pin.svg"
                mode="aspectFit"
              ></image>
              <text class="location-text">{{ truncateAddress(val.sheng + val.shi + val.qu + val.addr) }}</text>
            </view>

            <!-- 线上/线下活动标识 -->
            <view class="activity-type-tag-enhanced" :class="{'online-type': val.is_online, 'offline-type': !val.is_online}">
              {{ val.is_online ? '线上' : '线下' }}
            </view>
          </view>


        </view>
      </view>
      </view>
    </view>
    <up-back-top
        bottom="30rpx"
        right="75rpx"
        :scroll-top="scrollTop"
    ></up-back-top>
    <u-picker
        :show="pickerShow"
        key-name="name"
        :close-on-click-overlay="true"
        :columns="columns"
        @cancel="pickerShow = false"
        @close="pickerShow = false"
        @confirm="confirm"
    ></u-picker>
    <u-picker
        :show="pickerShows"
        :close-on-click-overlay="true"
        :columns="sortColumns"
        @cancel="pickerShows = false"
        @close="pickerShows = false"
        @confirm="sortConfirm"
    ></u-picker>

    <!-- 自定义底部导航栏 -->
    <CustomTabBar :current="0" />
  </view>
</template>

<style scoped lang="less">
// 为自定义底部导航栏预留空间 - 优化：考虑发布按钮额外高度
.page {
  // 基础导航栏高度(80rpx) + 发布按钮额外高度(40rpx) + 内边距(20rpx) + 安全区域
  padding-bottom: calc(140rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(140rpx + env(safe-area-inset-bottom));
  // 确保页面可以完整滚动
  box-sizing: border-box;
}

.container {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  height: 200rpx; /* 保持容器高度固定 */
  width: 350rpx; /* 保持容器宽度固定 */
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  position: relative;
}

.image-container {
  width: 100%; /* 图片容器宽度 */
  height: 100%; /* 图片容器高度 */
  overflow: hidden;
  border-radius: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.fixed-image {
  width: 100%;
  height: 100%;
  border-radius: 30rpx;
  transition: transform 0.3s ease;
}

.container:hover .fixed-image {
  transform: scale(1.05);
}

.borderBottom {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1); /* 更明显的黑色分割线 */
  padding-bottom: 20rpx; /* 减小底部内边距 */
}

/* 添加日期时间样式 */
.activity-date-time {
  display: flex;
  align-items: center;
  margin-top: 6rpx;
}

.activity-date-time .u-text {
  color: #8a8a8a;
}

.today-tag {
  background: linear-gradient(103deg, #8EFFFE 0%, #C6E538 100%);
  color: #333;
  padding: 2rpx 10rpx;
  border-radius: 10rpx;
  margin-left: 10rpx;
  font-size: 20rpx;
  font-weight: bold;
  box-shadow: 0 12rpx 32rpx rgba(106, 192, 134, 0.15);
}

.activity-status-tag {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: bold;
  color: #fff;
  z-index: 10;
}

.status-registration {
  background: linear-gradient(135deg, #8EFFFE 0%, #C6E538 100%);
  color: #333 !important; /* 深色文字在浅色背景上 */
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(142, 255, 254, 0.4);
  }

  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 6rpx rgba(142, 255, 254, 0);
  }

  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(142, 255, 254, 0);
  }
}

.status-ended {
  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);
}



.activity-image {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
}

.date-separator {
  width: 100%;
  height: 60rpx; /* 减小高度 */
  display: flex;
  align-items: center;
  margin: 20rpx 0 10rpx; /* 减小上下间距 */
  position: relative;
  padding: 0 30rpx;
  box-sizing: border-box;
}

.date-separator::before {
  content: '';
  position: absolute;
  left: 100rpx;
  right: 30rpx;
  height: 1px; /* 细黑色直线 */
  background: rgba(0, 0, 0, 0.2); /* 淡黑色 */
  z-index: 0;
  top: 50%;
}

.separator-text {
  background: #ffffff;
  padding: 6rpx 20rpx;
  font-size: 28rpx;
  color: #333;
  position: relative;
  z-index: 1;
  border-radius: 4rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.weekday-text {
  font-size: 32rpx;
  color: #000;
  margin-left: 10rpx;
  font-weight: bold;
}

.online-tag {
  background: linear-gradient(135deg, #8EFFFE 0%, #C6E538 100%);
  color: #333;
  padding: 2rpx 10rpx;
  border-radius: 10rpx;
  margin-left: 10rpx;
  font-size: 20rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.online-location {
  display: flex;
  align-items: center;
  margin-top: 6rpx;
}

/* {{ AURA-X: Modify - 修改地址文本样式，与线上活动标识字体保持一致. Confirmed via 寸止. }} */
.location-text {
  margin-left: 10rpx;
  font-size: 20rpx; /* 与activity-type-tag-enhanced保持一致 */
  color: #333;
  font-weight: 600; /* 与activity-type-tag-enhanced保持一致 */
}

/* 线上活动样式 */
.online-location {
  display: flex;
  align-items: center;
  margin-top: 6rpx;
}

.mini-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 6rpx;
}

.online-text {
  font-size: 22rpx;
  color: #4caf50;
  font-weight: bold;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
}

.loading-animation {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-circle {
  width: 18rpx;
  height: 18rpx;
  margin: 0 8rpx;
  border-radius: 50%;
  background: linear-gradient(90deg, #88D7A0, #6AC086);
  animation: bounce 1.4s infinite ease-in-out both;
}

.loading-circle:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-circle:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1.0);
  }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #666;
  background: linear-gradient(90deg, #88D7A0, #6AC086);
  -webkit-background-clip: text;
  color: transparent;
}

/* 空状态样式 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  background: linear-gradient(90deg, #88D7A0, #6AC086);
  -webkit-background-clip: text;
  color: transparent;
}

.activity-tabs-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 40rpx 0 20rpx;
  padding: 0 30rpx;
  gap: 24rpx; /* 适中的按钮间距，为放大图标预留一些空间 */
  min-height: 60rpx; /* 恢复正常的最小高度 */
}

/* 活动选项卡包装器 - 支持图标独立化 */
.activity-tab-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

/* 重新设计的按钮样式 - 水平布局，图标在左，文字在右 */
.activity-tab-item {
  position: relative;
  padding: 12rpx 20rpx;
  border-radius: 50rpx;
  background: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  font-size: 28rpx;
  color: #666666;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(0, 0, 0, 0.08);
  min-width: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6rpx; /* 减少图标和文字间距，为文字留出更多空间 */
  overflow: visible; /* 保留这个属性，允许放大的图标超出按钮边界 */
}

/* 图标容器样式 - 在按钮内部，文字左侧，为放大图标预留空间 */
.tab-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx; /* 缩小容器宽度，为文字留出更多空间 */
  height: 60rpx; /* 缩小容器高度，保持比例 */
  flex-shrink: 0; /* 防止图标被压缩 */
  position: relative;
  overflow: visible; /* 允许放大的图标超出容器边界 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 选中状态的图标容器 - 添加背景光晕效果 */
.activity-tab-active .tab-icon-container {
  /* 添加径向渐变背景光晕 */
  background: radial-gradient(circle, rgba(106, 192, 134, 0.15) 0%, rgba(106, 192, 134, 0.05) 50%, transparent 70%);
  border-radius: 50%;
}

/* 独立图标样式 - 保持原始颜色，添加选中状态特效 */
.tab-icon-independent {
  width: 45rpx; /* 基础尺寸减少到0.7倍：84rpx × 0.7 = 59rpx */
  height: 45rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
  position: relative;
  z-index: 10; /* 提高z-index确保能遮挡按钮和文字 */
  /* 图标保持原始颜色，不添加任何滤镜效果 */
}

/* 选中状态的图标特效 - 0.8倍缩放 + 3D立体效果 */
.activity-tab-active .tab-icon-independent {
  transform: scale(1.7);
  /* 3D立体效果 - 多层阴影 */
  filter: drop-shadow(0 4rpx 8rpx rgba(106, 192, 134, 0.3))
          drop-shadow(0 8rpx 16rpx rgba(106, 192, 134, 0.2))
          drop-shadow(0 12rpx 24rpx rgba(106, 192, 134, 0.1));
  /* 增强对比度和亮度 */
  -webkit-filter: brightness(1.1) contrast(1.1)
                  drop-shadow(0 4rpx 8rpx rgba(106, 192, 134, 0.3))
                  drop-shadow(0 8rpx 16rpx rgba(106, 192, 134, 0.2))
                  drop-shadow(0 12rpx 24rpx rgba(106, 192, 134, 0.1));
}

/* 按钮文字样式 */
.tab-text {
  font-size: 28rpx;
  transition: all 0.3s ease;
}

/* 移除图标的选中状态样式 - 图标保持原始颜色不变 */

/* 移除旧的图标容器样式，因为图标已独立化 */

/* 移除复杂的动画效果，保持简洁 */

/* 文字选中状态 - 简化样式 */
.text-active {
  color: #ffffff;
  font-weight: 600;
}

/* 按钮悬停效果 - 保持简单 */
.activity-tab-item:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
}

/* 按钮选中状态 - 简化设计 */
.activity-tab-active {
  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);
  color: #ffffff;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(106, 192, 134, 0.25);
  border-color: #6AC086;
}

/* 移除复杂的伪元素效果，保持简洁设计 */

.activity-tab-indicator {
  display: none;
}

/* 添加新样式 */
.location-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: 6rpx;
}

.activity-type-tag {
  font-size: 20rpx;
  font-weight: bold;
  padding: 2rpx 12rpx;
  border-radius: 10rpx;
  width: 80rpx;
  text-align: center;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.online-type {
  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);
  color: #fff;
}

.offline-type {
  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);
  color: #fff;
}

/* 新增强样式 */
.activity-card-enhanced {
  position: relative;
  display: flex;
  margin: 0 30rpx 16rpx; /* 减少底部间距从32rpx到16rpx */
  padding: 20rpx; /* 减少内边距从24rpx到20rpx */
  background: #ffffff;
  border-radius: 24rpx; /* 增加圆角 */
  box-shadow: 0 12rpx 32rpx rgba(106, 192, 134, 0.15); /* 统一使用绿色主题阴影 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  border: 1rpx solid rgba(106, 192, 134, 0.1); /* 边框也使用绿色主题 */
}

.activity-card-enhanced:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 16rpx 48rpx rgba(106, 192, 134, 0.25); /* hover时加深绿色阴影 */
}

.activity-card-enhanced:active {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 40rpx rgba(106, 192, 134, 0.2); /* active时使用绿色阴影 */
}

.activity-image-wrapper-enhanced {
  width: 200rpx;
  height: 200rpx;
  position: relative;
  border-radius: 16rpx; /* 增加圆角 */
  overflow: hidden;
  flex-shrink: 0;
  background: #f8f9fa;
}

.activity-image-enhanced {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.activity-card-enhanced:hover .activity-image-enhanced {
  transform: scale(1.05);
}

.activity-status-tag-enhanced {
  position: absolute;
  top: 12rpx;
  left: 12rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 600;
  color: #fff;
  z-index: 10;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

/* 为增强版状态标签添加特定颜色 */
.activity-status-tag-enhanced.status-registration {
  color: #333 !important; /* 深色文字在浅色背景上 */
}

.activity-status-tag-enhanced.status-ended {
  color: #fff !important; /* 白色文字在深色背景上 */
}

.activity-content-enhanced {
  flex: 1;
  margin-left: 24rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 200rpx;
}

.activity-header-enhanced {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.activity-title-enhanced {
  margin: 8rpx 0 12rpx;
  line-height: 1.4;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

.location-info-enhanced {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin: 8rpx 0;
}

.online-location-enhanced {
  display: flex;
  align-items: center;
}

.mini-icon-enhanced {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.online-text-enhanced {
  font-size: 24rpx;
  color: #6AC086;
  font-weight: 500;
}

.activity-type-tag-enhanced {
  font-size: 20rpx;
  font-weight: 600;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  min-width: 60rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.activity-date-time-enhanced {
  display: flex;
  align-items: center;
  margin-top: 8rpx;
  flex-wrap: wrap;
  gap: 8rpx;
}

.today-tag-enhanced {
  background: linear-gradient(135deg, #8EFFFE 0%, #C6E538 100%);
  color: #1a1a1a;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 600;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-left: 8rpx;
}

/* 日期分隔线优化 */
.date-separator {
  width: 100%;
  height: 40rpx; /* 减少高度从60rpx到40rpx */
  display: flex;
  align-items: center;
  margin: 20rpx 0 12rpx; /* 减少上下间距 */
  position: relative;
  padding: 0 30rpx;
  box-sizing: border-box;
}

.date-separator::before {
  content: '';
  position: absolute;
  left: 120rpx;
  right: 30rpx;
  height: 1rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(0, 0, 0, 0.1) 20%, rgba(0, 0, 0, 0.1) 80%, transparent 100%);
  z-index: 0;
  top: 50%;
}

.separator-text {
  background: #ffffff;
  padding: 8rpx 24rpx;
  font-size: 28rpx;
  color: #1a1a1a;
  position: relative;
  z-index: 1;
  border-radius: 20rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(0, 0, 0, 0.04);
}

.weekday-text {
  font-size: 32rpx;
  color: #666666;
  margin-left: 12rpx;
  font-weight: bold;
}

/* 轮播图容器样式 */
.swiper-container {
  position: relative;
  min-height: 280rpx;
  overflow: hidden;
}

.swiper-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 280rpx;
  background: #f8f9fa;
  border-radius: 30rpx;
}

.swiper-loading .loading-text {
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #666;
}

/* {{ AURA-X: Add - 线下活动图标样式. Confirmed via 寸止. }} */
.offline-location-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.offline-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
  opacity: 0.6;
}

.location-text {
  font-size: 24rpx;
  color: #999999;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}



/* 响应式优化 */
@media screen and (max-width: 750rpx) {
  .activity-card-enhanced {
    margin: 0 20rpx 24rpx;
    padding: 20rpx;
  }

  .activity-image-wrapper-enhanced {
    width: 160rpx;
    height: 160rpx;
  }

  .activity-content-enhanced {
    margin-left: 20rpx;
    min-height: 160rpx;
  }

  .activity-tabs-container {
    padding: 0 20rpx;
    gap: 20rpx; /* 响应式下适中间距 */
    min-height: 50rpx; /* 响应式下恢复正常高度 */
  }

  .activity-tab-item {
    padding: 10rpx 16rpx;
    font-size: 26rpx;
    min-width: 100rpx;
    gap: 4rpx; /* 响应式下进一步减小图标和文字间距，防止换行 */
  }

  .tab-icon-container {
    width: 50rpx; /* 响应式下缩小容器宽度，为文字留出更多空间 */
    height: 50rpx;
  }

  .tab-icon-independent {
    width: 35rpx; /* 响应式下基础尺寸减少到0.7倍：70rpx × 0.7 = 49rpx */
    height: 35rpx;
  }

  /* 响应式下选中状态的图标缩放效果 */
  .activity-tab-active .tab-icon-independent {
    transform: scale(1.7); /* 响应式下缩放比例到1.3倍 */
  }
}
</style>