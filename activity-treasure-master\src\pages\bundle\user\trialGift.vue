<script setup>
import { reactive, computed } from "vue";
import { onLoad, onShareAppMessage } from "@dcloudio/uni-app";
import { usercreate_trial_share } from "@/api";
import { store } from "@/store";
import { navto } from "@/utils";
import { requireLogin } from "@/utils/auth";

// {{ AURA-X: Delete - 删除不再使用的变量，改为微信原生分享. Confirmed via 寸止. }}

// 体验会员信息
const trialInfo = reactive({
  days: 30,
  title: "30天会员体验券",
  description: "邀请好友免费体验会员服务",
  benefits: [
    "免费参与所有活动",
    "专属会员标识",
    "优先客服支持",
    "会员专享内容"
  ],
  validDays: 7 // 分享链接有效期
});

// 🆕 修改：用户权限检查（新增role_type=5，体验券分享权限保持不变）
const hasPermission = computed(() => {
  const roleType = parseInt(store().$state.userInfo?.role_type || 2);
  // 管理员(0)、分会长(1)、场地与活动第三方(3)、城市分会长(4)、场地第三方-不管理分会(5) 有分享权限
  // 注意：体验券分享权限与活动收费权限不同，分会长仍保持此权限
  return [0, 1, 3, 4, 5].includes(roleType);
});

// {{ AURA-X: Modify - 改为微信原生分享方式，在onShareAppMessage中动态生成分享链接. Confirmed via 寸止. }}
// 分享配置 - 分享给好友
onShareAppMessage(async (e) => {
  try {
    console.log('onShareAppMessage 被触发, 来源:', e?.from, '目标:', e?.target);

    // 动态生成分享链接
    try {
      const res = await usercreate_trial_share({
        uid: store().$state.userInfo.uid,
        token: store().$state.userInfo.token,
        trial_days: trialInfo.days
      });

      console.log('创建分享链接结果:', res);

      if (res?.status === 'ok') {
        const shareConfig = {
          title: `赠送您一张${trialInfo.days}天会员体验券`,
          path: `/pages/bundle/user/trialClaim?code=${res.data.share_code}`,
          imageUrl: store().$state.config?.img_config?.app_logo?.val || ''
        };

        console.log('体验券分享配置:', shareConfig);
        return shareConfig;
      }
    } catch (error) {
      console.error('生成分享链接失败:', error);
    }

    // 默认分享配置
    return {
      title: '赠送您一张30天会员体验券',
      path: '/pages/bundle/user/trialGift',
      imageUrl: store().$state.config?.img_config?.app_logo?.val || ''
    };
  } catch (error) {
    console.error('分享配置失败:', error);
    return {
      title: '赠送您一张30天会员体验券',
      path: '/pages/bundle/user/trialGift',
      imageUrl: store().$state.config?.img_config?.app_logo?.val || ''
    };
  }
});

onLoad(() => {
  // 检查用户权限
  if (!hasPermission.value) {
    uni.showModal({
      title: '权限不足',
      content: '您暂无权限分享会员体验券',
      showCancel: false,
      success: () => {
        uni.navigateBack();
      }
    });
    return;
  }

  // 使用统一的登录校验
  if (!requireLogin('', '需要登录后才能分享体验券')) {
    return;
  }
});

// {{ AURA-X: Delete - 删除不再使用的shareToFriend函数，改为微信原生分享. Confirmed via 寸止. }}

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 查看会员权益
const viewMemberBenefits = () => {
  navto('/pages/bundle/user/vip');
};
</script>

<template>
  <view class="trial-gift-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-left" @click="goBack">
        <u-icon name="arrow-left" size="22" color="#333"></u-icon>
      </view>
      <view class="navbar-title">
        <text class="title-text">赠送体验会员</text>
      </view>
      <view class="navbar-right"></view>
    </view>

    <!-- 主要内容 -->
    <view class="main-content">
      <!-- 体验券卡片 -->
      <view class="gift-card">
        <view class="card-header">
          <view class="gift-icon">
            <u-icon name="gift" color="#245D3C" size="32"></u-icon>
          </view>
          <text class="card-title">{{ trialInfo.title }}</text>
        </view>
        
        <view class="card-content">
          <view class="days-display">
            <text class="days-number">{{ trialInfo.days }}</text>
            <text class="days-unit">天</text>
          </view>
          <text class="card-description">{{ trialInfo.description }}</text>
        </view>

        <view class="card-footer">
          <text class="validity-text">分享链接有效期：{{ trialInfo.validDays }}天</text>
        </view>
      </view>

      <!-- 会员权益 -->
      <view class="benefits-section">
        <view class="section-title">
          <u-icon name="star" color="#245D3C" size="18"></u-icon>
          <text class="title-text">会员专享权益</text>
        </view>
        <view class="benefits-list">
          <view 
            v-for="(benefit, index) in trialInfo.benefits" 
            :key="index" 
            class="benefit-item"
          >
            <view class="benefit-icon">
              <u-icon name="checkmark" color="#3AEE55" size="16"></u-icon>
            </view>
            <text class="benefit-text">{{ benefit }}</text>
          </view>
        </view>
      </view>

      <!-- 使用说明 -->
      <view class="usage-section">
        <view class="section-title">
          <u-icon name="info-circle" color="#245D3C" size="18"></u-icon>
          <text class="title-text">使用说明</text>
        </view>
        <view class="usage-list">
          <text class="usage-item">• 每个体验券仅限一人领取</text>
          <text class="usage-item">• 仅限未成为过会员的用户领取</text>
          <text class="usage-item">• 体验期结束后可选择续费正式会员</text>
          <text class="usage-item">• 分享链接{{ trialInfo.validDays }}天内有效</text>
        </view>
      </view>
    </view>

    <!-- 底部操作区 -->
    <view class="bottom-actions">
      <view class="action-btn secondary" @click="viewMemberBenefits">
        <text class="btn-text">查看会员权益</text>
      </view>
      <!-- {{ AURA-X: Modify - 改为微信原生分享方式，参考活动详情页面的实现. Confirmed via 寸止. }} -->
      <button class="action-btn primary u-reset-button" type="" open-type="share">
        <text class="btn-text">分享给好友</text>
      </button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.trial-gift-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #C8E6D1 0%, #F8F9FA 50%);
  display: flex;
  flex-direction: column;
}

.custom-navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  height: calc(44px + var(--status-bar-height, 44rpx));
  padding-top: var(--status-bar-height, 44rpx);
  background: rgba(255,255,255,0.6);
  border-bottom: 1px solid rgba(255,255,255,0.8);
  backdrop-filter: blur(12px);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;

  .navbar-left,
  .navbar-right {
    width: 120rpx;
    display: flex;
    align-items: center;
  }

  .navbar-left {
    justify-content: flex-start;
  }

  .navbar-title {
    .title-text {
      font-size: 32rpx;
      font-weight: 600;
      color: #245D3C;
    }
  }
}

.main-content {
  flex: 1;
  padding: 32rpx;
  padding-top: calc(44px + var(--status-bar-height, 44rpx) + 32rpx);
  padding-bottom: 160rpx;
}

.gift-card {
  background: rgba(255,255,255,0.8);
  border: 1px solid rgba(255,255,255,0.9);
  backdrop-filter: blur(12px);
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8px 24px rgba(36, 93, 60, 0.1);

  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 32rpx;

    .gift-icon {
      width: 64rpx;
      height: 64rpx;
      background: linear-gradient(135deg, #A5E1B8, #73C088);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 24rpx;
    }

    .card-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #245D3C;
    }
  }

  .card-content {
    text-align: center;
    margin-bottom: 32rpx;

    .days-display {
      display: flex;
      align-items: baseline;
      justify-content: center;
      margin-bottom: 16rpx;

      .days-number {
        font-size: 72rpx;
        font-weight: 700;
        color: #245D3C;
        line-height: 1;
      }

      .days-unit {
        font-size: 32rpx;
        color: #73C088;
        margin-left: 8rpx;
      }
    }

    .card-description {
      font-size: 28rpx;
      color: #666;
      line-height: 1.5;
    }
  }

  .card-footer {
    text-align: center;
    padding-top: 24rpx;
    border-top: 1px solid rgba(165, 225, 184, 0.3);

    .validity-text {
      font-size: 24rpx;
      color: #999;
    }
  }
}

.benefits-section,
.usage-section {
  background: rgba(255,255,255,0.6);
  border: 1px solid rgba(255,255,255,0.8);
  backdrop-filter: blur(12px);
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;

  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;

    .title-text {
      font-size: 30rpx;
      font-weight: 600;
      color: #245D3C;
      margin-left: 12rpx;
    }
  }
}

.benefits-list {
  .benefit-item {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .benefit-icon {
      width: 32rpx;
      height: 32rpx;
      background: rgba(58, 238, 85, 0.1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16rpx;
    }

    .benefit-text {
      font-size: 28rpx;
      color: #333;
      line-height: 1.5;
    }
  }
}

.usage-list {
  .usage-item {
    display: block;
    font-size: 26rpx;
    color: #666;
    line-height: 1.6;
    margin-bottom: 12rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  background: rgba(255,255,255,0.9);
  border-top: 1px solid rgba(255,255,255,0.8);
  backdrop-filter: blur(12px);
  display: flex;
  gap: 16rpx;

  .action-btn {
    flex: 1;
    height: 88rpx;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 10px rgba(0,0,0,0.05);
    /* {{ AURA-X: Add - 为button元素添加样式重置. Confirmed via 寸止. }} */
    border: none;
    outline: none;
    background: none;
    padding: 0;
    margin: 0;

    &:active {
      transform: scale(0.98);
    }

    &.primary {
      background: linear-gradient(135deg, #3AEE55, #73C088);
      color: #fff;

      &:hover {
        transform: scale(1.02);
        box-shadow: 0 8px 15px rgba(58, 238, 85, 0.3);
      }
    }

    &.secondary {
      background: rgba(255,255,255,0.8);
      border: 1px solid rgba(36, 93, 60, 0.2);
      color: #245D3C;

      &:hover {
        background: rgba(255,255,255,0.9);
        transform: scale(1.02);
      }
    }

    .btn-text {
      font-size: 30rpx;
    }
  }
}
</style>
