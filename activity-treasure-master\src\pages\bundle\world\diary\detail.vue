<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { getDiaryDetail, deleteDiary, editDiary } from '@/api/index.js';
import { store } from '@/store';
import { navto } from '@/utils';
import customNavbar from '@/components/customNavbar.vue';

// 🔧 移除点赞和评论相关导入

// {{ AURA-X: Fix - 添加缺失的变量定义. Confirmed via 寸止 }}
// 状态管理
const diary = ref(null);
const loading = ref(true);
const diaryId = ref('');
const isAuthor = ref(false);

// 基础状态管理
const isTransitioning = ref(false);

// 添加缺失的变量定义
const contentHistory = ref([]);
const currentIndex = ref(0);
const isMember = ref(false);

// 关闭页面方法
const closePage = () => {
  uni.navigateBack();
};

// 检查用户会员状态
const checkMemberStatus = () => {
  const userInfo = store().$state.userInfo;
  if (!userInfo) return false;

  if (userInfo.is_huiyuan == 1) {
    if (!userInfo.huiyuan_end_time || new Date(userInfo.huiyuan_end_time) > new Date()) {
      return true;
    }
  }
  return false;
};

// {{ AURA-X: Fix - 添加页面加载调试信息. Confirmed via 寸止 }}
// 获取URL参数
onLoad((options) => {
  console.log('🚀 日卡详情页面加载，参数:', options);
  console.log('📱 触摸事件已绑定到容器元素');

  if (options.id) {
    diaryId.value = options.id;
    loadDiaryDetail();
  } else {
    uni.showToast({ title: '参数错误', icon: 'none' });
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }
});

// {{ AURA-X: Add - 监听日记更新事件. Confirmed via 寸止. }}
// 监听日记更新事件
uni.$on('diary-updated', (data) => {
  if (data.id === diaryId.value) {
    // 重新加载日记详情
    loadDiaryDetail();
  }
});

// 页面卸载时移除事件监听
onUnmounted(() => {
  uni.$off('diary-updated');
});

// 加载日记详情
const loadDiaryDetail = async () => {
  try {
    loading.value = true;
    
    const res = await getDiaryDetail({
      id: diaryId.value,
      uid: store().$state.userInfo?.uid || 0,
      token: store().$state.userInfo?.token || ''
    });

    if (res.status === 'ok') {
      diary.value = res.data;
      // 检查是否是作者
      isAuthor.value = diary.value.user_id === store().$state.userInfo?.uid;

      // {{ AURA-X: Add - 将原始日卡添加到历史栈. Confirmed via 寸止 }}
      // 将原始日卡添加到历史栈
      const diaryWithType = {
        ...diary.value,
        _type: 'diary'
      };
      contentHistory.value = [diaryWithType];
      currentIndex.value = 0;

      // 检查会员状态
      isMember.value = checkMemberStatus();

    } else {
      uni.showToast({ title: res.msg || '加载失败', icon: 'none' });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  } catch (error) {
    console.error('加载日记详情失败:', error);
    uni.showToast({ title: '加载失败', icon: 'none' });
  } finally {
    loading.value = false;
  }
};

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '';

  const formattedTimeStr = timeStr.replace(/-/g, '/');
  const time = new Date(formattedTimeStr);
  const year = time.getFullYear();
  const month = String(time.getMonth() + 1).padStart(2, '0');
  const day = String(time.getDate()).padStart(2, '0');
  const hours = String(time.getHours()).padStart(2, '0');
  const minutes = String(time.getMinutes()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// {{ AURA-X: Modify - 修复位置信息JSON解析错误，添加HTML实体解码处理. Confirmed via 寸止. }}
// 安全解析位置信息
const getLocationName = (locationStr) => {
  if (!locationStr) return '';

  try {
    // 如果是JSON字符串，尝试解析
    if (typeof locationStr === 'string' && locationStr.startsWith('{')) {
      // 先进行HTML实体解码处理
      let decodedStr = locationStr
        .replace(/&quot;/g, '"')
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&#39;/g, "'");

      const locationObj = JSON.parse(decodedStr);
      return locationObj.name || locationObj.address || '未知位置';
    }
    // 如果是普通字符串，直接返回
    return locationStr;
  } catch (error) {
    console.warn('位置信息解析失败:', error);
    return locationStr || '未知位置';
  }
};

// 编辑日记
const editDiaryAction = () => {
  // {{ AURA-X: Add - 实现日记编辑跳转功能. Confirmed via 寸止. }}
  navto(`/pages/bundle/world/diary/edit?id=${diaryId.value}`);
};

// 删除日记
const deleteDiaryAction = () => {
  uni.showModal({
    title: '确认删除',
    content: '删除后无法恢复，确定要删除这篇日记吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          const result = await deleteDiary({
            id: diaryId.value,
            uid: store().$state.userInfo.uid,
            token: store().$state.userInfo.token
          });
          
          if (result.status === 'ok') {
            uni.showToast({ title: '删除成功', icon: 'success' });
            setTimeout(() => {
              uni.navigateBack();
            }, 1000);
          } else {
            uni.showToast({ title: result.msg || '删除失败', icon: 'none' });
          }
        } catch (error) {
          console.error('删除日记失败:', error);
          uni.showToast({ title: '删除失败', icon: 'none' });
        }
      }
    }
  });
};

// 预览图片
const previewImage = (current, images) => {
  uni.previewImage({
    current: current,
    urls: images
  });
};

// 移除了摘录相关功能

// 移除了上滑切换功能

// 移除了下拉回看功能

// 移除了触摸事件处理

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<template>
  <!-- {{ AURA-X: Fix - 恢复日记详情页面为简单布局. Confirmed via 寸止 }} -->
  <view class="diary-detail-container">
    <!-- 顶部导航栏 -->
    <view class="diary-header">
      <view class="back-button" @click="closePage">
        <u-icon name="arrow-left" color="#333" size="20"></u-icon>
      </view>
      <text class="header-title">日记详情</text>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-state">
      <u-loading-icon mode="circle" size="30" color="#ffffff"></u-loading-icon>
    </view>

    <!-- {{ AURA-X: Modify - 修改为全屏背景图片布局，去除卡片容器. Confirmed via 寸止 }} -->
    <!-- 背景图片 -->
    <view v-if="diary" class="background-image">
      <image
        v-if="diary.images && diary.images.length > 0"
        :src="diary.images[0]"
        class="full-background-image"
        mode="aspectFill"
      ></image>
      <view v-else class="white-background"></view>
    </view>

    <!-- {{ AURA-X: Modify - 重新布局用户信息和内容，文本移到用户信息下方. Confirmed via 寸止 }} -->
    <!-- 内容区域 - 垂直布局 -->
    <view v-if="diary" class="content-overlay">
      <!-- 用户信息 - 顶部 -->
      <view class="user-info-top">
        <image class="user-avatar-small" :src="diary.user?.avatar_url || '/static/default-avatar.png'" mode="aspectFill"></image>
        <view class="user-meta-overlay">
          <text class="user-nickname-overlay">{{ diary.user?.nickname || '用户' }}</text>
          <text class="post-time-overlay">{{ formatTime(diary.created_at) }}</text>
        </view>
        <!-- 编辑和删除按钮，仅在当前用户是内容创建者时显示 -->
        <view v-if="isAuthor" class="action-buttons-overlay">
          <view class="edit-btn-overlay" @click="editDiaryAction">
            <u-icon name="edit" color="#6AC086" size="16"></u-icon>
          </view>
          <view class="delete-btn-overlay" @click="deleteDiaryAction">
            <u-icon name="trash" color="#666" size="16"></u-icon>
          </view>
        </view>
      </view>

      <!-- 内容文字 - 用户信息下方 -->
      <view class="content-text-below" :class="{ 'has-background': diary.images && diary.images.length > 0 }">
        <text class="diary-text-overlay">{{ diary.content }}</text>

        <!-- 位置信息 - 移到文本内容蒙版内的右下方 -->
        <view v-if="diary.location" class="location-info-inline">
          <u-icon name="map" size="14" :color="diary.images && diary.images.length > 0 ? '#ffffff' : '#666'"></u-icon>
          <text class="location-text-inline" :class="{ 'white-text': diary.images && diary.images.length > 0 }">{{ getLocationName(diary.location) }}</text>
        </view>
      </view>

      <!-- 标签 -->
      <view v-if="diary.tags" class="tags-container-overlay">
        <view
          v-for="tag in diary.tags.split(',')"
          :key="tag"
          class="tag-item-overlay"
          :class="{ 'has-background': diary.images && diary.images.length > 0 }"
        >
          <text class="tag-text-overlay" :class="{ 'white-text': diary.images && diary.images.length > 0 }">#{{ tag.trim() }}</text>
        </view>
      </view>

      <!-- 图片列表 - 横排显示所有图片 -->
      <view v-if="diary.images && diary.images.length > 0" class="images-horizontal">
        <view
          v-for="(img, index) in diary.images"
          :key="index"
          class="image-item-horizontal"
          @click="previewImage(img, diary.images)"
        >
          <image :src="img" mode="aspectFill" class="diary-image-horizontal"></image>
        </view>
      </view>
    </view>

    <!-- 错误状态 -->
    <view v-else class="error-state">
      <u-icon name="warning" size="40" color="#999"></u-icon>
      <text class="error-text">加载失败，请重试</text>
      <view class="retry-button" @click="loadDiaryDetail">
        <text class="retry-text">重新加载</text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
/* {{ AURA-X: Modify - 修改日记详情页面样式，与日卡详情页面保持一致. Confirmed via 寸止 }} */
@import '@/style/judu-theme.scss';

/* {{ AURA-X: Modify - 修改为全屏背景图片布局样式. Confirmed via 寸止 }} */
.diary-detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  position: relative;
  background: #ffffff;
  overflow: hidden;
  animation: fadeIn 0.4s ease-out forwards;
}

/* 背景图片样式 */
.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.full-background-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.white-background {
  width: 100%;
  height: 100%;
  background: #ffffff;
}

.diary-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 60rpx 32rpx 20rpx 32rpx;
  background: transparent;
  z-index: 100;
}

.back-button {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx);
  transition: background-color 0.3s ease;

  &:active {
    background: rgba(255, 255, 255, 0.3);
  }
}

.header-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

/* {{ AURA-X: Modify - 重新设计内容覆盖层布局. Confirmed via 寸止 }} */
/* 内容覆盖层 - 垂直布局 */
.content-overlay {
  position: absolute;
  top: 140rpx;
  left: 40rpx;
  right: 40rpx;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* 用户信息 - 顶部 */
.user-info-top {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  padding: 16rpx 24rpx;
  border-radius: 50rpx;
  backdrop-filter: blur(10rpx);
  align-self: flex-start;
  max-width: 500rpx;
}

.user-avatar-small {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.user-meta-overlay {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.user-nickname-overlay {
  font-size: 26rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1.2;
}

.post-time-overlay {
  font-size: 22rpx;
  color: #666666;
  margin-top: 4rpx;
}

/* {{ AURA-X: Add - 添加操作按钮容器和编辑按钮样式. Confirmed via 寸止. }} */
.action-buttons-overlay {
  display: flex;
  align-items: center;
  margin-left: 16rpx;
}

.edit-btn-overlay {
  margin-right: 8rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
  transition: background-color 0.3s ease;

  &:active {
    background: rgba(255, 255, 255, 1);
  }
}

.delete-btn-overlay {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
  transition: background-color 0.3s ease;

  &:active {
    background: rgba(255, 255, 255, 1);
  }
}

.loading-state {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

/* 内容文字样式 */
.content-text-below {
  padding: 32rpx 40rpx 50rpx 40rpx; /* 底部增加更多内边距为地址信息留空间 */
  border-radius: 20rpx;
  position: relative; /* 为内联地址信息提供定位基准 */
  min-height: 120rpx; /* 设置最小高度，确保蒙版有足够高度 */
  /* 移除固定高度，让内容自适应 */
  width: 100%;
  box-sizing: border-box;

  /* 无背景图片时的样式 */
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);

  /* 有背景图片时的样式 */
  &.has-background {
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(10rpx);
  }
}

/* 日记文本样式 */
.diary-text-overlay {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap; /* 保持换行符 */
  display: block;
  width: 100%;
}

/* 有背景图片时的文本颜色 */
.content-text-below.has-background .diary-text-overlay {
  color: #ffffff;
}

.diary-text-overlay {
  font-size: 32rpx;
  line-height: 1.6;
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  letter-spacing: 1rpx;
  font-weight: 400;

  /* 默认深色文字 */
  color: #333333;

  /* 有背景图片时的白色文字 */
  .has-background & {
    color: #ffffff;
    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  }
}

/* 内联位置信息样式 - 位于文本内容右下方 */
.location-info-inline {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  display: flex;
  align-items: center;
  gap: 6rpx;
  /* 去除背景容器，直接显示在文本蒙版内 */
}

.location-text-inline {
  font-size: 22rpx;
  color: #666666;
  opacity: 0.8;

  &.white-text {
    color: #ffffff;
    text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.3);
  }
}

/* 保留原有位置信息样式作为备用 */
.location-info-overlay {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border-radius: 50rpx;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
  align-self: flex-start;
}

.location-text-overlay {
  font-size: 24rpx;
  color: #666666;

  &.white-text {
    color: #ffffff;
  }
}

/* 标签样式 */
.tags-container-overlay {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.tag-item-overlay {
  padding: 8rpx 16rpx;
  border-radius: 50rpx;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);

  &.has-background {
    background: rgba(0, 0, 0, 0.3);
  }
}

.tag-text-overlay {
  font-size: 22rpx;
  color: #666666;
  font-weight: 500;

  &.white-text {
    color: #ffffff;
  }
}

/* 横排图片样式 */
.images-horizontal {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: 8rpx;
}

.image-item-horizontal {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5rpx);
}

.diary-image-horizontal {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* {{ AURA-X: Delete - 删除旧的图片网格、位置信息和标签样式. Confirmed via 寸止 }} */

.error-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 32rpx;
}

.error-text {
  font-size: 28rpx;
  color: #999999;
}

.retry-button {
  background-color: #6AC086;
  border-radius: 50rpx;
  padding: 24rpx 48rpx;

  &:active {
    background-color: #5a9c73;
  }
}

.retry-text {
  font-size: 28rpx;
  color: #ffffff;
}

// {{ AURA-X: Add - 添加双向滑动功能的样式. Confirmed via 寸止 }}
.content-indicator {
  display: flex;
  justify-content: center;
  gap: 32rpx;
  margin-top: 40rpx;
  padding: 20rpx 0;
}

.indicator-item {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  background: #f5f5f5;
  transition: all 0.3s ease;

  &.active {
    background: #6AC086;

    .indicator-text {
      color: #ffffff;
    }
  }
}

.indicator-text {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.swipe-hints {
  margin-top: 32rpx;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  text-align: center;
}

.hint-item {
  margin-bottom: 16rpx;

  &:last-child {
    margin-bottom: 0;
  }

  &.limit {
    .hint-text {
      color: #ff6b6b;
    }
  }
}

// 移除了摘录相关样式

</style>
