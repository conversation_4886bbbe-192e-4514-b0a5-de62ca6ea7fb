<?php
namespace controller;
use core\Controller;
use core\Db;
class China extends Controller{

	public function __construct(){
		parent::__construct();
	}
	
	static public function get_js($type=1){
		dbConn();

		try {
			// {{ AURA-X: Modify - 只使用高德数据表，移除旧china表回退逻辑. Confirmed via 寸止. }}
			// 直接使用高德数据表
			$res = Db()->_fetchAll("SELECT adcode as id, parent_adcode as pid, name FROM gaode_district ORDER BY adcode");
			$data = self::get_gaode_children('0',$res);
			foreach($data as &$sheng){
				$sheng['children'] = self::get_gaode_children($sheng['id'],$res);
				foreach($sheng['children'] as &$shi){
					$shi['children'] =  self::get_gaode_children($shi['id'],$res);
				}
			}
		} catch (\Throwable $e) {
			// 如果查询失败，返回空数组
			$data = [];
		}
		responseType("json");
		header("Content-Type: application/json; charset=utf-8");
		if($type == 1){//只有一个省市区数组对象
			return $data;
		}else if($type == 2){//自动联动
			$zhushi = <<<EOF
/*
标签固定id，自动联动
<select id="area_sheng"></select>
<select id="area_shi"></select>
<select id="area_qu"></select>
自动填充中文
<input type="hidden" name="sheng_name" id="sheng_name" value="" />
<input type="hidden" name="shi_name" id="shi_name" value="" />
<input type="hidden" name="qu_name" id="qu_name" value="" />
*/

EOF;
		$data = json_encode($data);
		$func_js = <<<EOF

function china_init(){
	var str = "";
	for(var i=0;i<china_area.length;i++){
		str += '<option value="'+china_area[i]['id']+'">'+china_area[i]['name']+'</option>';
	};
	$("#area_sheng").empty();
	$("#area_sheng").append(str);
	$("#area_sheng").change();
}
$(document).ready(function(){
	//
	$("#area_sheng").change(function(){
		var sheng_id = $(this).val();
		for(i=0;i<china_area.length;i++){
			if(china_area[i]['id'] == sheng_id){
				var shi_arr = china_area[i]['children'];
				var str = "";
				for(j=0;j<shi_arr.length;j++){
					str += '<option value="'+shi_arr[j]['id']+'">'+shi_arr[j]['name']+'</option>';
				};
				$("#area_shi").empty();
				$("#area_shi").append(str);
				break;
			}
		}
		$("#area_shi").change();
	});
	//
	$("#area_shi").change(function(){
		var sheng_id = $("#area_sheng").val();
		var shi_id = $("#area_shi").val();
		for(var i=0;i<china_area.length;i++){
			if(china_area[i]['id'] == sheng_id){
				var shi_arr = china_area[i]['children'];
				for(var j=0;j<shi_arr.length;j++){
					if(shi_arr[j]['id'] == shi_id){
						//console.log(shi_arr[j]['children']);
						
						var qu_arr = shi_arr[j]['children'];
						var str = "";
						for(var k=0;k<qu_arr.length;k++){
							str += '<option value="'+qu_arr[k]['id']+'">'+qu_arr[k]['name']+'</option>';
						};
						$("#area_qu").empty();
						$("#area_qu").append(str);
						
						break;						
					}
				}

			}
		}
		$("#area_qu").change();
	});
	$("#area_qu").change(function(){
		idToName();
	});
	//
	china_init();
});
function idToName(){
	var sheng_id = $("#area_sheng").val();
	var shi_id = $("#area_shi").val();	
	var qu_id = $("#area_qu").val();
	var sheng_name = "";
	var shi_name = "";
	var qu_name = "";
	for(var i=0;i<china_area.length;i++){
		if(china_area[i]['id'] == sheng_id){
			sheng_name = china_area[i]['name'];
			var shi_arr = china_area[i]['children'];
			for(var j=0;j<shi_arr.length;j++){
				if(shi_arr[j]['id'] == shi_id){
					shi_name = shi_arr[j]['name'];
					var qu_arr = shi_arr[j]['children'];
					for(var k=0;k<qu_arr.length;k++){
						if(qu_arr[k]['id'] == qu_id){
							qu_name = qu_arr[k]['name'];
							break;
						}
					};			
				}
			}
		}
	}
	$("#sheng_name").val(sheng_name);
	$("#shi_name").val(shi_name);
	$("#qu_name").val(qu_name);
}		
EOF;
			echo $zhushi . "const china_area = ".$data .";".$func_js;
		}else if($type == 3){//调试模式
			echo json_encode($data,JSON_PRETTY_PRINT|JSON_UNESCAPED_UNICODE);
		}else{
			echo json_encode(["status"=>"error","msg"=>"请选择类型"]);
		}
		exit;
	}
	
	static private function get_children($pid,$data){
		$res = [];
		foreach($data as $row){
			if($row['pid'] == $pid){
				$res[] = [
					"id"=>$row['id'],
					"name"=>$row['name'],
				];
			}
		}
		return $res;
	}

	/**
	 * 获取高德数据的子级数据
	 * @param string $pid 父级adcode
	 * @param array $data 所有数据
	 * @return array
	 */
	static private function get_gaode_children($pid,$data){
		$res = [];
		foreach($data as $row){
			if($row['pid'] == $pid){
				$res[] = [
					"id"=>$row['id'],
					"name"=>$row['name'],
				];
			}
		}
		return $res;
	}

	public function _empty(){

	}
	function __destruct(){

	}
}
