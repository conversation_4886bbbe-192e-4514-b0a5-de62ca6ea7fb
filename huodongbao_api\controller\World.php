<?php
/**
 * 世界模块控制器
 */

namespace controller;

use core\Controller;
use core\Db; // 引入 Huodong.php 中使用的 Db 类

class World extends Controller
{

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 记录世界模块相关错误日志
     * @param string $message 错误消息
     * @param array $business_data 业务相关数据
     * @param string $level 日志级别 (ERROR, WARNING, INFO)
     */

	private function logWriteError($message, $business_data = [], $level = 'ERROR') {
		// 使用正确的日志路径
		$log_file = '/home/<USER>/world_errors_' . date('Ymd') . '.log';

		try {

			// 检查并创建目录
			$log_dir = dirname($log_file);
			if (!is_dir($log_dir)) {
				// 尝试创建目录，使用更宽松的权限
				if (!mkdir($log_dir, 0777, true)) {
					// 如果创建失败，尝试使用备用路径
					$log_file = '/tmp/world_errors_' . date('Ymd') . '.log';
					$log_dir = dirname($log_file);
				} else {
					// 创建成功后设置权限
					chmod($log_dir, 0777);
				}
			}

			// 检查并创建文件
			if (!file_exists($log_file)) {
				if (!touch($log_file)) {
					// 如果创建失败，使用备用路径
					$log_file = '/tmp/world_errors_' . date('Ymd') . '.log';
					touch($log_file);
				} else {
					// 创建成功后设置权限
					chmod($log_file, 0666);
				}
			}


			// 检测当前执行用户身份（在目录创建后）
			$user_diagnosis = [];

			if (function_exists('posix_geteuid')) {
				$euid = posix_geteuid();
				$egid = posix_getegid();
				$current_user_info = posix_getpwuid($euid);
				$current_group_info = posix_getgrgid($egid);

				$user_diagnosis = [
					'effective_uid' => $euid,
					'effective_gid' => $egid,
					'username' => $current_user_info['name'] ?? 'unknown',
					'home_dir' => $current_user_info['dir'] ?? 'unknown',
					'shell' => $current_user_info['shell'] ?? 'unknown',
					'group_name' => $current_group_info['name'] ?? 'unknown',
					'is_root' => ($euid === 0),
					'log_file_path' => $log_file,
					'log_dir_exists' => is_dir(dirname($log_file)),
					'log_dir_writable' => is_writable(dirname($log_file)),
					'log_file_exists' => file_exists($log_file),
					'log_file_writable' => file_exists($log_file) ? is_writable($log_file) : 'file_not_exists'
				];
			} else {
				$user_diagnosis['posix_functions'] = 'not_available';
			}

			// 构建日志条目
			$log_entry = [
				'timestamp' => date('Y-m-d H:i:s'),
				'level' => $level,
				'message' => $message,
				'business_data' => $business_data,
				'user_diagnosis' => $user_diagnosis,
				'server_info' => [
					'php_version' => PHP_VERSION,
					'server_time' => date('c'),
					'memory_usage' => memory_get_usage(true),
					'request_uri' => $_SERVER['REQUEST_URI'] ?? 'CLI',
					'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
				]
			];

			// 格式化为JSON
			$json_log = json_encode($log_entry, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n" . str_repeat('-', 80) . "\n";

			// 直接写入，与test_log_simple.php保持一致
			$write_result = file_put_contents($log_file, $json_log, FILE_APPEND | LOCK_EX);

			if ($write_result === false) {

				// 写入失败，但不再调用exception_log避免循环
			}

		} catch (\Throwable $e) {
			try {

				// 记录异常但不调用exception_log避免循环
			} catch (\Throwable $e2) {
				// 最后的备用方案：写入到临时文件
				$temp_file = sys_get_temp_dir() . '/world_errors_backup_' . date('Ymd') . '.log';
				$backup_content = $json_log ?? "World logWriteError异常: " . $e->getMessage();
				if (isset($user_diagnosis)) {
					$backup_content .= " | 权限诊断: " . json_encode($user_diagnosis, JSON_UNESCAPED_UNICODE);
				}
				@file_put_contents($temp_file, $backup_content, FILE_APPEND | LOCK_EX);
			}
		}
	}

    /**
     * 构建日卡查询的FROM和SELECT子句
     * @param int $userId 用户ID，用于获取点赞和收藏状态
     * @return array ['from' => string, 'select' => string, 'params' => array]
     */
    private function buildCardQueryClauses($userId = 0)
    {
        $fromClause = 'daily_cards';
        $selectFields = 'daily_cards.id, daily_cards.card_date as date, daily_cards.description, daily_cards.author, daily_cards.background_image_url, daily_cards.like_count as likeCount, daily_cards.comment_count as commentCount';
        $params = [];

        if ($userId > 0) {
            $fromClause .= ' LEFT JOIN user_card_likes ucl ON ucl.card_id = daily_cards.id AND ucl.user_id = :userId';
            $fromClause .= ' LEFT JOIN user_favorites uf ON uf.item_id = daily_cards.id AND uf.item_type = "card" AND uf.user_id = :userId';
            $selectFields .= ', IF(ucl.id IS NOT NULL, 1, 0) as isLiked, IF(uf.id IS NOT NULL, 1, 0) as isFavorited';
            $params[':userId'] = $userId;
        } else {
            $selectFields .= ', 0 as isLiked, 0 as isFavorited';
        }

        return ['from' => $fromClause, 'select' => $selectFields, 'params' => $params];
    }

    /**
     * 构建动态查询的FROM和SELECT子句
     * @param int $userId 用户ID，用于获取点赞和收藏状态
     * @return array ['from' => string, 'select' => string, 'params' => array]
     */
    private function buildFeedQueryClauses($userId = 0)
    {
        $fromClause = 'feeds LEFT JOIN user u ON u.uid = feeds.user_id';
        $selectFields = 'feeds.id, feeds.user_id, feeds.content, feeds.images_json, feeds.location, feeds.tags, feeds.like_count as likeCount, feeds.comment_count as commentCount, feeds.created_at, u.nickname, u.avatar as avatar_url';
        $params = [];

        if ($userId > 0) {
            $fromClause .= ' LEFT JOIN user_feed_likes ufl ON ufl.feed_id = feeds.id AND ufl.user_id = :userId';
            $fromClause .= ' LEFT JOIN user_favorites uf ON uf.item_id = feeds.id AND uf.item_type = "feed" AND uf.user_id = :userId';
            $selectFields .= ', IF(ufl.id IS NOT NULL, 1, 0) as isLiked, IF(uf.id IS NOT NULL, 1, 0) as isFavorited';
            $params[':userId'] = $userId;
        } else {
            $selectFields .= ', 0 as isLiked, 0 as isFavorited';
        }

        return ['from' => $fromClause, 'select' => $selectFields, 'params' => $params];
    }

  
    /**
     * @apiName 获取指定日期范围的日卡数据
     * @method get_daily_cards
     * @POST
     * @param uid int 用户ID (可选, 用于获取点赞状态)
     * @param token string token (可选, 用于验证用户)
     * @param startDate string 开始日期 YYYY-MM-DD
     * @param endDate string 结束日期 YYYY-MM-DD
     * @return {"status":"ok","data":{"cards":[...]} | {"status":"error","msg":"..."} | {"status":"empty"}
     */
    public function get_daily_cards($startDate, $endDate, $uid = 0, $token = "")
    {
        // 参数验证
        if (
            empty($startDate) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $startDate) ||
            empty($endDate) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $endDate)
        ) {
            return ["status" => "error", "msg" => "日期参数错误"];
        }

        // (可选) 用户验证，用于获取点赞状态
        $userId = 0;
        if (!empty($uid) && !empty($token)) {
             if (!check($uid, "intgt0") || strlen($token) != 32) {
                 // 不严格要求验证，未登录或验证失败则不显示点赞状态
             } else if ($this->auth($uid, $token)) {
                 $userId = (int)$uid;
             }
        }


        // (可选) 限制日期范围
        $startTs = strtotime($startDate);
        $endTs = strtotime($endDate);
        if ($endTs - $startTs > 30 * 86400) { // 例如限制最大查询 31 天
           $endDate = date('Y-m-d', $startTs + 30 * 86400);
        }

        dbConn(); // 确保数据库连接
        try {
            // 使用私有方法构建查询子句
            $queryClauses = $this->buildCardQueryClauses($userId);

            // 合并参数
            $allParams = array_merge([":startDate" => $startDate, ":endDate" => $endDate], $queryClauses['params']);

            // 执行查询
            $cards = Db()->table($queryClauses['from'])
                ->select($queryClauses['select'])
                ->where("daily_cards.card_date >= :startDate AND daily_cards.card_date <= :endDate") // 明确指定表名
                ->order('daily_cards.card_date', 'asc') // 明确指定表名
                ->prepareParam($allParams)
                ->fetchAll();

            if (empty($cards)) {
                return ["status" => "empty", "msg" => "指定日期范围无数据"];
            }

            return ["status" => "ok", "data" => ["cards" => $cards]];

        } catch (\Throwable $e) {
            $this->logWriteError("获取日卡数据失败", [
                'exception_message' => $e->getMessage(),
                'operation' => 'get_daily_cards'
            ]);
            return ["status" => "error", "msg" => "获取日卡数据失败"];
        }
    }

    /**
     * @apiName 获取单张日卡详情
     * @method get_card_detail
     * @POST
     * @param id int 日卡ID
     * @param uid int 用户ID (可选, 用于获取点赞状态)
     * @param token string token (可选)
     * @return {"status":"ok","data":{...}} | {"status":"error","msg":"..."} | {"status":"empty"}
     */
    public function get_card_detail($id, $uid = 0, $token = "")
    {
        if (!check($id, "intgt0")) {
            return ["status" => "error", "msg" => "无效的日卡ID"];
        }
        $cardId = (int)$id;

        $userId = 0;
        if (!empty($uid) && !empty($token)) {
            if (!check($uid, "intgt0") || strlen($token) != 32) {
                // fail silently
            } else if ($this->auth($uid, $token)) {
                $userId = (int)$uid;
            }
        }

        dbConn();
        try {
            // 使用私有方法构建查询子句
            $queryClauses = $this->buildCardQueryClauses($userId);

            // 合并参数
            $allParams = array_merge([':cardId' => $cardId], $queryClauses['params']);

            $card = Db()->table($queryClauses['from'])
                ->select($queryClauses['select'])
                ->where('daily_cards.id = :cardId')
                ->prepareParam($allParams)
                ->fetch();

            if (empty($card)) {
                return ["status" => "empty", "msg" => "日卡不存在"];
            }

            // 未来可能需要关联评论信息等

            return ["status" => "ok", "data" => $card];

        } catch (\Throwable $e) {
            $this->logWriteError("获取日卡详情失败", [
                'exception_message' => $e->getMessage(),
                'operation' => 'get_daily_card_detail'
            ]);
            return ["status" => "error", "msg" => "获取日卡详情失败"];
        }
    }

    /**
     * @apiName 获取卡片列表 (分页)
     * @method get_cards
     * @POST
     * @param uid int 用户ID (可选)
     * @param token string token (可选)
     * @param page int 页码 (默认 1)
     * @param page_size int 每页数量 (默认 10)
     * @return {"status":"ok","data":{"list":[...], "total": N}} | {"status":"error","msg":"..."} | {"status":"empty"}
     */
    public function get_cards($page = 1, $page_size = 10, $uid = 0, $token = "")
    {
        if (!check($page, "intgt0") || !check($page_size, "intgt0") || $page_size > 100) {
             return ["status" => "error", "msg" => "分页参数错误"];
        }
        $page = (int)$page;
        $page_size = (int)$page_size;

        $userId = 0;
        if (!empty($uid) && !empty($token)) {
             if (!check($uid, "intgt0") || strlen($token) != 32) {
                 // fail silently
             } else if ($this->auth($uid, $token)) {
                 $userId = (int)$uid;
             }
        }

        dbConn();
        try {
            $queryBase = Db()->table('daily_cards')
                         ->order('card_date', 'desc'); // 假设按日期倒序

            // 克隆查询对象用于计算总数 (在应用 JOIN 和 SELECT 之前)
            $countQuery = clone $queryBase;
            $total = $countQuery->count(); // 获取总数

            // 使用私有方法构建查询子句
            $queryClauses = $this->buildCardQueryClauses($userId);

            // 重新构建查询，因为需要使用JOIN
            if ($userId > 0) {
                $queryBase = Db()->table($queryClauses['from'])
                           ->select($queryClauses['select'])
                           ->order('daily_cards.card_date', 'desc');
            } else {
                $queryBase->select('id, card_date as date, description, author, background_image_url, like_count as likeCount, comment_count as commentCount, 0 as isLiked, 0 as isFavorited');
            }

            // 最后调用 page (它会使用 $queryBase 当前的 SELECT 设置并执行查询)
            $cards = $queryBase->page($page, $page_size); // page 方法现在会获取正确的字段

            if (empty($cards)) {
                return ["status" => "empty", "msg" => "暂无卡片数据"];
            }

            // 注意：Db::page 返回的是 fetchAll 的结果，所以不需要再调用 fetchAll
            // $cards = $query->fetchAll(); // 这行是多余的，因为 page 内部处理了

            return ["status" => "ok", "data" => ["list" => $cards, "total" => \core\Page::$count ?? $total]]; // 使用 \core\Page::$count

        } catch (\Throwable $e) {
            $this->logWriteError("获取卡片列表失败", [
                'exception_message' => $e->getMessage(),
                'operation' => 'get_cards_list'
            ]);
            return ["status" => "error", "msg" => "获取卡片列表失败"];
        }
    }

    /**
     * @apiName 获取动态/日记列表 (分页)
     * @method get_feeds
     * @POST
     * @param uid int 用户ID (可选, 用于获取点赞状态)
     * @param token string token (可选)
     * @param page int 页码 (默认 1)
     * @param page_size int 每页数量 (默认 10)
     * @param user_id int 要筛选的用户ID (可选, 0 表示不筛选)
     * @param type string 类型筛选 ('feed', 'diary', 'all') (可选, 默认 'feed')
     * @return {"status":"ok","data":{"list":[...], "total": N}} | {"status":"error","msg":"..."} | {"status":"empty"}
     */
    public function get_feeds($page = 1, $page_size = 10, $uid = 0, $token = "", $user_id = 0, $filter_type = 'latest', $type = 'feed')
    {
        // {{ AURA-X: Add - 从REQUEST中获取category参数，兼容前端传参. Confirmed via 寸止 }}
        if (isset($_REQUEST['category'])) {
            $filter_type = $_REQUEST['category'];
        }

        if (!check($page, "intgt0") || !check($page_size, "intgt0") || $page_size > 100) {
             return ["status" => "error", "msg" => "分页参数错误"];
        }
         if (!check($user_id, "integt0")) { // 允许为 0
             return ["status" => "error", "msg" => "用户筛选参数错误"];
        }
        $page = (int)$page;
        $page_size = (int)$page_size;
        $filterUserId = (int)$user_id;

        $currentUserId = 0;
        if (!empty($uid) && !empty($token)) {
             if (!check($uid, "intgt0") || strlen($token) != 32) {
                // fail silently
             } else if ($this->auth($uid, $token)) {
                 $currentUserId = (int)$uid;
             }
        }

        dbConn();
        try {


            // 使用私有方法构建查询子句
            $queryClauses = $this->buildFeedQueryClauses($currentUserId);

            // --- Apply WHERE condition ---
            $where = "1";
            $prepareParam = []; // Initialize prepareParam array
            $baseWhereConditions = []; // Array to hold actual WHERE conditions

            if ($filter_type === 'following') {
                if ($currentUserId <= 0) {
                    return ["status" => "relogin", "msg" => "请先登录以查看关注内容"];
                }
                $followingUids = Db()->table('user_guanzhu')
                                      ->where('uid = :currentUserId')
                                      ->prepareParam([':currentUserId' => $currentUserId])
                                      ->fetchAll();
                if (empty($followingUids)) {
                    return ["status" => "empty", "msg" => "您还没有关注任何人"];
                }
                $followingUidList = array_column($followingUids, 'to_uid');
                if (empty($followingUidList)) {
                     return ["status" => "empty", "msg" => "您关注的人还没有发布内容"];
                }
                $followParamIndex = 0;
                $followingPlaceholdersNamed = [];
                foreach ($followingUidList as $followUid) {
                    $paramName = ':followUid' . $followParamIndex++;
                    $followingPlaceholdersNamed[] = $paramName;
                    $prepareParam[$paramName] = $followUid;
                }
                // $where .= " AND feeds.user_id IN (" . implode(',', $followingPlaceholdersNamed) . ")"; // Removed direct modification
                 if (!empty($followingPlaceholdersNamed)) {
                    $baseWhereConditions[] = "feeds.user_id IN (" . implode(',', $followingPlaceholdersNamed) . ")";
                }

            } elseif ($filterUserId > 0) {
                // Filter by a specific user_id
                // $where .= " AND feeds.user_id = :filterUserId"; // Removed direct modification
                $baseWhereConditions[] = "feeds.user_id = :filterUserId";
                $prepareParam[':filterUserId'] = $filterUserId;
            }
            // Always add status filter
            $baseWhereConditions[] = "feeds.status = 'published'";

            // Add type filter
            if ($type !== 'all') {
                $baseWhereConditions[] = "feeds.type = :type";
                $prepareParam[':type'] = $type;
            }

            // Build WHERE clause string from conditions
            $whereClauseString = "";
            if (!empty($baseWhereConditions)) {
                 $whereClauseString = " WHERE " . implode(" AND ", $baseWhereConditions);
                 // Update $where for main query SQL string (used later)
                 $where = $whereClauseString;
                 // Note: If $where started as "1", this replaces it.
            } else {
                 // Should not happen now because status is always added
                 $where = ""; // No WHERE clause if no conditions
            }


            // --- Determine Order ---
            // 根据分类参数决定排序方式 - 支持多种参数传递方式
            $categoryParam = null;
            if (isset($_REQUEST['category'])) {
                $categoryParam = $_REQUEST['category'];
            } elseif (isset($_POST['category'])) {
                $categoryParam = $_POST['category'];
            } elseif ($filter_type === 'hot') {
                $categoryParam = 'hot';
            }

            if ($categoryParam === 'hot') {
                // 最热排序：按点赞数和评论数总和降序，再按创建时间降序
                $orderByClause = "ORDER BY (feeds.like_count + feeds.comment_count) DESC, feeds.created_at DESC";

            } else {
                // 默认最新排序：按创建时间降序
                $orderByClause = "ORDER BY feeds.created_at DESC";

            }

            // --- Calculate Total Count ---
            // Use the constructed $whereClauseString directly
            $countSql = "SELECT COUNT(1) as totalCount FROM feeds" . $whereClauseString;

            // Pass $prepareParam, it's needed if filters were applied
            $countResult = Db::_fetch($countSql, $prepareParam);
            $total = $countResult ? (int)$countResult['totalCount'] : 0;

            // --- End Count Calculation ---

            // --- Calculate LIMIT ---
            $limitOffset = ($page - 1) * $page_size;
            $limitRowCount = $page_size;
            $limitClause = "LIMIT {$limitOffset}, {$limitRowCount}";

            // --- Construct the final SQL manually ---
            // Use the final $where string which includes the WHERE keyword if needed
            $sql = "SELECT {$queryClauses['select']} FROM {$queryClauses['from']}{$where} {$orderByClause} {$limitClause}";


            // 合并查询参数
            $allParams = array_merge($prepareParam, $queryClauses['params']);

            // --- Execute the prepared query ---
            $feeds = Db::_fetchAll($sql, $allParams); // Pass allParams to _fetchAll
            $this->logWriteError("分页查询执行完成", [
                'feeds_count' => count($feeds),
                'operation' => 'get_feeds_query'
            ], 'INFO');

            // --- Process Results ---
            if (empty($feeds)) {
                // Adjust empty message based on filter type
                $emptyMsg = "暂无日记数据";
                if ($filter_type === 'following') {
                    $emptyMsg = "您关注的人还没有发布内容";
                } else if ($filterUserId > 0) {
                     $emptyMsg = "该用户还没有发布内容";
                }
                return ["status" => "empty", "msg" => $emptyMsg];
            }

            foreach ($feeds as &$feed) {
                $feed['images'] = !empty($feed['images_json']) ? json_decode($feed['images_json'], true) : [];
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $this->logWriteError("JSON解析错误", [
                        'feed_id' => $feed['id'],
                        'json_error' => json_last_error_msg(),
                        'operation' => 'parse_feed_images'
                    ]);
                    $feed['images'] = [];
                }
                unset($feed['images_json']);
                $feed['user'] = [
                    'uid' => $feed['user_id'],
                    'nickname' => $feed['nickname'],
                    'avatar_url' => $feed['avatar_url']
                ];
                unset($feed['nickname'], $feed['avatar_url']);
            }

            // Return data with manually obtained total
            return ["status" => "ok", "data" => ["list" => $feeds, "total" => $total]];

        } catch (\Throwable $e) {
            $errorMessage = $e->getMessage();
            $errorTrace = $e->getTraceAsString();
            // Log the last attempted SQL if available
            $lastSql = is_array(Db::$lastSql) ? json_encode(Db::$lastSql) : Db::$lastSql;
            $this->logWriteError("获取动态列表失败", [
                'error_message' => $errorMessage,
                'exception_message' => $e->getMessage(),
                'last_sql' => $lastSql,
                'trace' => $errorTrace,
                'operation' => 'get_feeds'
            ]);
            // Restore standard user-facing error message
            return ["status" => "error", "msg" => "获取日记列表失败，请稍后重试"];
        }
    }


    /**
     * @apiName 获取日记详情
     * @method get_feed_detail
     * @POST
     * @param id int 日记ID
     * @param uid int 用户ID (可选)
     * @param token string token (可选)
     * @return {"status":"ok","data":{...}} | {"status":"error","msg":"..."} | {"status":"empty"}
     */
    public function get_feed_detail($id, $uid = 0, $token = "")
    {
        if (!check($id, "intgt0")) {
             return ["status" => "error", "msg" => "无效的日记ID"];
        }
        $feedId = (int)$id;

         $currentUserId = 0;
        if (!empty($uid) && !empty($token)) {
             if (!check($uid, "intgt0") || strlen($token) != 32) {
                // fail silently
             } else if ($this->auth($uid, $token)) {
                 $currentUserId = (int)$uid;
             }
        }

        dbConn();
        try {
            // 使用私有方法构建查询子句
            $queryClauses = $this->buildFeedQueryClauses($currentUserId);

            // 合并参数
            $allParams = array_merge([':feedId' => $feedId], $queryClauses['params']);

            // 执行查询
            $query = Db()->table($queryClauses['from'])
                 ->select($queryClauses['select'])
                 ->where('feeds.id = :feedId') // 使用别名 feeds.
                 ->prepareParam($allParams);

            // 最后执行 fetch
            $feed = $query->fetch();

            if (empty($feed)) {
                return ["status" => "empty", "msg" => "日记不存在或已被删除"];
            }

            // 处理图片 JSON 和用户信息
            $feed['images'] = !empty($feed['images_json']) ? json_decode($feed['images_json'], true) : [];
            if (json_last_error() !== JSON_ERROR_NONE) $feed['images'] = [];
            unset($feed['images_json']);
            $feed['user'] = [
                'uid' => $feed['user_id'],
                'nickname' => $feed['nickname'],
                'avatar_url' => $feed['avatar_url'] // 使用 select 中的别名
            ];
            unset($feed['nickname'], $feed['avatar_url']);

            return ["status" => "ok", "data" => $feed];

        } catch (\Throwable $e) {
            $this->logWriteError("获取日记详情失败", [
                'exception_message' => $e->getMessage(),
                'operation' => 'get_feed_detail'
            ]);
            return ["status" => "error", "msg" => "获取日记详情失败"];
        }
    }


    /**
     * @apiName 发布动态/日记
     * @method publish_feed
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param content string 内容
     * @param images array 图片URL数组 (可选)
     * @param location string 位置信息 (可选)
     * @param tags string 标签 (可选, 逗号分隔或数组)
     * @param privacy string 隐私设置 ('public', 'private') (可选, 默认 'public')
     * @param type string 类型 ('feed', 'diary') (可选, 默认 'feed')
     * @return {"status":"ok","msg":"发布成功","data":{"feed_id": N}} | {"status":"error","msg":"..."}
     */
    public function publish_feed($uid, $token, $content, $images = [], $location = "", $tags = "", $privacy = 'public', $type = 'feed')
    {
        // {{ AURA-X: Modify - 修复try-catch覆盖范围，确保包围整个方法体. Confirmed via 寸止. }}
        try {
        // {{ AURA-X: Remove - 移除exception_log调用. Confirmed via 寸止. }}
        // 发布动态参数接收（已移除调试日志）

        // {{ AURA-X: Add - 添加参数接收调试日志. Confirmed via 寸止. }}



        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            $this->logWriteError("用户参数验证失败", [
                'uid_check' => check($uid, "intgt0"),
                'token_empty' => empty($token),
                'token_length' => strlen($token ?? ''),
                'uid_value' => $uid,
                'token_value' => substr($token ?? '', 0, 8) . '...'
            ], 'ERROR');
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
            $this->logWriteError("用户认证失败", [
                'uid' => $uid,
                'token_prefix' => substr($token, 0, 8) . '...'
            ], 'ERROR');
             return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        $userId = (int)$uid;

        $content = trim($content);
        if (empty($content)) {
             return ["status" => "error", "msg" => "内容不能为空"];
        }
        if (mb_strlen($content) > 5000) { // 检查长度
             return ["status" => "error", "msg" => "内容过长"];
        }

        // {{ AURA-X: Remove - 移除exception_log调用. Confirmed via 寸止. }}
        // 图片参数处理（已移除调试日志）

        // 处理图片参数
        $validImages = [];

        // 如果是空值，直接设置为空数组
        if (empty($images)) {
            $images = [];
        }
        // 如果已经是数组，处理每个元素
        else if (is_array($images)) {
            foreach ($images as $img) {
                // 如果是字符串且是URL，添加到有效图片列表
                if (is_string($img) && check($img, "url")) {
                    $validImages[] = $img;
                }
                // 如果是对象或数组，检查是否有status和data属性
                else if (is_array($img) || is_object($img)) {
                    $imgArray = (array)$img;
                    // 如果有status属性且为ok，并且有data属性
                    if (isset($imgArray['status']) && $imgArray['status'] === 'ok' && isset($imgArray['data'])) {
                        if (is_string($imgArray['data']) && check($imgArray['data'], "url")) {
                            $validImages[] = $imgArray['data'];
                        }
                    }
                    // 如果有url属性
                    else if (isset($imgArray['url']) && is_string($imgArray['url']) && check($imgArray['url'], "url")) {
                        $validImages[] = $imgArray['url'];
                    }
                }
            }
        }
        // 如果是字符串，尝试解析
        else if (is_string($images)) {
            // 如果是JSON数组字符串
            if (substr($images, 0, 1) === '[' && substr($images, -1) === ']') {
                $decodedImages = json_decode($images, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($decodedImages)) {
                    foreach ($decodedImages as $img) {
                        if (is_string($img) && check($img, "url")) {
                            $validImages[] = $img;
                        }
                    }
                }
            }
            // 如果是单个URL
            else if (check($images, "url")) {
                $validImages[] = $images;
            }
        }

        // 使用处理后的有效图片数组
        $images = $validImages;

        // Now $images should be an array (possibly empty)
        // Optional: Validate image URLs within the array
        foreach ($images as $img) {
            // Allow empty strings or null if that's possible from frontend
            if (!empty($img) && !check($img, "url")) {
                 $this->logWriteError("检测到无效图片URL", [
                     'invalid_url' => htmlspecialchars($img),
                     'operation' => 'validate_image_url'
                 ]);
                 // Decide whether to return error or just ignore the invalid URL
                 // return ["status" => "error", "msg" => "图片地址格式错误: " . htmlspecialchars($img)];
            }
        }
        // Filter out potentially empty/invalid URLs if choosing to ignore them
        $images = array_filter($images, function($url) { return !empty($url) && check($url, "url"); });

        $tagsString = is_array($tags) ? implode(',', $tags) : $tags;

        // Validate privacy setting
        $validPrivacy = ['public', 'private']; // Initially support public and private
        if (!in_array($privacy, $validPrivacy)) {
            $privacy = 'public'; // Default to public if invalid value provided
        }

        // {{ AURA-X: Modify - 修复SQL参数绑定错误，确保参数数量匹配. Confirmed via 寸止. }}
        $data = [
            'user_id' => $userId,
            'type' => $type, // 添加类型字段
            'content' => htmlspecialchars($content), // 直接使用处理后的内容，不使用预处理参数
            'images_json' => count($images) > 0 ? json_encode($images, JSON_UNESCAPED_UNICODE) : null,
            'location' => !empty($location) ? htmlspecialchars($location) : null, // 直接使用处理后的内容
            'tags' => !empty($tagsString) ? htmlspecialchars($tagsString) : null, // 直接使用处理后的内容
            'like_count' => 0, // 初始值
            'comment_count' => 0, // 初始值
            'status' => 'published', // Default status
            'privacy' => $privacy,       // Save privacy setting
            'created_at' => date('Y-m-d H:i:s'), // 使用当前时间
            'updated_at' => date('Y-m-d H:i:s')
        ];

        dbConn();

            // {{ AURA-X: Modify - 移除prepareParam调用，直接插入数据. Confirmed via 寸止. }}
            $insertResult = Db()->table('feeds')->insert($data);
            if (!$insertResult) {
                // {{ AURA-X: Remove - 移除exception_log调用. Confirmed via 寸止. }}
                return ["status" => "error", "msg" => "插入动态数据失败"];
            }
            $feedId = Db()->insertId();
            if (!$feedId) {
                // {{ AURA-X: Remove - 移除exception_log调用. Confirmed via 寸止. }}
                return ["status" => "error", "msg" => "获取插入ID失败"];
            }

            $this->user_log($userId, "发布动态【{$feedId}】");
            return ["status" => "ok", "msg" => "发布成功", "data" => ['feed_id' => $feedId]];

        } catch (\Throwable $e) {
            // {{ AURA-X: Remove - 移除exception_log调用. Confirmed via 寸止. }}
            return ["status" => "error", "msg" => "发布失败，请稍后重试"];
        }
    }

     /**
     * @apiName 发布日卡 (管理员)
     * @method publish_card
     * @POST
     * @param uid int 管理员用户ID
     * @param token string 管理员token
     * @param card_date string 日期 YYYY-MM-DD
     * @param description string 描述
     * @param author string 作者 (可选)
     * @param background_image_url string 背景图URL (可选)
     * @return {"status":"ok","msg":"发布成功","data":{"card_id": N}} | {"status":"error","msg":"..."}
     */
    public function publish_card($uid, $token, $card_date, $description, $author = "", $background_image_url = "")
    {
        // 权限校验 (需要您根据项目实际情况实现 isAdmin 检查)
        if (!$this->auth($uid, $token)) {
             return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        /* // 假设有一个 isAdmin 方法检查权限
        if (!$this->isAdmin($uid)) {
             return ["status" => "error", "msg" => "无权操作", "code" => 403];
        }
        */

        $description = trim($description);
        if (empty($card_date) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $card_date)) {
            return ["status" => "error", "msg" => "日期格式不正确"];
        }
        if (empty($description)) {
            return ["status" => "error", "msg" => "描述不能为空"];
        }
        if (!empty($background_image_url) && !check($background_image_url, "url")) {
             return ["status" => "error", "msg" => "背景图URL格式不正确"];
        }

        dbConn();
        try {
            // 检查日期是否已存在
            $existingCard = Db()->table('daily_cards')->where("card_date = :card_date")->prepareParam([':card_date' => $card_date])->fetch();
            if ($existingCard) {
                return ["status" => "error", "msg" => "该日期的卡片已存在"];
            }

            $data = [
                'card_date' => $card_date,
                'description' => ':description',
                'author' => ':author',
                'background_image_url' => ':background_image_url',
                'like_count' => 0,
                'comment_count' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
             $prepareParam = [
                ':description' => htmlspecialchars($description),
                ':author' => !empty($author) ? htmlspecialchars($author) : null,
                ':background_image_url' => !empty($background_image_url) ? htmlspecialchars($background_image_url) : null
            ];

            $insertResult = Db()->table('daily_cards')->prepareParam($prepareParam)->insert($data);
            if (!$insertResult) {
                // 记录插入日卡数据失败日志
                $this->logWriteError("插入日卡数据失败", [
                    'user_id' => $uid,
                    'card_date' => $card_date,
                    'operation' => 'create_daily_card'
                ]);
                return ["status" => "error", "msg" => "发布日卡失败"];
            }

            $cardId = Db()->insertId();
            if (!$cardId) {
                $this->logWriteError("获取日卡插入ID失败", [
                    'user_id' => $uid,
                    'card_date' => $card_date,
                    'operation' => 'create_daily_card'
                ]);
                return ["status" => "error", "msg" => "发布日卡失败"];
            }

            $this->user_log($uid, "发布日卡【{$cardId} - {$card_date}】");
            return ["status" => "ok", "msg" => "发布成功", "data" => ['card_id' => $cardId]];

        } catch (\Throwable $e) {
            $this->logWriteError("发布日卡失败", [
                'exception_message' => $e->getMessage(),
                'operation' => 'publish_daily_card'
            ]);
            return ["status" => "error", "msg" => "发布日卡失败"];
        }
    }


    /**
     * @apiName 点赞/取消点赞 日记
     * @method like_feed
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param id int 日记ID
     * @return {"status":"ok","msg":"点赞/取消成功","data":{"isLiked": boolean}} | {"status":"error","msg":"..."}
     */
    public function like_feed($uid, $token, $id)
    {
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
             return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        if (!check($id, "intgt0")) {
             return ["status" => "error", "msg" => "无效的日记ID"];
        }
        $userId = (int)$uid;
        $feedId = (int)$id;

        dbConn();
        try {
            // 检查日记是否存在
            $feedExists = Db()->table('feeds')->where("id = :feedId")->prepareParam([':feedId' => $feedId])->fetch();
            if (!$feedExists) {
                 return ["status" => "error", "msg" => "日记不存在"];
            }
            // 检查是否已点赞
            $like = Db()->table('user_feed_likes')
                ->where('user_id = :userId AND feed_id = :feedId')
                ->prepareParam([':userId' => $userId, ':feedId' => $feedId])
                ->fetch();

            $isLikedAfterToggle = false;
            if ($like) {
                // 已点赞，取消点赞
                $deleteResult = Db()->table('user_feed_likes')->where('id = :likeId')->prepareParam([':likeId' => $like['id']])->del();
                if (!$deleteResult) {
                    // 记录取消点赞失败日志
                    $this->logWriteError("取消日记点赞记录失败", [
                        'user_id' => $userId,
                        'feed_id' => $feedId,
                        'like_id' => $like['id'],
                        'operation' => 'unlike_feed'
                    ]);
                    return ["status" => "error", "msg" => "取消点赞失败"];
                }

                // 更新日记点赞数 (避免负数)
                $updateResult = Db()->_exec("UPDATE `feeds` SET `like_count` = GREATEST(0, `like_count` - 1) WHERE id = :feedId", [':feedId' => $feedId]);
                if ($updateResult === false) {
                    // 记录更新点赞数失败日志
                    $this->logWriteError("更新日记点赞数失败", [
                        'user_id' => $userId,
                        'feed_id' => $feedId,
                        'operation' => 'update_like_count'
                    ]);
                    return ["status" => "error", "msg" => "更新点赞数失败"];
                }

                $isLikedAfterToggle = false;
                $msg = "取消点赞成功";
            } else {
                // 未点赞，添加点赞
                $insertData = [
                    'user_id' => $userId,
                    'feed_id' => $feedId,
                    'created_at' => date('Y-m-d H:i:s')
                ];
                $insertResult = Db()->table('user_feed_likes')->insert($insertData);
                 if (!$insertResult) {
                     // 记录添加点赞记录失败日志
                     $this->logWriteError("添加日记点赞记录失败", [
                         'user_id' => $userId,
                         'feed_id' => $feedId,
                         'operation' => 'like_feed'
                     ]);
                     return ["status" => "error", "msg" => "添加点赞失败"];
                 }

                // 更新日记点赞数
                $updateResult = Db()->_exec("UPDATE `feeds` SET `like_count` = `like_count` + 1 WHERE id = :feedId", [':feedId' => $feedId]);
                if ($updateResult === false) {
                    $this->logWriteError("更新日记点赞数失败", [
                        'feed_id' => $feedId,
                        'user_id' => $userId,
                        'operation' => 'like_feed_update_count'
                    ]);
                    return ["status" => "error", "msg" => "更新点赞数失败"];
                }

                $isLikedAfterToggle = true;
                $msg = "点赞成功";
            }
            $this->user_log($userId, ($isLikedAfterToggle ? '点赞' : '取消点赞') . "日记【{$feedId}】");
            return ["status" => "ok", "msg" => $msg, "data" => ['isLiked' => $isLikedAfterToggle]];

        } catch (\Throwable $e) {
            $this->logWriteError("点赞日记失败", [
                'exception_message' => $e->getMessage(),
                'feed_id' => $feedId,
                'user_id' => $userId,
                'operation' => 'like_feed'
            ]);
            return ["status" => "error", "msg" => "操作失败，请稍后重试"];
        }
    }


    /**
     * @apiName 评论日记
     * @method comment_feed
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param feed_id int 日记ID
     * @param content string 评论内容
     * @param parent_id int 回复的评论ID (可选, 0 或 null 表示直接评论)
     * @return {"status":"ok","msg":"评论成功","data":{"comment_id": N}} | {"status":"error","msg":"..."}
     */
    public function comment_feed($uid, $token, $feed_id, $content, $parent_id = null)
    {
                $userId = (int)$uid;
                $feedId = (int)$feed_id;
                $parentId = $parent_id !== null ? (int)$parent_id : null;
                $this->logWriteError("准备插入日记评论数据", [
                    'feed_id' => $feedId,
                    'user_id' => $userId,
                    'content_length' => strlen($content),
                    'parent_id' => $parentId ?? 'null',
                    'step' => 1,
                    'operation' => 'comment_feed'
                ], 'INFO');
         if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
             return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
         if (!check($feed_id, "intgt0")) {
             return ["status" => "error", "msg" => "无效的日记ID"];
        }
        if($parent_id == "null"){
            $parent_id = null;
        }
         // parent_id 可以为 null 或大于0的整数
         if ($parent_id !== null && (!is_numeric($parent_id) || intval($parent_id) <= 0)) {
             return ["status" => "error", "msg" => "回复参数错误"];
         }

        // 内容处理
        $content = trim($content);

        if (empty($content)) {
             return ["status" => "error", "msg" => "评论内容不能为空"];
        }
         if (mb_strlen($content) > 1000) { // 限制评论长度
             return ["status" => "error", "msg" => "评论内容过长"];
        }
        $this->logWriteError("准备插入日记评论数据", [
            'feed_id' => $feedId,
            'user_id' => $userId,
            'content_length' => strlen($content),
            'parent_id' => $parentId ?? 'null',
            'step' => 1,
            'operation' => 'comment_feed'
        ], 'INFO');

        dbConn();
        $this->logWriteError("准备插入日记评论数据", [
            'feed_id' => $feedId,
            'user_id' => $userId,
            'content_length' => strlen($content),
            'parent_id' => $parentId ?? 'null',
            'step' => 2,
            'operation' => 'comment_feed'
        ], 'INFO');

        try {
            // 检查日记是否存在
             $feedExists = Db()->table('feeds')->where("id = :feedId")->prepareParam([':feedId' => $feedId])->fetch();
             if (!$feedExists) {
                  return ["status" => "error", "msg" => "评论的日记不存在"];
             }
             $this->logWriteError("准备插入日记评论数据", [
                 'feed_id' => $feedId,
                 'user_id' => $userId,
                 'content_length' => strlen($content),
                 'parent_id' => $parentId ?? 'null',
                 'step' => 3,
                 'operation' => 'comment_feed'
             ], 'INFO');

             // 如果是回复，检查父评论是否存在于同一日记下
            if ($parentId !== null) { // 检查parentId是否不为null
                $parentComment = Db()->table('feed_comments')
                                   ->where('id = :parentId AND feed_id = :feedId')
                                   ->prepareParam([':parentId' => $parentId, ':feedId' => $feedId])
                                   ->fetch();
                if (!$parentComment) {
                     return ["status" => "error", "msg" => "回复的评论不存在或不属于该日记"];
                }
            }
            $this->logWriteError("准备插入日记评论数据", [
                'feed_id' => $feedId,
                'user_id' => $userId,
                'content_length' => strlen($content),
                'parent_id' => $parentId ?? 'null',
                'step' => 4,
                'operation' => 'comment_feed'
            ], 'INFO');


            // 使用直接插入方式
            $data = [
                'feed_id' => $feedId,
                'user_id' => $userId,
                'content' => htmlspecialchars($content), // 防 XSS
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            if($parentId !== null){
                $data['parent_id'] = $parentId;
            }

            // 使用insert方法插入评论数据
            $insertResult = Db()->table('feed_comments')->insert($data);
            if (!$insertResult) {
                $this->logWriteError("插入日记评论数据失败", [
                    'feed_id' => $feedId,
                    'user_id' => $userId,
                    'operation' => 'comment_feed_insert'
                ]);
                return ["status" => "error", "msg" => "评论发布失败"];
            }
            $this->logWriteError("准备插入日记评论数据", [
                'feed_id' => $feedId,
                'user_id' => $userId,
                'content_length' => strlen($content),
                'parent_id' => $parentId ?? 'null',
                'step' => 5,
                'operation' => 'comment_feed'
            ], 'INFO');

            // 更新日记评论数
            $updateResult = Db()->_exec("UPDATE `feeds` SET `comment_count` = `comment_count` + 1 WHERE id = :feedId", [':feedId' => $feedId]);
            if ($updateResult === false) {
                $this->logWriteError("更新日记评论数失败", [
                    'feed_id' => $feedId,
                    'user_id' => $userId,
                    'operation' => 'comment_feed_update_count'
                ]);
                return ["status" => "error", "msg" => "更新评论数失败"];
            }
            $this->logWriteError("准备插入日记评论数据", [
                'feed_id' => $feedId,
                'user_id' => $userId,
                'content_length' => strlen($content),
                'parent_id' => $parentId ?? 'null',
                'step' => 6,
                'operation' => 'comment_feed'
            ], 'INFO');


            $this->user_log($userId, "评论日记【{$feedId}】");
            // 可以考虑返回新评论的更多信息，而不仅仅是ID
            return ["status" => "ok", "msg" => "评论成功"];

        } catch (\Throwable $e) {
            // 记录详细错误信息
            $errorDetails = "评论日记失败: " . $e->getMessage() .
                           "\nFile: " . $e->getFile() .
                           "\nLine: " . $e->getLine() .
                           "\nTrace: " . $e->getTraceAsString() .
                           "\nParams: feedId={$feedId}, userId={$userId}, content长度=" . strlen($content);
            $this->logWriteError("日记评论插入失败详情", [
                'error_details' => $errorDetails,
                'operation' => 'comment_feed'
            ]);

            // 返回更具体的错误信息
            return ["status" => "error", "msg" => "评论失败: " . $e->getMessage()];
        }
    }

    // --- 新增：摘录相关方法 ---

    /**
     * @apiName 创建摘录
     * @method create_quote
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param content string 摘录内容
     * @param author_id int 作者ID (可选)
     * @param source_id int 出处ID (可选)
     * @param tags string 标签 (可选, 逗号分隔)
     * @param privacy string 隐私设置 ('public' 或 'private', 默认 'public')
     * @param allow_official int 是否允许官方使用 (0或1, 默认0)
     * @param images array 图片URL数组 (可选)
     * @return {"status":"ok","msg":"摘录成功","data":{"quote_id": N}} | {"status":"error","msg":"..."} | {"status":"relogin"}
     */
    public function create_quote($uid, $token, $content, $author_id = null, $source_id = null, $tags = "", $privacy = 'public', $allow_official = 0, $images = [])
    {
        // 1. 用户验证
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        $userId = (int)$uid;

        // 2. 内容验证
        $content = trim($content);
        if (empty($content)) {
            return ["status" => "error", "msg" => "摘录内容不能为空"];
        }
        if (mb_strlen($content) > 10000) { // 限制长度 (按需调整)
            return ["status" => "error", "msg" => "内容过长"];
        }

        // {{ AURA-X: Modify - 更新参数处理支持新的外键字段. Confirmed via 寸止 }}
        // 3. 验证和处理作者和出处ID
        $authorId = null;
        $sourceId = null;

        if (!empty($author_id)) {
            $authorId = (int)$author_id;
            // 验证作者是否存在
            $author_check = Db()->_fetch("SELECT id FROM authors WHERE id = :id AND status = 1", [':id' => $authorId]);
            if (!$author_check) {
                return ["status" => "error", "msg" => "指定的作者不存在"];
            }
        }

        if (!empty($source_id)) {
            $sourceId = (int)$source_id;
            // 验证出处是否存在
            $source_check = Db()->_fetch("SELECT id FROM sources WHERE id = :id AND status = 1", [':id' => $sourceId]);
            if (!$source_check) {
                return ["status" => "error", "msg" => "指定的出处不存在"];
            }
        }

        $tags = !empty($tags) ? trim(htmlspecialchars($tags)) : null; // 保持为字符串

        // 4. 验证隐私设置
        $validPrivacy = ['public', 'private'];
        if (!in_array($privacy, $validPrivacy)) {
            $privacy = 'public'; // 默认公开
        }

        // 5. 处理图片数据
        $imagesJson = null;
        if (!empty($images)) {
            if (is_array($images)) {
                // 验证图片URL格式
                $validImages = [];
                foreach ($images as $imageUrl) {
                    if (is_string($imageUrl) && filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                        $validImages[] = $imageUrl;
                    }
                }
                if (!empty($validImages)) {
                    $imagesJson = json_encode($validImages, JSON_UNESCAPED_UNICODE);
                }
            } elseif (is_string($images)) {
                // 如果传入的是JSON字符串，验证格式
                $decodedImages = json_decode($images, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($decodedImages)) {
                    $validImages = [];
                    foreach ($decodedImages as $imageUrl) {
                        if (is_string($imageUrl) && filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                            $validImages[] = $imageUrl;
                        }
                    }
                    if (!empty($validImages)) {
                        $imagesJson = json_encode($validImages, JSON_UNESCAPED_UNICODE);
                    }
                }
            }
        }

        // {{ AURA-X: Add - 处理allow_official参数. Confirmed via 寸止 }}
        // 从REQUEST中获取allow_official参数，兼容前端传参
        if (isset($_REQUEST['allow_official'])) {
            $allow_official = (int)$_REQUEST['allow_official'];
        }
        $allow_official = in_array($allow_official, [0, 1]) ? $allow_official : 0;

        // {{ AURA-X: Modify - 更新数据结构支持新的外键字段. Confirmed via 寸止 }}
        // 6. 准备插入数据 - 使用新的author_id和source_id字段
        $data = [
            'user_id' => $userId,
            'content' => htmlspecialchars($content), // 防 XSS
            'images_json' => $imagesJson,
            'author_id' => $authorId, // 新字段：作者ID
            'source_id' => $sourceId, // 新字段：出处ID
            'tags' => $tags,
            'privacy' => $privacy,
            'allow_official' => $allow_official,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // 7. 执行插入
        dbConn();
        try {
            // 替换insertGetId为insert + lastInsertId
            $insertResult = Db()->table('quotes')->insert($data);
            if (!$insertResult) {
                $this->logWriteError("插入摘录数据失败", [
                    'user_id' => $uid,
                    'operation' => 'create_quote'
                ]);
                return ["status" => "error", "msg" => "摘录保存失败"];
            }
            $quoteId = Db()->insertId();
            if (!$quoteId) {
                $this->logWriteError("获取摘录插入ID失败", [
                    'user_id' => $uid,
                    'operation' => 'create_quote'
                ]);
                return ["status" => "error", "msg" => "摘录保存失败"];
            }

            // {{ AURA-X: Add - 更新作者和出处的引用计数. Confirmed via 寸止 }}
            // 8. 更新引用计数
            if ($authorId) {
                Db()->_execute("UPDATE authors SET quote_count = quote_count + 1 WHERE id = :id", [':id' => $authorId]);
            }
            if ($sourceId) {
                Db()->_execute("UPDATE sources SET quote_count = quote_count + 1 WHERE id = :id", [':id' => $sourceId]);
            }

            $this->user_log($userId, "创建摘录【{$quoteId}】");
            return ["status" => "ok", "msg" => "摘录成功", "data" => ['quote_id' => $quoteId]];

        } catch (\Throwable $e) {
            $this->logWriteError("创建摘录失败", [
                'exception_message' => $e->getMessage(),
                'operation' => 'create_quote'
            ]);
            return ["status" => "error", "msg" => "摘录保存失败，请稍后重试"];
        }
    }

    // --- 后续添加 get_quotes, update_quote, delete_quote 等 ---

    /**
     * @apiName 点赞/取消点赞 日卡
     * @method like_card
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param id int 日卡ID
     * @return {"status":"ok","msg":"点赞/取消成功","data":{"isLiked": boolean}} | {"status":"error","msg":"..."}
     */
    public function like_card($uid, $token, $id)
    {
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
             return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        if (!check($id, "intgt0")) {
             return ["status" => "error", "msg" => "无效的日卡ID"];
        }
        $userId = (int)$uid;
        $cardId = (int)$id;

        dbConn();
        try {
            // 检查日卡是否存在
            $cardExists = Db()->table('daily_cards')->where("id = :cardId")->prepareParam([':cardId' => $cardId])->fetch();
            if (!$cardExists) {
                 return ["status" => "error", "msg" => "日卡不存在"];
            }

            // 检查是否已点赞
            $like = Db()->table('user_card_likes')
                ->where('user_id = :userId AND card_id = :cardId')
                ->prepareParam([':userId' => $userId, ':cardId' => $cardId])
                ->fetch();

            $isLikedAfterToggle = false;
            if ($like) {
                // 已点赞，取消点赞
                $deleteResult = Db()->table('user_card_likes')->where('id = :likeId')->prepareParam([':likeId' => $like['id']])->del();
                if (!$deleteResult) {
                    $this->logWriteError("取消日卡点赞记录失败", [
                        'card_id' => $cardId,
                        'user_id' => $userId,
                        'operation' => 'unlike_card'
                    ]);
                    return ["status" => "error", "msg" => "取消点赞失败"];
                }

                // 更新日卡点赞数 (避免负数)
                $updateResult = Db()->_exec("UPDATE `daily_cards` SET `like_count` = GREATEST(0, `like_count` - 1) WHERE id = :cardId", [':cardId' => $cardId]);
                if ($updateResult === false) {
                    $this->logWriteError("更新日卡点赞数失败", [
                        'card_id' => $cardId,
                        'user_id' => $userId,
                        'operation' => 'unlike_card_update_count'
                    ]);
                    return ["status" => "error", "msg" => "更新点赞数失败"];
                }

                $isLikedAfterToggle = false;
                $msg = "取消点赞成功";

            } else {
                // 未点赞，添加点赞
                $insertData = [
                    'user_id' => $userId,
                    'card_id' => $cardId,
                    'created_at' => date('Y-m-d H:i:s')
                ];
                $insertResult = Db()->table('user_card_likes')->insert($insertData);
                 if (!$insertResult) {
                     $this->logWriteError("添加日卡点赞记录失败", [
                         'card_id' => $cardId,
                         'user_id' => $userId,
                         'operation' => 'like_card'
                     ]);
                     return ["status" => "error", "msg" => "添加点赞失败"];
                 }

                // 更新日卡点赞数
                $updateResult = Db()->_exec("UPDATE `daily_cards` SET `like_count` = `like_count` + 1 WHERE id = :cardId", [':cardId' => $cardId]);
                if ($updateResult === false) {
                    $this->logWriteError("更新日卡点赞数失败", [
                        'card_id' => $cardId,
                        'user_id' => $userId,
                        'operation' => 'like_card_update_count'
                    ]);
                    return ["status" => "error", "msg" => "更新点赞数失败"];
                }

                $isLikedAfterToggle = true;
                $msg = "点赞成功";
            }

            $this->user_log($userId, ($isLikedAfterToggle ? '点赞' : '取消点赞') . "日卡【{$cardId}】");
            return ["status" => "ok", "msg" => $msg, "data" => ['isLiked' => $isLikedAfterToggle]];

        } catch (\Throwable $e) {
            $this->logWriteError("点赞日卡失败", [
                'exception_message' => $e->getMessage(),
                'card_id' => $cardId,
                'user_id' => $userId,
                'operation' => 'like_card'
            ]);
            return ["status" => "error", "msg" => "操作失败，请稍后重试"];
        }
    }

    /**
     * @apiName 收藏/取消收藏 日卡
     * @method favorite_card
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param id int 日卡ID
     * @return {"status":"ok","msg":"收藏/取消成功","data":{"isFavorited": boolean}} | {"status":"error","msg":"..."}
     */
    public function favorite_card($uid, $token, $id)
    {
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
             return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        if (!check($id, "intgt0")) {
             return ["status" => "error", "msg" => "无效的日卡ID"];
        }
        $userId = (int)$uid;
        $cardId = (int)$id;

        dbConn();
        try {
            // 检查日卡是否存在
            $cardExists = Db()->table('daily_cards')->where("id = :cardId")->prepareParam([':cardId' => $cardId])->fetch();
            if (!$cardExists) {
                 return ["status" => "error", "msg" => "日卡不存在"];
            }

            // 检查是否已收藏
            $favorite = Db()->table('user_favorites')
                ->where("user_id = :userId AND item_id = :cardId AND item_type = 'card'")
                ->prepareParam([':userId' => $userId, ':cardId' => $cardId])
                ->fetch();

            $isFavoritedAfterToggle = false;
            if ($favorite) {
                // 已收藏，取消收藏
                $deleteResult = Db()->table('user_favorites')->where("id = :id")->prepareParam([':id' => $favorite['id']])->del();
                if (!$deleteResult) {
                    $this->logWriteError("取消日卡收藏记录失败", [
                        'card_id' => $cardId,
                        'user_id' => $userId,
                        'operation' => 'unfavorite_card'
                    ]);
                    return ["status" => "error", "msg" => "取消收藏失败"];
                }

                $isFavoritedAfterToggle = false;
                $msg = "取消收藏成功";

            } else {
                // 未收藏，添加收藏
                $insertData = [
                    'user_id' => $userId,
                    'item_id' => $cardId,
                    'item_type' => 'card',
                    'created_at' => date('Y-m-d H:i:s')
                ];
                $insertResult = Db()->table('user_favorites')->insert($insertData);
                 if (!$insertResult) {
                     $this->logWriteError("添加日卡收藏记录失败", [
                         'card_id' => $cardId,
                         'user_id' => $userId,
                         'operation' => 'favorite_card'
                     ]);
                     return ["status" => "error", "msg" => "添加收藏失败"];
                 }

                $isFavoritedAfterToggle = true;
                $msg = "收藏成功";
            }

            $this->user_log($userId, ($isFavoritedAfterToggle ? '收藏' : '取消收藏') . "日卡【{$cardId}】");
            return ["status" => "ok", "msg" => $msg, "data" => ['isFavorited' => $isFavoritedAfterToggle]];

        } catch (\Throwable $e) {
            return ["status" => "error", "msg" => "收藏操作失败: " . $e->getMessage()];
        }
    }

    /**
     * @apiName 收藏/取消收藏 动态
     * @method favorite_feed
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param id int 动态ID
     * @return {"status":"ok","msg":"收藏/取消成功","data":{"isFavorited": boolean}} | {"status":"error","msg":"..."}
     */
    public function favorite_feed($uid, $token, $id)
    {
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
             return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        if (!check($id, "intgt0")) {
             return ["status" => "error", "msg" => "无效的动态ID"];
        }
        $userId = (int)$uid;
        $feedId = (int)$id;

        dbConn();
        try {
            // 检查动态是否存在
            $feedExists = Db()->table('feeds')->where("id = :feedId")->prepareParam([':feedId' => $feedId])->fetch();
            if (!$feedExists) {
                 return ["status" => "error", "msg" => "动态不存在"];
            }

            // 检查是否已收藏
            $favorite = Db()->table('user_favorites')
                ->where("user_id = :userId AND item_id = :feedId AND item_type = 'feed'")
                ->prepareParam([':userId' => $userId, ':feedId' => $feedId])
                ->fetch();

            $isFavoritedAfterToggle = false;
            if ($favorite) {
                // 已收藏，取消收藏
                $deleteResult = Db()->table('user_favorites')->where("id = :id")->prepareParam([':id' => $favorite['id']])->del();
                if (!$deleteResult) {
                    $this->logWriteError("取消日记收藏记录失败", [
                        'feed_id' => $feedId,
                        'user_id' => $userId,
                        'operation' => 'unfavorite_feed'
                    ]);
                    return ["status" => "error", "msg" => "取消收藏失败"];
                }

                $isFavoritedAfterToggle = false;
                $msg = "取消收藏成功";

            } else {
                // 未收藏，添加收藏
                $insertData = [
                    'user_id' => $userId,
                    'item_id' => $feedId,
                    'item_type' => 'feed',
                    'created_at' => date('Y-m-d H:i:s')
                ];
                $insertResult = Db()->table('user_favorites')->insert($insertData);
                 if (!$insertResult) {
                     $this->logWriteError("添加日记收藏记录失败", [
                         'feed_id' => $feedId,
                         'user_id' => $userId,
                         'operation' => 'favorite_feed'
                     ]);
                     return ["status" => "error", "msg" => "添加收藏失败"];
                 }

                $isFavoritedAfterToggle = true;
                $msg = "收藏成功";
            }

            $this->user_log($userId, ($isFavoritedAfterToggle ? '收藏' : '取消收藏') . "动态【{$feedId}】");
            return ["status" => "ok", "msg" => $msg, "data" => ['isFavorited' => $isFavoritedAfterToggle]];

        } catch (\Throwable $e) {
            return ["status" => "error", "msg" => "收藏操作失败: " . $e->getMessage()];
        }
    }

    /**
     * @apiName 评论日卡
     * @method comment_card
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param card_id int 日卡ID
     * @param content string 评论内容
     * @param parent_id int 回复的评论ID (可选, 0 或 null 表示直接评论)
     * @return {"status":"ok","msg":"评论成功","data":{"comment_id": N}} | {"status":"error","msg":"..."}
     */
    public function comment_card($uid, $token, $card_id, $content, $parent_id = null)
    {
         if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
             return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
         if (!check($card_id, "intgt0")) {
             return ["status" => "error", "msg" => "无效的日卡ID"];
        }
        if($parent_id == "null"){
            $parent_id = null;
        }
         // parent_id 可以为 null 或大于0的整数
         if ($parent_id !== null && (!is_numeric($parent_id) || intval($parent_id) <= 0)) {
             return ["status" => "error", "msg" => "回复参数错误"];
         }

        $userId = (int)$uid;
        $cardId = (int)$card_id;
        // 修复parent_id处理，确保为整数或null
        $parentId = $parent_id !== null ? (int)$parent_id : null; // 保持为整数或null
        $content = trim($content);

        if (empty($content)) {
             return ["status" => "error", "msg" => "评论内容不能为空"];
        }
         if (mb_strlen($content) > 1000) { // 限制评论长度
             return ["status" => "error", "msg" => "评论内容过长"];
        }

        dbConn();
        try {
            // 检查日卡是否存在
             $cardExists = Db()->table('daily_cards')->where("id = :cardId")->prepareParam([':cardId' => $cardId])->fetch();
             if (!$cardExists) {
                  return ["status" => "error", "msg" => "评论的日卡不存在"];
             }

             // 如果是回复，检查父评论是否存在于同一日卡下
            if ($parentId !== null) { // 检查parentId是否不为null
                $parentComment = Db()->table('card_comments')
                                   ->where('id = :parentId AND card_id = :cardId')
                                   ->prepareParam([':parentId' => $parentId, ':cardId' => $cardId])
                                   ->fetch();
                if (!$parentComment) {
                     return ["status" => "error", "msg" => "回复的评论不存在或不属于该日卡"];
                }
            }

            // 记录调试日志
            $this->logWriteError("准备插入日卡评论数据", [
                'card_id' => $cardId,
                'user_id' => $userId,
                'content_length' => strlen($content),
                'parent_id' => $parentId ?? 'null',
                'operation' => 'comment_card'
            ], 'INFO');

            // 使用直接插入方式
            $data = [
                'card_id' => $cardId,
                'user_id' => $userId,
                'content' => htmlspecialchars($content), // 防 XSS
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            if($parentId !== null){
                $data['parent_id'] = $parentId;
            }
            // 使用insert方法插入评论数据
            $insertResult = Db()->table('card_comments')->insert($data);
            if (!$insertResult) {
                $this->logWriteError("插入日卡评论数据失败", [
                    'card_id' => $cardId,
                    'user_id' => $userId,
                    'operation' => 'comment_card_insert'
                ]);
                return ["status" => "error", "msg" => "评论发布失败"];
            }

            // 更新日卡评论数
            $updateResult = Db()->_exec("UPDATE `daily_cards` SET `comment_count` = `comment_count` + 1 WHERE id = :cardId", [':cardId' => $cardId]);
            if ($updateResult === false) {
                $this->logWriteError("更新日卡评论数失败", [
                    'card_id' => $cardId,
                    'user_id' => $userId,
                    'operation' => 'comment_card_update_count'
                ]);
                return ["status" => "error", "msg" => "更新评论数失败"];
            }

            $this->user_log($userId, "评论日卡【{$cardId}】");
            // 可以考虑返回新评论的更多信息，而不仅仅是ID
            return ["status" => "ok", "msg" => "评论成功",];

        } catch (\Throwable $e) {
            // 记录详细错误信息
            $errorDetails = "评论日卡失败: " . $e->getMessage() .
                           "\nFile: " . $e->getFile() .
                           "\nLine: " . $e->getLine() .
                           "\nTrace: " . $e->getTraceAsString() .
                           "\nParams: cardId={$cardId}, userId={$userId}, content长度=" . strlen($content);
            $this->logWriteError("日卡评论插入失败详情", [
                'error_details' => $errorDetails,
                'operation' => 'comment_card'
            ]);

            // 返回更具体的错误信息
            return ["status" => "error", "msg" => "评论失败: " . $e->getMessage()];
        }
    }

    /**
     * @apiName 获取日卡评论列表
     * @method get_card_comments
     * @POST
     * @param card_id int 日卡ID
     * @param uid int 用户ID (可选)
     * @param token string token (可选)
     * @param page int 页码 (默认 1)
     * @param page_size int 每页数量 (默认 20)
     * @param sort_type string 排序方式 (默认 'latest', 可选 'hot')
     * @return {"status":"ok","data":{"list":[...], "total": N}} | {"status":"error","msg":"..."} | {"status":"empty"}
     */
    public function get_card_comments($card_id, $page = 1, $page_size = 20, $uid = 0, $token = "", $sort_type = 'latest')
    {
        if (!check($card_id, "intgt0")) {
            return ["status" => "error", "msg" => "无效的日卡ID"];
        }
        if (!check($page, "intgt0") || !check($page_size, "intgt0") || $page_size > 100) {
            return ["status" => "error", "msg" => "分页参数错误"];
        }

        $cardId = (int)$card_id;
        $page = (int)$page;
        $page_size = (int)$page_size;
        $sort_type = in_array($sort_type, ['latest', 'hot']) ? $sort_type : 'latest';

        // 验证用户身份（可选）
        $userId = 0;
        if (!empty($uid) && !empty($token)) {
            if (check($uid, "intgt0") && strlen($token) == 32 && $this->auth($uid, $token)) {
                $userId = (int)$uid;
            }
        }

        dbConn();
        try {
            // 检查日卡是否存在
            $cardExists = Db()->table('daily_cards')->where("id = :cardId")->prepareParam([':cardId' => $cardId])->fetch();
            if (!$cardExists) {
                return ["status" => "error", "msg" => "日卡不存在"];
            }

            // 构建查询
            $fromClause = 'card_comments cc LEFT JOIN user u ON u.uid = cc.user_id';
            $selectFields = 'cc.id, cc.card_id, cc.user_id, cc.content, cc.parent_id, cc.like_count, cc.created_at, u.nickname, u.avatar';

            // 获取评论总数
            $total = Db()->table('card_comments')->where("card_id = :cardId")->prepareParam([':cardId' => $cardId])->count();

            // 根据排序类型决定排序规则 - 修复排序逻辑
            $orderBy = $sort_type === 'hot' ? "cc.like_count DESC, cc.created_at DESC" : "cc.created_at DESC";

            // 获取评论列表
            $comments = Db()->table($fromClause)
                ->select($selectFields)
                ->where("cc.card_id = :cardId")
                ->prepareParam([':cardId' => $cardId])
                ->order($orderBy)
                ->page($page, $page_size);

            if (empty($comments)) {
                return ["status" => "empty", "msg" => "暂无评论"];
            }

            // 处理评论数据
            foreach ($comments as &$comment) {
                $comment['user'] = [
                    'uid' => $comment['user_id'],
                    'nickname' => $comment['nickname'],
                    'avatar' => $comment['avatar']
                ];
                unset($comment['nickname'], $comment['avatar']);

                // 如果是回复评论，获取父评论信息
                if (!empty($comment['parent_id'])) {
                    $parentComment = Db()->table('card_comments cc LEFT JOIN user u ON u.uid = cc.user_id')
                        ->select('cc.id, cc.content, cc.user_id, u.nickname')
                        ->where("cc.id = :parentId")
                        ->prepareParam([':parentId' => $comment['parent_id']])
                        ->fetch();

                    if ($parentComment) {
                        $comment['reply_to'] = [
                            'id' => $parentComment['id'],
                            'user_id' => $parentComment['user_id'],
                            'nickname' => $parentComment['nickname'],
                            'content' => mb_substr($parentComment['content'], 0, 20) . (mb_strlen($parentComment['content']) > 20 ? '...' : '')
                        ];
                    }
                }
            }

            return ["status" => "ok", "data" => ["list" => $comments, "total" => $total]];

        } catch (\Throwable $e) {
            $this->logWriteError("获取日记评论失败", [
                'exception_message' => $e->getMessage(),
                'operation' => 'get_feed_comments'
            ]);
            return ["status" => "error", "msg" => "获取评论失败"];
        }
    }

    /**
     * @apiName 获取日记评论列表
     * @method get_feed_comments
     * @POST
     * @param feed_id int 日记ID
     * @param uid int 用户ID (可选)
     * @param token string token (可选)
     * @param page int 页码 (默认 1)
     * @param page_size int 每页数量 (默认 20)
     * @param sort_type string 排序方式 (默认 'latest', 可选 'hot')
     * @return {"status":"ok","data":{"list":[...], "total": N}} | {"status":"error","msg":"..."} | {"status":"empty"}
     */
    public function get_feed_comments($feed_id, $page = 1, $page_size = 20, $uid = 0, $token = "", $sort_type = 'latest')
    {
        if (!check($feed_id, "intgt0")) {
            return ["status" => "error", "msg" => "无效的日记ID"];
        }
        if (!check($page, "intgt0") || !check($page_size, "intgt0") || $page_size > 100) {
            return ["status" => "error", "msg" => "分页参数错误"];
        }

        $feedId = (int)$feed_id;
        $page = (int)$page;
        $page_size = (int)$page_size;
        $sort_type = in_array($sort_type, ['latest', 'hot']) ? $sort_type : 'latest';

        // 验证用户身份（可选）
        $userId = 0;
        if (!empty($uid) && !empty($token)) {
            if (check($uid, "intgt0") && strlen($token) == 32 && $this->auth($uid, $token)) {
                $userId = (int)$uid;
            }
        }

        dbConn();
        try {
            // 检查日记是否存在
            $feedExists = Db()->table('feeds')->where("id = :feedId")->prepareParam([':feedId' => $feedId])->fetch();
            if (!$feedExists) {
                return ["status" => "error", "msg" => "日记不存在"];
            }

            // 构建查询
            $fromClause = 'feed_comments fc LEFT JOIN user u ON u.uid = fc.user_id';
            $selectFields = 'fc.id, fc.feed_id, fc.user_id, fc.content, fc.parent_id, fc.like_count, fc.created_at, u.nickname, u.avatar';

            // 获取评论总数
            $total = Db()->table('feed_comments')->where("feed_id = :feedId")->prepareParam([':feedId' => $feedId])->count();

            // 根据排序类型决定排序规则 - 修复排序逻辑
            $orderBy = $sort_type === 'hot' ? "fc.like_count DESC, fc.created_at DESC" : "fc.created_at DESC";

            // 获取评论列表
            $comments = Db()->table($fromClause)
                ->select($selectFields)
                ->where("fc.feed_id = :feedId")
                ->prepareParam([':feedId' => $feedId])
                ->order($orderBy)
                ->page($page, $page_size);

            if (empty($comments)) {
                return ["status" => "empty", "msg" => "暂无评论"];
            }

            // 处理评论数据
            foreach ($comments as &$comment) {
                $comment['user'] = [
                    'uid' => $comment['user_id'],
                    'nickname' => $comment['nickname'],
                    'avatar' => $comment['avatar']
                ];
                unset($comment['nickname'], $comment['avatar']);

                // 如果是回复评论，获取父评论信息
                if (!empty($comment['parent_id'])) {
                    $parentComment = Db()->table('feed_comments fc LEFT JOIN user u ON u.uid = fc.user_id')
                        ->select('fc.id, fc.content, fc.user_id, u.nickname')
                        ->where("fc.id = :parentId")
                        ->prepareParam([':parentId' => $comment['parent_id']])
                        ->fetch();

                    if ($parentComment) {
                        $comment['reply_to'] = [
                            'id' => $parentComment['id'],
                            'user_id' => $parentComment['user_id'],
                            'nickname' => $parentComment['nickname'],
                            'content' => mb_substr($parentComment['content'], 0, 20) . (mb_strlen($parentComment['content']) > 20 ? '...' : '')
                        ];
                    }
                }
            }

            return ["status" => "ok", "data" => ["list" => $comments, "total" => $total]];

        } catch (\Throwable $e) {
            $this->logWriteError("获取日卡评论失败", [
                'exception_message' => $e->getMessage(),
                'operation' => 'get_card_comments'
            ]);
            return ["status" => "error", "msg" => "获取评论失败"];
        }
    }

    /**
     * 处理未定义的方法
     */
    public function _empty()
    {
        return ["status" => "error", "msg" => "请求的方法不存在"];
        // 或者使用助手函数: return response_error('请求的方法不存在', null, 404);
    }

    // 如果需要 isAdmin 权限检查，可以在这里或 BaseController 中实现
    /*
    protected function isAdmin($userId) {
        // 实现管理员检查逻辑，例如查询用户角色表
        dbConn();
        $role = Db()->table('user')->where('id = :userId')->prepareParam([':userId' => $userId])->getColumn('role');
        return $role === 'admin'; // 假设 admin 角色表示管理员
    }
    */

    /*
    * @apiName 点赞评论
    * @method like_comment
    * @POST
    * @param uid string 用户编号
    * @param token string token
    * @param comment_id string 评论ID
    * @param comment_type string 评论类型：feed=动态评论，card=日卡评论，quote=摘录评论
    * @return {"status":"ok","msg":"点赞成功"}
    */
    public function like_comment($uid, $token, $comment_id, $comment_type)
    {
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32 ||
            empty($comment_id) ||
            !check($comment_id, "intgt0") ||
            empty($comment_type) ||
            !in_array($comment_type, ['feed', 'card', 'quote'])
        ) return ["status" => "error", "msg" => "参数错误"];

        if (!$this->auth($uid, $token)) return ["status" => "relogin", "msg" => "登录信息验证失败"];

        $uid = (int)$uid;
        $comment_id = (int)$comment_id;
        dbConn();

        try {
            // 记录操作开始的日志
            $this->logWriteError("用户尝试操作评论点赞", [
                'user_id' => $uid,
                'comment_type' => $comment_type,
                'comment_id' => $comment_id,
                'operation' => 'like_comment'
            ], 'INFO');
            
            // 检查是否已经点赞
            $existing = Db()->table("comment_likes")->where("user_id={$uid} AND comment_id={$comment_id} AND comment_type='{$comment_type}'")->fetch();

            if ($existing) {
                // 取消点赞
                Db()->table("comment_likes")->where("user_id={$uid} AND comment_id={$comment_id} AND comment_type='{$comment_type}'")->del();

                // 更新评论表的点赞数
                $table_map = [
                    'feed' => 'feed_comments',
                    'card' => 'card_comments',
                    'quote' => 'quote_comments'
                ];

                if (isset($table_map[$comment_type])) {
                    // 使用参数化查询执行点赞数减1操作
                    $tableName = $table_map[$comment_type];
                    Db()->_exec("UPDATE `{$tableName}` SET `like_count` = GREATEST(0, `like_count` - 1) WHERE `id` = :comment_id", [':comment_id' => $comment_id]);
                }
                
                // 记录取消点赞成功日志
                $this->logWriteError("用户取消评论点赞成功", [
                    'user_id' => $uid,
                    'comment_type' => $comment_type,
                    'comment_id' => $comment_id,
                    'operation' => 'unlike_comment'
                ], 'INFO');

                return ["status" => "ok", "msg" => "取消点赞成功", "action" => "unlike"];
            } else {
                // 添加点赞
                Db()->table("comment_likes")->insert([
                    "user_id" => $uid,
                    "comment_id" => $comment_id,
                    "comment_type" => $comment_type,
                    "created_at" => date('Y-m-d H:i:s')
                ]);

                // 更新评论表的点赞数
                $table_map = [
                    'feed' => 'feed_comments',
                    'card' => 'card_comments',
                    'quote' => 'quote_comments'
                ];

                if (isset($table_map[$comment_type])) {
                    // 使用参数化查询执行点赞数加1操作
                    $tableName = $table_map[$comment_type];
                    Db()->_exec("UPDATE `{$tableName}` SET `like_count` = `like_count` + 1 WHERE `id` = :comment_id", [':comment_id' => $comment_id]);
                }
                
                // 记录点赞成功日志
                $this->logWriteError("用户评论点赞成功", [
                    'user_id' => $uid,
                    'comment_type' => $comment_type,
                    'comment_id' => $comment_id,
                    'operation' => 'like_comment'
                ], 'INFO');

                return ["status" => "ok", "msg" => "点赞成功", "action" => "like"];
            }
        } catch (\Throwable $e) {
            // 记录异常日志
            $this->logWriteError("评论点赞操作异常", [
                'user_id' => $uid,
                'comment_type' => $comment_type,
                'comment_id' => $comment_id,
                'exception_message' => $e->getMessage(),
                'operation' => 'like_comment'
            ]);
            return ["status" => "error", "msg" => "操作失败"];
        }
    }

    /*
    * @apiName 获取评论点赞状态
    * @method get_comment_like_status
    * @POST
    * @param uid string 用户编号
    * @param token string token
    * @param comment_ids string 评论ID列表，逗号分隔
    * @param comment_type string 评论类型：feed=动态评论，card=日卡评论，quote=摘录评论
    * @return {"status":"ok","data":{"liked_comments":[1,2,3]}}
    */
    public function get_comment_like_status($uid, $token, $comment_ids, $comment_type)
    {
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32 ||
            empty($comment_ids) ||
            empty($comment_type) ||
            !in_array($comment_type, ['feed', 'card', 'quote'])
        ) return ["status" => "error", "msg" => "参数错误"];

        if (!$this->auth($uid, $token)) return ["status" => "relogin", "msg" => "登录信息验证失败"];

        $uid = (int)$uid;
        $comment_ids_array = explode(',', $comment_ids);
        $comment_ids_array = array_map('intval', $comment_ids_array);
        $comment_ids_str = implode(',', $comment_ids_array);

        dbConn();

        try {
            $liked_comments = Db()->table("comment_likes")
                ->select("comment_id")
                ->where("user_id={$uid} AND comment_type='{$comment_type}' AND comment_id IN ({$comment_ids_str})")
                ->fetchAll();

            $liked_ids = array_column($liked_comments, 'comment_id');
            $liked_ids = array_map('intval', $liked_ids);

            return ["status" => "ok", "data" => ["liked_comments" => $liked_ids]];
        } catch (\Throwable $e) {
            $this->logWriteError("获取评论点赞状态失败", [
                'exception_message' => $e->getMessage(),
                'operation' => 'get_comment_like_status'
            ]);
            return ["status" => "error", "msg" => "获取点赞状态失败"];
        }
    }

    public function __destruct()
    {
        // 如果需要执行清理操作
    }

    /**
     * @apiName 删除动态
     * @method delete_feed
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param feed_id int 动态ID
     * @return {"status":"ok","msg":"删除成功"} | {"status":"error","msg":"..."}
     */
    public function delete_feed($uid, $token, $feed_id)
    {
        // 添加详细日志，帮助排查问题
        $this->logWriteError("删除动态请求", [
            'user_id' => $uid,
            'feed_id' => $feed_id,
            'operation' => 'delete_feed'
        ], 'INFO');
        
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            $this->logWriteError("删除动态参数错误", [
                'error' => 'uid或token无效',
                'operation' => 'delete_feed'
            ]);
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
            $this->logWriteError("删除动态授权失败", [
                'error' => '登录信息验证失败',
                'operation' => 'delete_feed'
            ]);
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        if (!check($feed_id, "intgt0")) {
            $this->logWriteError("删除动态参数错误", [
                'error' => '动态ID无效',
                'feed_id' => $feed_id,
                'operation' => 'delete_feed'
            ]);
            return ["status" => "error", "msg" => "无效的动态ID"];
        }
        
        $userId = (int)$uid;
        $feedId = (int)$feed_id;
        
        dbConn();
        try {
            // 检查feeds表是否存在
            $tableExists = Db()->_fetch("SHOW TABLES LIKE 'feeds'");
            if (!$tableExists) {
                $this->logWriteError("删除动态失败", [
                    'error' => '表不存在',
                    'table' => 'feeds',
                    'operation' => 'delete_feed'
                ]);
                return ["status" => "error", "msg" => "数据库表结构错误"];
            }
            
            // 先查询动态是否存在
            $feed = Db()->table('feeds')
                ->where("id = :feedId")
                ->prepareParam([':feedId' => $feedId])
                ->fetch();
                
            if (!$feed) {
                $this->logWriteError("删除动态失败", [
                    'error' => '动态不存在',
                    'feed_id' => $feedId,
                    'operation' => 'delete_feed'
                ]);
                return ["status" => "error", "msg" => "动态不存在"];
            }
            
            // 检查是否是用户自己的动态
            if ($feed['user_id'] != $userId) {
                $this->logWriteError("删除动态失败", [
                    'error' => '无权删除',
                    'feed_user_id' => $feed['user_id'],
                    'request_user_id' => $userId,
                    'operation' => 'delete_feed'
                ]);
                return ["status" => "error", "msg" => "您无权删除此动态"];
            }
            
            // 开始事务
            Db::begin();
            
            // 删除该动态的所有评论
            try {
                Db()->table('feed_comments')
                    ->where('feed_id = :feedId')
                    ->prepareParam([':feedId' => $feedId])
                    ->del();
                $this->logWriteError("已删除动态评论", [
                    'feed_id' => $feedId,
                    'operation' => 'delete_feed_comments'
                ], 'INFO');
            } catch (\Throwable $e) {
                $this->logWriteError("删除动态评论失败", [
                    'feed_id' => $feedId,
                    'exception_message' => $e->getMessage(),
                    'operation' => 'delete_feed_comments'
                ]);
                // 继续处理，不阻止删除动态本身
            }
                
            // 删除该动态的所有点赞记录
            try {
                Db()->table('user_feed_likes')
                    ->where('feed_id = :feedId')
                    ->prepareParam([':feedId' => $feedId])
                    ->del();
                $this->logWriteError("已删除动态点赞记录", [
                    'feed_id' => $feedId,
                    'operation' => 'delete_feed_likes'
                ], 'INFO');
            } catch (\Throwable $e) {
                $this->logWriteError("删除动态点赞记录失败", [
                    'feed_id' => $feedId,
                    'exception_message' => $e->getMessage(),
                    'operation' => 'delete_feed_likes'
                ]);
                // 继续处理，不阻止删除动态本身
            }
                
            // 删除该动态的所有收藏记录
            try {
                Db()->table('user_favorites')
                    ->where('item_id = :feedId AND item_type = "feed"')
                    ->prepareParam([':feedId' => $feedId])
                    ->del();
                $this->logWriteError("已删除动态收藏记录", [
                    'feed_id' => $feedId,
                    'operation' => 'delete_feed_favorites'
                ], 'INFO');
            } catch (\Throwable $e) {
                $this->logWriteError("删除动态收藏记录失败", [
                    'feed_id' => $feedId,
                    'exception_message' => $e->getMessage(),
                    'operation' => 'delete_feed_favorites'
                ]);
                // 继续处理，不阻止删除动态本身
            }
                
            // 删除动态本身
            $deleteResult = Db()->table('feeds')
                ->where('id = :feedId AND user_id = :userId')
                ->prepareParam([':feedId' => $feedId, ':userId' => $userId])
                ->del();
                
            if (!$deleteResult) {
                $this->logWriteError("删除动态失败", [
                    'feed_id' => $feedId,
                    'user_id' => $userId,
                    'operation' => 'delete_feed'
                ]);
                return ["status" => "error", "msg" => "删除动态失败"];
            }
            
            // 提交事务
            Db::commit();
            
            $this->user_log($userId, "删除动态【{$feedId}】成功");
            return ["status" => "ok", "msg" => "删除成功"];
            
        } catch (\Throwable $e) {
            // 回滚事务
            Db::rollback();
            
            $this->logWriteError("删除动态失败", [
                'exception_message' => $e->getMessage(),
                'operation' => 'delete_feed'
            ]);
            return ["status" => "error", "msg" => "删除失败，请稍后重试: " . $e->getMessage()];
        }
    }
    
    /**
     * @apiName 删除评论
     * @method delete_comment
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param comment_id int 评论ID
     * @param comment_type string 评论类型：feed=动态评论，card=日卡评论
     * @return {"status":"ok","msg":"删除成功"} | {"status":"error","msg":"..."}
     */
    public function delete_comment($uid, $token, $comment_id, $comment_type)
    {
        // 添加详细日志，帮助排查问题
        $this->logWriteError("删除评论请求", [
            'user_id' => $uid,
            'comment_id' => $comment_id,
            'comment_type' => $comment_type,
            'operation' => 'delete_comment'
        ], 'INFO');

        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            $this->logWriteError("删除评论参数错误", [
                'error' => 'uid或token无效',
                'operation' => 'delete_comment'
            ]);
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
            $this->logWriteError("删除评论授权失败", [
                'error' => '登录信息验证失败',
                'operation' => 'delete_comment'
            ]);
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        if (!check($comment_id, "intgt0")) {
            $this->logWriteError("删除评论参数错误", [
                'error' => '评论ID无效',
                'comment_id' => $comment_id,
                'operation' => 'delete_comment'
            ]);
            return ["status" => "error", "msg" => "无效的评论ID"];
        }
        if (empty($comment_type) || !in_array($comment_type, ['feed', 'card'])) {
            $this->logWriteError("删除评论参数错误", [
                'error' => '评论类型无效',
                'comment_type' => $comment_type,
                'operation' => 'delete_comment'
            ]);
            return ["status" => "error", "msg" => "无效的评论类型"];
        }
        
        $userId = (int)$uid;
        $commentId = (int)$comment_id;
        
        dbConn();
        try {
            // 获取表名
            $tableName = $comment_type . '_comments';
            
            // 检查表是否存在
            $tableExists = Db()->_fetch("SHOW TABLES LIKE '{$tableName}'");
            if (!$tableExists) {
                $this->logWriteError("删除评论失败", [
                    'error' => '表不存在',
                    'table_name' => $tableName,
                    'operation' => 'delete_comment'
                ]);
                return ["status" => "error", "msg" => "评论类型无效"];
            }
            
            // 先查询评论是否存在
            $comment = Db()->table($tableName)
                ->where("id = :commentId")
                ->prepareParam([':commentId' => $commentId])
                ->fetch();
                
            if (!$comment) {
                $this->logWriteError("删除评论失败", [
                    'error' => '评论不存在',
                    'comment_id' => $commentId,
                    'table_name' => $tableName,
                    'operation' => 'delete_comment'
                ]);
                return ["status" => "error", "msg" => "评论不存在"];
            }
            
            // 检查是否是用户自己的评论
            if ($comment['user_id'] != $userId) {
                $this->logWriteError("删除评论失败", [
                    'error' => '无权删除',
                    'comment_user_id' => $comment['user_id'],
                    'request_user_id' => $userId,
                    'operation' => 'delete_comment'
                ]);
                return ["status" => "error", "msg" => "您无权删除此评论"];
            }
            
            // 开始事务
            Db::begin();
            
            // 获取父级内容的ID，以便更新评论计数
            $parentIdColumn = $comment_type . '_id';
            if (!isset($comment[$parentIdColumn])) {
                $this->logWriteError("删除评论错误", [
                    'error' => '评论缺少父级ID字段',
                    'parent_id_column' => $parentIdColumn,
                    'operation' => 'delete_comment'
                ]);
                return ["status" => "error", "msg" => "评论数据结构错误"];
            }
            
            $parentId = $comment[$parentIdColumn];
            
            // 检查父级内容是否存在
            $parentTable = $comment_type === 'feed' ? 'feeds' : 'daily_cards';
            $parent = Db()->table($parentTable)
                ->where("id = :parentId")
                ->prepareParam([':parentId' => $parentId])
                ->fetch();
                
            if (!$parent) {
                $this->logWriteError("删除评论警告", [
                    'warning' => '父级内容不存在',
                    'parent_id' => $parentId,
                    'parent_table' => $parentTable,
                    'operation' => 'delete_comment'
                ], 'WARNING');
                // 继续处理，不阻止删除评论
            }
            
            // 删除评论点赞记录
            try {
                Db()->table('comment_likes')
                    ->where("comment_id = :commentId AND comment_type = :commentType")
                    ->prepareParam([':commentId' => $commentId, ':commentType' => $comment_type])
                    ->del();
            } catch (\Throwable $e) {
                $this->logWriteError("删除评论点赞记录失败", [
                    'exception_message' => $e->getMessage(),
                    'operation' => 'delete_comment_likes'
                ]);
                // 继续处理，不阻止删除评论本身
            }
                
            // 删除评论本身
            $deleteResult = Db()->table($tableName)
                ->where('id = :commentId AND user_id = :userId')
                ->prepareParam([':commentId' => $commentId, ':userId' => $userId])
                ->del();
                
            if (!$deleteResult) {
                $this->logWriteError("删除评论失败", [
                    'comment_id' => $commentId,
                    'user_id' => $userId,
                    'operation' => 'delete_comment'
                ]);
                return ["status" => "error", "msg" => "删除评论失败"];
            }
            
            // 更新父级内容的评论计数（如果父级存在）
            if ($parent) {
                Db()->_exec("UPDATE `{$parentTable}` SET `comment_count` = GREATEST(0, `comment_count` - 1) WHERE id = :parentId", [':parentId' => $parentId]);
            }
            
            // 提交事务
            Db::commit();
            
            $this->user_log($userId, "删除{$comment_type}评论【{$commentId}】成功");
            return ["status" => "ok", "msg" => "删除成功"];
            
        } catch (\Throwable $e) {
            // 回滚事务
            Db::rollback();
            
            $this->logWriteError("删除评论失败", [
                'exception_message' => $e->getMessage(),
                'operation' => 'delete_comment'
            ]);
            return ["status" => "error", "msg" => "删除失败，请稍后重试: " . $e->getMessage()];
        }
    }

    /**
     * @apiName 获取日卡详情页随机摘录
     * @method get_random_quotes_for_card
     * @POST
     * @param uid int 用户ID (必需, 用于权限控制和缓存)
     * @param token string token (必需)
     * @return {"status":"ok","data":{"content":"...","author":"...","source":"...","images":[...]}} | {"status":"error","msg":"..."} | {"status":"limit","msg":"..."}
     */
    public function get_random_quotes_for_card($uid, $token)
    {
        // 1. 用户验证
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "参数错误"];
        }

        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "请重新登录"];
        }

        dbConn();
        try {
            // 2. 获取用户信息，判断会员状态
            $user = Db()->table("user")
                ->select("is_huiyuan, huiyuan_end_time")
                ->where("uid = :uid")
                ->prepareParam([":uid" => $uid])
                ->fetch();

            if (!$user) {
                return ["status" => "error", "msg" => "用户不存在"];
            }

            // 3. 判断用户是否为会员
            $is_member = false;
            if ($user['is_huiyuan'] == 1) {
                if (empty($user['huiyuan_end_time']) || strtotime($user['huiyuan_end_time']) > time()) {
                    $is_member = true;
                }
            }

            // 4. 设置每日限制
            $daily_limit = $is_member ? 20 : 5;

            // 5. 检查今日已刷取次数
            $today = date('Y-m-d');
            $viewed_key = "quote_viewed:uid:{$uid}:{$today}";

            try {
                $redis = \lib\Redis::getInstance();
                if (!$redis) {
                    return ["status" => "error", "msg" => "系统繁忙，请稍后重试"];
                }

                // 获取今日已刷取的摘录ID列表
                $viewed_quotes = $redis->get($viewed_key);
                $viewed_list = $viewed_quotes ? json_decode($viewed_quotes, true) : [];

                // 检查是否达到每日限制
                if (count($viewed_list) >= $daily_limit) {
                    $limit_msg = $is_member
                        ? "您已经刷了二十条了给自己的眼睛放放假请明天再来吧"
                        : "您今天已经刷了五条了，开通会员可以畅享二十条摘录";
                    return ["status" => "limit", "msg" => $limit_msg];
                }

                // 6. 获取用户的摘录池
                $pool_key = "quote_pool:uid:{$uid}";
                $quote_pool = $redis->get($pool_key);

                if (!$quote_pool) {
                    // 首次访问，从数据库随机获取20条摘录
                    $quotes = $this->getRandomQuotesFromDB(20, $viewed_list);
                    if (empty($quotes)) {
                        return ["status" => "empty", "msg" => "暂无摘录内容"];
                    }

                    // {{ AURA-X: Fix - 修复Redis setex方法参数顺序. Confirmed via 寸止 }}
                    // 存储到Redis，24小时过期
                    $redis->setex($pool_key, 24 * 3600, json_encode($quotes));
                    $quote_pool = $quotes;
                } else {
                    $quote_pool = json_decode($quote_pool, true);
                }

                // 7. 从摘录池中获取下一条未刷过的摘录
                $next_quote = null;
                foreach ($quote_pool as $quote) {
                    if (!in_array($quote['id'], $viewed_list)) {
                        $next_quote = $quote;
                        break;
                    }
                }

                if (!$next_quote) {
                    // 摘录池已刷完，重新获取
                    $quotes = $this->getRandomQuotesFromDB(20, $viewed_list);
                    if (empty($quotes)) {
                        return ["status" => "empty", "msg" => "暂无更多摘录内容"];
                    }

                    $redis->setex($pool_key, 24 * 3600, json_encode($quotes));
                    $next_quote = $quotes[0];
                }

                // 8. 记录已刷取的摘录
                $viewed_list[] = $next_quote['id'];
                $redis->setex($viewed_key, 24 * 3600, json_encode($viewed_list));

                // 9. 处理返回数据格式
                $result = [
                    'content' => $next_quote['content'],
                    'author' => $next_quote['author'],
                    'source' => $next_quote['source'],
                    'images' => !empty($next_quote['images']) ? json_decode($next_quote['images'], true) : [],
                    'remaining_count' => $daily_limit - count($viewed_list),
                    'is_member' => $is_member
                ];

                return ["status" => "ok", "data" => $result];

            } catch (\Throwable $e) {
                $this->logWriteError("Redis操作失败", [
                    'exception_message' => $e->getMessage(),
                    'user_id' => $uid,
                    'operation' => 'get_random_quotes_for_card'
                ]);
                return ["status" => "error", "msg" => "系统繁忙，请稍后重试"];
            }

        } catch (\Throwable $e) {
            $this->logWriteError("获取随机摘录失败", [
                'exception_message' => $e->getMessage(),
                'user_id' => $uid,
                'operation' => 'get_random_quotes_for_card'
            ]);
            return ["status" => "error", "msg" => "获取摘录失败"];
        }
    }

    /**
     * 从数据库随机获取摘录
     * @param int $limit 获取数量
     * @param array $exclude_ids 排除的摘录ID列表
     * @return array
     */
    private function getRandomQuotesFromDB($limit = 20, $exclude_ids = [])
    {
        try {
            $where_clause = "privacy = 'public'";
            $params = [];

            if (!empty($exclude_ids)) {
                $placeholders = [];
                foreach ($exclude_ids as $index => $id) {
                    $placeholder = ":exclude_id_{$index}";
                    $placeholders[] = $placeholder;
                    $params[$placeholder] = $id;
                }
                $where_clause .= " AND id NOT IN (" . implode(',', $placeholders) . ")";
            }

            $sql = "SELECT id, content, author, source, images_json
                    FROM `huodong`.`quotes`
                    WHERE {$where_clause}
                    ORDER BY RAND()
                    LIMIT {$limit}";

            $quotes = Db()->_fetchAll($sql, $params);
            return $quotes ?: [];

        } catch (\Throwable $e) {
            $this->logWriteError("从数据库获取随机摘录失败", [
                'exception_message' => $e->getMessage(),
                'operation' => 'getRandomQuotesFromDB'
            ]);
            return [];
        }
    }

    /**
     * @apiName 获取摘录列表 (分页)
     * @method get_quotes
     * @POST
     * @param uid int 用户ID (可选, 用于获取点赞状态)
     * @param token string token (可选)
     * @param page int 页码 (默认 1)
     * @param page_size int 每页数量 (默认 20)
     * @param user_id int 要筛选的用户ID (可选, 0 表示不筛选)
     * @return {"status":"ok","data":{"list":[...], "total": N}} | {"status":"error","msg":"..."} | {"status":"empty"}
     */
    public function get_quotes($page = 1, $page_size = 20, $uid = 0, $token = "", $user_id = 0)
    {
        if (!check($page, "intgt0") || !check($page_size, "intgt0") || $page_size > 100) {
             return ["status" => "error", "msg" => "分页参数错误"];
        }
         if (!check($user_id, "integt0")) { // 允许为 0
             return ["status" => "error", "msg" => "用户筛选参数错误"];
        }

        $currentUserId = 0;
        if (!empty($uid) && !empty($token)) {
             if (!check($uid, "intgt0") || strlen($token) != 32) {
                // fail silently
             } else if ($this->auth($uid, $token)) {
                 $currentUserId = (int)$uid;
             }
        }

        $filterUserId = (int)$user_id;

        dbConn();
        try {
            // 构建查询条件
            $baseWhereConditions = [];
            $prepareParam = [];

            if ($filterUserId > 0) {
                $baseWhereConditions[] = "quotes.user_id = :filterUserId";
                $prepareParam[':filterUserId'] = $filterUserId;
            }

            // 隐私筛选：只显示公开的摘录，或者当前用户自己的摘录
            if ($currentUserId > 0) {
                $baseWhereConditions[] = "(quotes.privacy = 'public' OR quotes.user_id = :currentUserId)";
                $prepareParam[':currentUserId'] = $currentUserId;
            } else {
                $baseWhereConditions[] = "quotes.privacy = 'public'";
            }

            // 构建FROM和SELECT子句
            $fromClause = 'quotes LEFT JOIN user u ON u.uid = quotes.user_id';
            $selectFields = 'quotes.id, quotes.user_id, quotes.content, quotes.images_json, quotes.author, quotes.source, quotes.tags, quotes.privacy, quotes.created_at, u.nickname, u.avatar as avatar_url';

            // 构建WHERE子句
            $whereClauseString = "";
            if (!empty($baseWhereConditions)) {
                 $whereClauseString = " WHERE " . implode(" AND ", $baseWhereConditions);
            }

            // 计算总数
            $countSql = "SELECT COUNT(1) as totalCount FROM quotes LEFT JOIN user u ON u.uid = quotes.user_id" . $whereClauseString;
            $countResult = Db::_fetch($countSql, $prepareParam);
            $total = $countResult ? (int)$countResult['totalCount'] : 0;

            if ($total === 0) {
                return ["status" => "empty", "msg" => "暂无摘录数据"];
            }

            // 计算分页
            $limitOffset = ($page - 1) * $page_size;
            $limitRowCount = $page_size;
            $limitClause = "LIMIT {$limitOffset}, {$limitRowCount}";

            // 构建最终SQL
            $orderByClause = "ORDER BY quotes.created_at DESC";
            $sql = "SELECT {$selectFields} FROM {$fromClause}{$whereClauseString} {$orderByClause} {$limitClause}";

            // 执行查询
            $quotes = Db::_fetchAll($sql, $prepareParam);

            if (empty($quotes)) {
                return ["status" => "empty", "msg" => "暂无摘录数据"];
            }

            // 处理结果
            foreach ($quotes as &$quote) {
                // 处理图片数据
                if (!empty($quote['images_json'])) {
                    $images = json_decode($quote['images_json'], true);
                    $quote['images'] = is_array($images) ? $images : [];
                } else {
                    $quote['images'] = [];
                }
                unset($quote['images_json']);

                $quote['user'] = [
                    'uid' => $quote['user_id'],
                    'nickname' => $quote['nickname'],
                    'avatar_url' => $quote['avatar_url']
                ];
                unset($quote['nickname'], $quote['avatar_url']);
            }

            return ["status" => "ok", "data" => ["list" => $quotes, "total" => $total]];

        } catch (\Throwable $e) {
            $this->logWriteError("获取摘录列表失败", [
                'exception_message' => $e->getMessage(),
                'operation' => 'get_quotes_list'
            ]);
            return ["status" => "error", "msg" => "获取摘录列表失败"];
        }
    }

    /**
     * 获取摘录详情
     * @param uid int 用户ID
     * @param token string 用户token
     * @param quote_id int 摘录ID
     * @return {"status":"ok","data":{...}} | {"status":"error","msg":"..."} | {"status":"relogin"}
     */
    public function get_quote_detail($uid, $token, $quote_id)
    {
        // 1. 参数验证
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }

        if (!check($quote_id, "intgt0")) {
            return ["status" => "error", "msg" => "摘录ID参数错误"];
        }

        // 2. 用户验证
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        dbConn();
        try {
            // 3. 获取摘录详情
            $quote = Db()->_fetch("
                SELECT
                    q.*,
                    u.nickname as user_nickname,
                    u.avatar as user_avatar
                FROM quotes q
                LEFT JOIN user u ON q.user_id = u.uid
                WHERE q.id = :quote_id
            ", [":quote_id" => $quote_id]);

            if (!$quote) {
                return ["status" => "error", "msg" => "摘录不存在"];
            }

            // 4. 处理数据格式
            $quote['images'] = !empty($quote['images']) ? json_decode($quote['images'], true) : [];
            $quote['tags'] = !empty($quote['tags']) ? explode(',', $quote['tags']) : [];

            // 5. 添加用户信息
            $quote['user'] = [
                'uid' => $quote['user_id'],
                'nickname' => $quote['user_nickname'],
                'avatar_url' => $quote['user_avatar']
            ];

            return ["status" => "ok", "data" => $quote];

        } catch (\Throwable $e) {
            $this->logWriteError("获取摘录详情失败", [
                'exception_message' => $e->getMessage(),
                'operation' => 'get_quote_detail'
            ]);
            return ["status" => "error", "msg" => "获取摘录详情失败"];
        }
    }

    /**
     * 获取摘录评论列表
     * @param uid int 用户ID
     * @param token string 用户token
     * @param quote_id int 摘录ID
     * @param page int 页码 (默认 1)
     * @param page_size int 每页数量 (默认 20)
     * @return {"status":"ok","data":{"list":[...], "total": N}} | {"status":"error","msg":"..."} | {"status":"relogin"}
     */
    public function get_quote_comments($uid, $token, $quote_id, $page = 1, $page_size = 20)
    {
        // 1. 参数验证
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }

        if (!check($quote_id, "intgt0")) {
            return ["status" => "error", "msg" => "摘录ID参数错误"];
        }

        if (!check($page, "intgt0") || !check($page_size, "intgt0") || $page_size > 100) {
            return ["status" => "error", "msg" => "分页参数错误"];
        }

        // 2. 用户验证
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        dbConn();
        try {
            // 3. 获取评论总数
            $total = Db()->_fetch("SELECT COUNT(*) as count FROM quote_comments WHERE quote_id = :quote_id", [":quote_id" => $quote_id])['count'];

            if ($total == 0) {
                return ["status" => "empty", "msg" => "暂无评论"];
            }

            // 4. 获取评论列表
            $offset = ($page - 1) * $page_size;
            $comments = Db()->_fetchAll("
                SELECT
                    c.*,
                    u.nickname as user_nickname,
                    u.avatar as user_avatar
                FROM quote_comments c
                LEFT JOIN user u ON c.user_id = u.uid
                WHERE c.quote_id = :quote_id
                ORDER BY c.created_at DESC
                LIMIT :offset, :page_size
            ", [
                ":quote_id" => $quote_id,
                ":offset" => $offset,
                ":page_size" => $page_size
            ]);

            // 5. 处理数据格式
            foreach ($comments as &$comment) {
                $comment['user'] = [
                    'uid' => $comment['user_id'],
                    'nickname' => $comment['user_nickname'],
                    'avatar_url' => $comment['user_avatar']
                ];
                unset($comment['user_nickname'], $comment['user_avatar']);
            }

            return ["status" => "ok", "data" => ["list" => $comments, "total" => $total]];

        } catch (\Throwable $e) {
            $this->logWriteError("获取摘录评论失败", [
                'exception_message' => $e->getMessage(),
                'operation' => 'get_quote_comments'
            ]);
            return ["status" => "error", "msg" => "获取评论失败"];
        }
    }

    /**
     * 点赞/取消点赞摘录
     * @param uid int 用户ID
     * @param token string 用户token
     * @param id int 摘录ID
     * @return {"status":"ok","msg":"点赞成功/取消点赞","data":{"isLiked":true/false}} | {"status":"error","msg":"..."} | {"status":"relogin"}
     */
    public function like_quote($uid, $token, $id)
    {
        // 1. 参数验证
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }

        if (!check($id, "intgt0")) {
            return ["status" => "error", "msg" => "摘录ID参数错误"];
        }

        // 2. 用户验证
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        dbConn();
        try {
            // 3. 检查摘录是否存在
            $quote = Db()->_fetch("SELECT id FROM quotes WHERE id = :id", [":id" => $id]);
            if (!$quote) {
                return ["status" => "error", "msg" => "摘录不存在"];
            }

            // 4. 检查是否已点赞
            $existingLike = Db()->_fetch("
                SELECT id FROM quote_likes
                WHERE user_id = :user_id AND quote_id = :quote_id
            ", [":user_id" => $uid, ":quote_id" => $id]);

            if ($existingLike) {
                // 已点赞，取消点赞
                $deleteResult = Db()->_exec("
                    DELETE FROM quote_likes
                    WHERE user_id = :user_id AND quote_id = :quote_id
                ", [":user_id" => $uid, ":quote_id" => $id]);

                if ($deleteResult === false) {
                    return ["status" => "error", "msg" => "取消点赞失败"];
                }

                // 更新摘录点赞数
                Db()->_exec("UPDATE quotes SET like_count = GREATEST(0, like_count - 1) WHERE id = :id", [":id" => $id]);

                $this->user_log($uid, "取消点赞摘录【{$id}】");
                return ["status" => "ok", "msg" => "取消点赞", "data" => ["isLiked" => false]];
            } else {
                // 未点赞，添加点赞
                $insertData = [
                    'user_id' => $uid,
                    'quote_id' => $id,
                    'created_at' => date('Y-m-d H:i:s')
                ];
                $insertResult = Db()->table('quote_likes')->insert($insertData);

                if (!$insertResult) {
                    return ["status" => "error", "msg" => "点赞失败"];
                }

                // 更新摘录点赞数
                Db()->_exec("UPDATE quotes SET like_count = like_count + 1 WHERE id = :id", [":id" => $id]);

                $this->user_log($uid, "点赞摘录【{$id}】");
                return ["status" => "ok", "msg" => "点赞成功", "data" => ["isLiked" => true]];
            }

        } catch (\Throwable $e) {
            return ["status" => "error", "msg" => "点赞操作失败: " . $e->getMessage()];
        }
    }

    /**
     * 收藏/取消收藏摘录
     * @param uid int 用户ID
     * @param token string 用户token
     * @param id int 摘录ID
     * @return {"status":"ok","msg":"收藏成功/取消收藏","data":{"isFavorited":true/false}} | {"status":"error","msg":"..."} | {"status":"relogin"}
     */
    public function favorite_quote($uid, $token, $id)
    {
        // 1. 参数验证
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }

        if (!check($id, "intgt0")) {
            return ["status" => "error", "msg" => "摘录ID参数错误"];
        }

        // 2. 用户验证
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        dbConn();
        try {
            // 3. 检查摘录是否存在
            $quote = Db()->_fetch("SELECT id FROM quotes WHERE id = :id", [":id" => $id]);
            if (!$quote) {
                return ["status" => "error", "msg" => "摘录不存在"];
            }

            // 4. 检查是否已收藏
            $existingFavorite = Db()->_fetch("
                SELECT id FROM user_favorites
                WHERE user_id = :user_id AND item_id = :item_id AND item_type = 'quote'
            ", [":user_id" => $uid, ":item_id" => $id]);

            if ($existingFavorite) {
                // 已收藏，取消收藏
                $deleteResult = Db()->_exec("
                    DELETE FROM user_favorites
                    WHERE user_id = :user_id AND item_id = :item_id AND item_type = 'quote'
                ", [":user_id" => $uid, ":item_id" => $id]);

                if ($deleteResult === false) {
                    return ["status" => "error", "msg" => "取消收藏失败"];
                }

                $this->user_log($uid, "取消收藏摘录【{$id}】");
                return ["status" => "ok", "msg" => "取消收藏", "data" => ["isFavorited" => false]];
            } else {
                // 未收藏，添加收藏
                $insertData = [
                    'user_id' => $uid,
                    'item_id' => $id,
                    'item_type' => 'quote',
                    'created_at' => date('Y-m-d H:i:s')
                ];
                $insertResult = Db()->table('user_favorites')->insert($insertData);

                if (!$insertResult) {
                    return ["status" => "error", "msg" => "收藏失败"];
                }

                $this->user_log($uid, "收藏摘录【{$id}】");
                return ["status" => "ok", "msg" => "收藏成功", "data" => ["isFavorited" => true]];
            }

        } catch (\Throwable $e) {
            return ["status" => "error", "msg" => "收藏操作失败: " . $e->getMessage()];
        }
    }

    /**
     * 评论摘录
     * @param uid int 用户ID
     * @param token string 用户token
     * @param quote_id int 摘录ID
     * @param content string 评论内容
     * @param parent_id int 父评论ID (可选，用于回复)
     * @return {"status":"ok","msg":"评论成功","data":{"comment_id":N}} | {"status":"error","msg":"..."} | {"status":"relogin"}
     */
    public function comment_quote($uid, $token, $quote_id, $content, $parent_id = 0)
    {
        // 1. 参数验证
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }

        if (!check($quote_id, "intgt0")) {
            return ["status" => "error", "msg" => "摘录ID参数错误"];
        }

        if (empty($content) || mb_strlen($content) > 500) {
            return ["status" => "error", "msg" => "评论内容不能为空且不能超过500字"];
        }

        if (!check($parent_id, "integt0")) {
            return ["status" => "error", "msg" => "父评论ID参数错误"];
        }

        // 2. 用户验证
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        dbConn();
        try {
            // 3. 检查摘录是否存在
            $quote = Db()->_fetch("SELECT id, user_id FROM quotes WHERE id = :id", [":id" => $quote_id]);
            if (!$quote) {
                return ["status" => "error", "msg" => "摘录不存在"];
            }

            // 4. 如果是回复，检查父评论是否存在
            if ($parent_id > 0) {
                $parentComment = Db()->_fetch("
                    SELECT id FROM quote_comments
                    WHERE id = :id AND quote_id = :quote_id
                ", [":id" => $parent_id, ":quote_id" => $quote_id]);

                if (!$parentComment) {
                    return ["status" => "error", "msg" => "父评论不存在"];
                }
            }

            // 5. 插入评论
            $insertData = [
                'user_id' => $uid,
                'quote_id' => $quote_id,
                'content' => $content,
                'parent_id' => $parent_id,
                'created_at' => date('Y-m-d H:i:s')
            ];
            $insertResult = Db()->table('quote_comments')->insert($insertData);

            if (!$insertResult) {
                return ["status" => "error", "msg" => "评论失败"];
            }

            $comment_id = Db()->insertId();

            // 6. 更新摘录评论数
            Db()->_exec("UPDATE quotes SET comment_count = comment_count + 1 WHERE id = :id", [":id" => $quote_id]);

            // 7. 记录日志
            $this->user_log($uid, "评论摘录【{$quote_id}】");

            return ["status" => "ok", "msg" => "评论成功", "data" => ["comment_id" => $comment_id]];

        } catch (\Throwable $e) {
            return ["status" => "error", "msg" => "评论失败: " . $e->getMessage()];
        }
    }

    /**
     * @apiName 搜索作者
     * @method search_authors
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param keyword string 搜索关键词 (可选)
     * @param page int 页码 (默认1)
     * @param page_size int 每页数量 (默认20)
     * @return {"status":"ok","data":{"list":[],"total":N,"has_more":bool}} | {"status":"error","msg":"..."} | {"status":"relogin"}
     */
    public function search_authors($uid, $token, $keyword = '', $page = 1, $page_size = 20)
    {
        // {{ AURA-X: Add - 实现作者搜索API. Confirmed via 寸止 }}
        // 1. 用户验证
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        // 2. 参数验证和处理
        $page = max(1, (int)$page);
        $page_size = max(1, min(100, (int)$page_size)); // 限制每页最多100条
        $offset = ($page - 1) * $page_size;
        $keyword = trim($keyword);

        try {
            // 3. 检查缓存（仅对有关键词的搜索进行缓存）
            $cacheKey = null;
            if (!empty($keyword)) {
                $cacheKey = $this->getCacheKey('authors', $keyword, $page, $page_size);
                $cachedResult = $this->getCache($cacheKey);
                if ($cachedResult !== null) {
                    return $cachedResult;
                }
            }

            dbConn();

            // 4. 构建搜索条件
            $where = "status = :status";
            $params = [':status' => 1]; // 只查询正常状态的作者

            if (!empty($keyword)) {
                $where .= " AND name LIKE :keyword";
                $params[':keyword'] = '%' . $keyword . '%';
            }

            // 4. 获取总数
            $total_sql = "SELECT COUNT(*) as total FROM authors WHERE {$where}";
            $total_result = Db()->_fetch($total_sql, $params);
            $total = $total_result['total'] ?? 0;

            // 5. 获取列表（按引用次数和创建时间排序）
            $list_sql = "SELECT id, name, avatar, description, quote_count, official_verified, category
                         FROM authors
                         WHERE {$where}
                         ORDER BY quote_count DESC, created_at DESC
                         LIMIT {$offset}, {$page_size}";

            $list = Db()->_fetchAll($list_sql, $params);

            // 6. 处理返回数据（添加关键词高亮）
            $result_list = [];
            if (!empty($list)) {
                foreach ($list as $item) {
                    $result_list[] = [
                        'id' => (int)$item['id'],
                        'name' => $item['name'],
                        'name_highlighted' => !empty($keyword) ? $this->highlightKeyword($item['name'], $keyword) : $item['name'],
                        'avatar' => $item['avatar'],
                        'description' => $item['description'] ?: '',
                        'description_highlighted' => !empty($keyword) ? $this->highlightKeyword($item['description'] ?: '', $keyword) : ($item['description'] ?: ''),
                        'quote_count' => (int)$item['quote_count'],
                        'official_verified' => (int)$item['official_verified'],
                        'category' => $item['category'] ?: ''
                    ];
                }
            }

            $result = [
                "status" => "ok",
                "data" => [
                    "list" => $result_list,
                    "total" => (int)$total,
                    "has_more" => ($offset + $page_size) < $total
                ]
            ];

            // 7. 设置缓存（仅对有关键词的搜索进行缓存）
            if (!empty($keyword) && $cacheKey !== null) {
                $this->setCache($cacheKey, $result);
            }

            return $result;

        } catch (\Throwable $e) {
            $this->logWriteError("搜索作者失败", [
                'exception_message' => $e->getMessage(),
                'keyword' => $keyword,
                'operation' => 'search_authors'
            ]);
            return ["status" => "error", "msg" => "搜索失败，请稍后重试"];
        }
    }

    /**
     * @apiName 搜索出处
     * @method search_sources
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param keyword string 搜索关键词 (可选)
     * @param page int 页码 (默认1)
     * @param page_size int 每页数量 (默认20)
     * @return {"status":"ok","data":{"list":[],"total":N,"has_more":bool}} | {"status":"error","msg":"..."} | {"status":"relogin"}
     */
    public function search_sources($uid, $token, $keyword = '', $page = 1, $page_size = 20)
    {
        // {{ AURA-X: Add - 实现出处搜索API. Confirmed via 寸止 }}
        // 1. 用户验证
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        // 2. 参数验证和处理
        $page = max(1, (int)$page);
        $page_size = max(1, min(100, (int)$page_size)); // 限制每页最多100条
        $offset = ($page - 1) * $page_size;
        $keyword = trim($keyword);

        try {
            // 3. 检查缓存（仅对有关键词的搜索进行缓存）
            $cacheKey = null;
            if (!empty($keyword)) {
                $cacheKey = $this->getCacheKey('sources', $keyword, $page, $page_size);
                $cachedResult = $this->getCache($cacheKey);
                if ($cachedResult !== null) {
                    return $cachedResult;
                }
            }

            dbConn();

            // 4. 构建搜索条件
            $where = "status = :status";
            $params = [':status' => 1]; // 只查询正常状态的出处

            if (!empty($keyword)) {
                $where .= " AND name LIKE :keyword";
                $params[':keyword'] = '%' . $keyword . '%';
            }

            // 4. 获取总数
            $total_sql = "SELECT COUNT(*) as total FROM sources WHERE {$where}";
            $total_result = Db()->_fetch($total_sql, $params);
            $total = $total_result['total'] ?? 0;

            // 5. 获取列表（按引用次数和创建时间排序）
            $list_sql = "SELECT id, name, cover_image, description, quote_count, official_verified, category, publisher
                         FROM sources
                         WHERE {$where}
                         ORDER BY quote_count DESC, created_at DESC
                         LIMIT {$offset}, {$page_size}";

            $list = Db()->_fetchAll($list_sql, $params);

            // 6. 处理返回数据（添加关键词高亮）
            $result_list = [];
            if (!empty($list)) {
                foreach ($list as $item) {
                    $result_list[] = [
                        'id' => (int)$item['id'],
                        'name' => $item['name'],
                        'name_highlighted' => !empty($keyword) ? $this->highlightKeyword($item['name'], $keyword) : $item['name'],
                        'cover_image' => $item['cover_image'],
                        'description' => $item['description'] ?: '',
                        'description_highlighted' => !empty($keyword) ? $this->highlightKeyword($item['description'] ?: '', $keyword) : ($item['description'] ?: ''),
                        'quote_count' => (int)$item['quote_count'],
                        'official_verified' => (int)$item['official_verified'],
                        'category' => $item['category'] ?: '',
                        'publisher' => $item['publisher'] ?: ''
                    ];
                }
            }

            $result = [
                "status" => "ok",
                "data" => [
                    "list" => $result_list,
                    "total" => (int)$total,
                    "has_more" => ($offset + $page_size) < $total
                ]
            ];

            // 7. 设置缓存（仅对有关键词的搜索进行缓存）
            if (!empty($keyword) && $cacheKey !== null) {
                $this->setCache($cacheKey, $result);
            }

            return $result;

        } catch (\Throwable $e) {
            $this->logWriteError("搜索出处失败", [
                'exception_message' => $e->getMessage(),
                'keyword' => $keyword,
                'operation' => 'search_sources'
            ]);
            return ["status" => "error", "msg" => "搜索失败，请稍后重试"];
        }
    }

    /**
     * @apiName 创建作者
     * @method create_author
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param name string 作者姓名 (必填)
     * @param avatar string 作者头像URL (可选)
     * @param description string 作者简介 (可选)
     * @param birth_year string 出生年份 (可选)
     * @param nationality string 国籍 (可选)
     * @param category string 作者分类 (可选)
     * @param force_create int 强制创建 (可选，1=强制创建，忽略相似检查)
     * @return {"status":"ok","msg":"创建成功","data":{"author_id": N}} | {"status":"warning","msg":"发现相似作者","data":{"similar_authors": [...]}} | {"status":"error","msg":"..."} | {"status":"relogin"}
     */
    public function create_author($uid, $token, $name, $avatar = '', $description = '', $birth_year = '', $nationality = '', $category = '', $force_create = 0)
    {
        // {{ AURA-X: Add - 实现创建作者API. Confirmed via 寸止 }}
        // 1. 用户验证
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        // 2. 参数验证
        $name = trim($name);
        if (empty($name)) {
            return ["status" => "error", "msg" => "作者姓名不能为空"];
        }
        if (mb_strlen($name) > 100) {
            return ["status" => "error", "msg" => "作者姓名过长"];
        }

        // 3. 清理和验证其他参数
        $avatar = !empty($avatar) ? trim($avatar) : null;
        $description = !empty($description) ? trim($description) : null;
        $birth_year = !empty($birth_year) ? trim($birth_year) : null;
        $nationality = !empty($nationality) ? trim($nationality) : null;
        $category = !empty($category) ? trim($category) : null;

        // 4. 验证头像URL格式
        if ($avatar && !filter_var($avatar, FILTER_VALIDATE_URL)) {
            return ["status" => "error", "msg" => "头像URL格式不正确"];
        }

        try {
            dbConn();

            // 5. 检查作者名称是否已存在（精确匹配）
            $check_sql = "SELECT id FROM authors WHERE name = :name AND status = 1";
            $existing = Db()->_fetch($check_sql, [':name' => $name]);
            if ($existing) {
                return ["status" => "error", "msg" => "该作者已存在"];
            }

            // 6. 检查相似作者（模糊匹配）- 除非强制创建
            if (!$force_create) {
                $similarAuthors = $this->findSimilarAuthors($name, 80.0, 5);
                if (!empty($similarAuthors)) {
                    return [
                        "status" => "warning",
                        "msg" => "发现相似的作者，请确认是否继续创建",
                        "data" => [
                            "similar_authors" => $similarAuthors,
                            "can_proceed" => true
                        ]
                    ];
                }
            }

            // 6. 检查用户权限，设置初始状态
            $userInfo = Db::_fetch("SELECT role_type FROM user WHERE uid = :uid", [':uid' => $uid]);
            $isAdmin = in_array($userInfo['role_type'], ['0', '1', '4']); // 管理员、分会长、城市分会长
            $initialStatus = $isAdmin ? 1 : 2; // 管理员直接通过，普通用户需要审核

            // 7. 准备插入数据
            $data = [
                'name' => htmlspecialchars($name),
                'avatar' => $avatar,
                'description' => $description ? htmlspecialchars($description) : null,
                'birth_year' => $birth_year,
                'nationality' => $nationality ? htmlspecialchars($nationality) : null,
                'category' => $category ? htmlspecialchars($category) : null,
                'quote_count' => 0,
                'official_verified' => 0,
                'created_by' => (int)$uid,
                'status' => $initialStatus,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // 8. 执行插入
            $insertResult = Db()->table('authors')->insert($data);
            if (!$insertResult) {
                $this->logWriteError("插入作者数据失败", [
                    'user_id' => $uid,
                    'author_name' => $name,
                    'operation' => 'create_author'
                ]);
                return ["status" => "error", "msg" => "创建失败"];
            }

            $authorId = Db()->insertId();
            if (!$authorId) {
                $this->logWriteError("获取作者插入ID失败", [
                    'user_id' => $uid,
                    'operation' => 'create_author'
                ]);
                return ["status" => "error", "msg" => "创建失败"];
            }

            $this->user_log($uid, "创建作者【{$name}】");

            // 清除作者搜索缓存
            $this->clearSearchCache('authors');

            // 根据状态返回不同消息
            $message = $isAdmin ? "创建成功" : "创建成功，等待审核";
            return ["status" => "ok", "msg" => $message, "data" => ['author_id' => $authorId, 'needs_approval' => !$isAdmin]];

        } catch (\Throwable $e) {
            $this->logWriteError("创建作者失败", [
                'exception_message' => $e->getMessage(),
                'author_name' => $name,
                'operation' => 'create_author'
            ]);
            return ["status" => "error", "msg" => "创建失败，请稍后重试"];
        }
    }

    /**
     * @apiName 创建出处
     * @method create_source
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param name string 出处名称 (必填)
     * @param cover_image string 封面图片URL (可选)
     * @param description string 出处描述 (可选)
     * @param publish_year string 发布年份 (可选)
     * @param publisher string 出版社/发布方 (可选)
     * @param category string 出处分类 (可选)
     * @param force_create int 强制创建 (可选，1=强制创建，忽略相似检查)
     * @return {"status":"ok","msg":"创建成功","data":{"source_id": N}} | {"status":"warning","msg":"发现相似出处","data":{"similar_sources": [...]}} | {"status":"error","msg":"..."} | {"status":"relogin"}
     */
    public function create_source($uid, $token, $name, $cover_image = '', $description = '', $publish_year = '', $publisher = '', $category = '', $force_create = 0)
    {
        // {{ AURA-X: Add - 实现创建出处API. Confirmed via 寸止 }}
        // 1. 用户验证
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        // 2. 参数验证
        $name = trim($name);
        if (empty($name)) {
            return ["status" => "error", "msg" => "出处名称不能为空"];
        }
        if (mb_strlen($name) > 255) {
            return ["status" => "error", "msg" => "出处名称过长"];
        }

        // 3. 清理和验证其他参数
        $cover_image = !empty($cover_image) ? trim($cover_image) : null;
        $description = !empty($description) ? trim($description) : null;
        $publish_year = !empty($publish_year) ? trim($publish_year) : null;
        $publisher = !empty($publisher) ? trim($publisher) : null;
        $category = !empty($category) ? trim($category) : null;

        // 4. 验证封面图片URL格式
        if ($cover_image && !filter_var($cover_image, FILTER_VALIDATE_URL)) {
            return ["status" => "error", "msg" => "封面图片URL格式不正确"];
        }

        try {
            dbConn();

            // 5. 检查出处名称是否已存在（精确匹配）
            $check_sql = "SELECT id FROM sources WHERE name = :name AND status = 1";
            $existing = Db()->_fetch($check_sql, [':name' => $name]);
            if ($existing) {
                return ["status" => "error", "msg" => "该出处已存在"];
            }

            // 6. 检查相似出处（模糊匹配）- 除非强制创建
            if (!$force_create) {
                $similarSources = $this->findSimilarSources($name, 80.0, 5);
                if (!empty($similarSources)) {
                    return [
                        "status" => "warning",
                        "msg" => "发现相似的出处，请确认是否继续创建",
                        "data" => [
                            "similar_sources" => $similarSources,
                            "can_proceed" => true
                        ]
                    ];
                }
            }

            // 7. 检查用户权限，设置初始状态
            $userInfo = Db::_fetch("SELECT role_type FROM user WHERE uid = :uid", [':uid' => $uid]);
            $isAdmin = in_array($userInfo['role_type'], ['0', '1', '4']); // 管理员、分会长、城市分会长
            $initialStatus = $isAdmin ? 1 : 2; // 管理员直接通过，普通用户需要审核

            // 7. 准备插入数据
            $data = [
                'name' => htmlspecialchars($name),
                'cover_image' => $cover_image,
                'description' => $description ? htmlspecialchars($description) : null,
                'publish_year' => $publish_year,
                'publisher' => $publisher ? htmlspecialchars($publisher) : null,
                'category' => $category ? htmlspecialchars($category) : null,
                'quote_count' => 0,
                'official_verified' => 0,
                'created_by' => (int)$uid,
                'status' => $initialStatus,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // 8. 执行插入
            $insertResult = Db()->table('sources')->insert($data);
            if (!$insertResult) {
                $this->logWriteError("插入出处数据失败", [
                    'user_id' => $uid,
                    'source_name' => $name,
                    'operation' => 'create_source'
                ]);
                return ["status" => "error", "msg" => "创建失败"];
            }

            $sourceId = Db()->insertId();
            if (!$sourceId) {
                $this->logWriteError("获取出处插入ID失败", [
                    'user_id' => $uid,
                    'operation' => 'create_source'
                ]);
                return ["status" => "error", "msg" => "创建失败"];
            }

            $this->user_log($uid, "创建出处【{$name}】");

            // 清除出处搜索缓存
            $this->clearSearchCache('sources');

            // 根据状态返回不同消息
            $message = $isAdmin ? "创建成功" : "创建成功，等待审核";
            return ["status" => "ok", "msg" => $message, "data" => ['source_id' => $sourceId, 'needs_approval' => !$isAdmin]];

        } catch (\Throwable $e) {
            $this->logWriteError("创建出处失败", [
                'exception_message' => $e->getMessage(),
                'source_name' => $name,
                'operation' => 'create_source'
            ]);
            return ["status" => "error", "msg" => "创建失败，请稍后重试"];
        }
    }

    /**
     * @apiName 获取作者详情
     * @method get_author_detail
     * @POST
     * @param uid int 用户ID (可选)
     * @param token string token (可选)
     * @param author_id int 作者ID
     * @return {"status":"ok","data":{"author": {...}}} | {"status":"error","msg":"..."}
     */
    public function get_author_detail($uid = 0, $token = '', $author_id)
    {
        // {{ AURA-X: Add - 获取作者详情API. Confirmed via 寸止 }}
        try {
            // 验证参数
            if (!check($author_id, "intgt0")) {
                return ["status" => "error", "msg" => "作者ID无效"];
            }

            // 验证用户身份（可选）
            $userId = 0;
            if (!empty($uid) && !empty($token)) {
                if (check($uid, "intgt0") && strlen($token) == 32 && $this->auth($uid, $token)) {
                    $userId = (int)$uid;
                }
            }

            dbConn();

            // 获取作者详情
            $author = Db::_fetch("
                SELECT id, name, category, description, birth_year,
                       nationality, avatar, quote_count, created_at, updated_at
                FROM authors
                WHERE id = :id AND status = 1
            ", [':id' => $author_id]);

            if (!$author) {
                return ["status" => "error", "msg" => "作者不存在"];
            }

            // 格式化数据
            $author['quote_count'] = (int)$author['quote_count'];
            $author['birth_year'] = $author['birth_year'] ?: null;

            return ["status" => "ok", "data" => ["author" => $author]];

        } catch (\Throwable $e) {
            $this->logWriteError("获取作者详情失败", [
                'exception_message' => $e->getMessage(),
                'author_id' => $author_id,
                'operation' => 'get_author_detail'
            ]);
            return ["status" => "error", "msg" => "获取作者详情失败"];
        }
    }

    /**
     * @apiName 获取出处详情
     * @method get_source_detail
     * @POST
     * @param uid int 用户ID (可选)
     * @param token string token (可选)
     * @param source_id int 出处ID
     * @return {"status":"ok","data":{"source": {...}}} | {"status":"error","msg":"..."}
     */
    public function get_source_detail($uid = 0, $token = '', $source_id)
    {
        // {{ AURA-X: Add - 获取出处详情API. Confirmed via 寸止 }}
        try {
            // 验证参数
            if (!check($source_id, "intgt0")) {
                return ["status" => "error", "msg" => "出处ID无效"];
            }

            // 验证用户身份（可选）
            $userId = 0;
            if (!empty($uid) && !empty($token)) {
                if (check($uid, "intgt0") && strlen($token) == 32 && $this->auth($uid, $token)) {
                    $userId = (int)$uid;
                }
            }

            dbConn();

            // 获取出处详情
            $source = Db::_fetch("
                SELECT id, name, category, description, publisher, publish_year,
                       cover_image, quote_count, created_at, updated_at
                FROM sources
                WHERE id = :id AND status = 1
            ", [':id' => $source_id]);

            if (!$source) {
                return ["status" => "error", "msg" => "出处不存在"];
            }

            // 格式化数据
            $source['quote_count'] = (int)$source['quote_count'];
            $source['publish_year'] = $source['publish_year'] ?: null;

            return ["status" => "ok", "data" => ["source" => $source]];

        } catch (\Throwable $e) {
            $this->logWriteError("获取出处详情失败", [
                'exception_message' => $e->getMessage(),
                'source_id' => $source_id,
                'operation' => 'get_source_detail'
            ]);
            return ["status" => "error", "msg" => "获取出处详情失败"];
        }
    }

    /**
     * @apiName 软删除作者
     * @method soft_delete_author
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param author_id int 作者ID
     * @return {"status":"ok","msg":"删除成功"} | {"status":"error","msg":"..."}
     */
    public function soft_delete_author($uid, $token, $author_id)
    {
        // {{ AURA-X: Add - 实现作者软删除功能. Confirmed via 寸止 }}
        // 1. 用户验证
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        // 2. 参数验证
        if (!check($author_id, "intgt0")) {
            return ["status" => "error", "msg" => "作者ID无效"];
        }

        try {
            dbConn();

            // 3. 检查作者是否存在且为正常状态
            $author = Db::_fetch("SELECT id, created_by FROM authors WHERE id = :id AND status = 1", [':id' => $author_id]);
            if (!$author) {
                return ["status" => "error", "msg" => "作者不存在或已删除"];
            }

            // 4. 权限检查：只有创建者或管理员可以删除
            $userInfo = Db::_fetch("SELECT role_type FROM user WHERE uid = :uid", [':uid' => $uid]);
            $isAdmin = in_array($userInfo['role_type'], ['0', '1', '4']); // 管理员、分会长、城市分会长
            $isCreator = (int)$author['created_by'] === (int)$uid;

            if (!$isAdmin && !$isCreator) {
                return ["status" => "error", "msg" => "无权限删除此作者"];
            }

            // 5. 执行软删除
            $result = Db::_exec("UPDATE authors SET status = 0, updated_at = NOW() WHERE id = :id", [':id' => $author_id]);

            if ($result) {
                return ["status" => "ok", "msg" => "删除成功"];
            } else {
                return ["status" => "error", "msg" => "删除失败"];
            }

        } catch (\Throwable $e) {
            $this->logWriteError("软删除作者失败", [
                'exception_message' => $e->getMessage(),
                'author_id' => $author_id,
                'uid' => $uid,
                'operation' => 'soft_delete_author'
            ]);
            return ["status" => "error", "msg" => "删除失败"];
        }
    }

    /**
     * @apiName 软删除出处
     * @method soft_delete_source
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param source_id int 出处ID
     * @return {"status":"ok","msg":"删除成功"} | {"status":"error","msg":"..."}
     */
    public function soft_delete_source($uid, $token, $source_id)
    {
        // {{ AURA-X: Add - 实现出处软删除功能. Confirmed via 寸止 }}
        // 1. 用户验证
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        // 2. 参数验证
        if (!check($source_id, "intgt0")) {
            return ["status" => "error", "msg" => "出处ID无效"];
        }

        try {
            dbConn();

            // 3. 检查出处是否存在且为正常状态
            $source = Db::_fetch("SELECT id, created_by FROM sources WHERE id = :id AND status = 1", [':id' => $source_id]);
            if (!$source) {
                return ["status" => "error", "msg" => "出处不存在或已删除"];
            }

            // 4. 权限检查：只有创建者或管理员可以删除
            $userInfo = Db::_fetch("SELECT role_type FROM user WHERE uid = :uid", [':uid' => $uid]);
            $isAdmin = in_array($userInfo['role_type'], ['0', '1', '4']); // 管理员、分会长、城市分会长
            $isCreator = (int)$source['created_by'] === (int)$uid;

            if (!$isAdmin && !$isCreator) {
                return ["status" => "error", "msg" => "无权限删除此出处"];
            }

            // 5. 执行软删除
            $result = Db::_exec("UPDATE sources SET status = 0, updated_at = NOW() WHERE id = :id", [':id' => $source_id]);

            if ($result) {
                return ["status" => "ok", "msg" => "删除成功"];
            } else {
                return ["status" => "error", "msg" => "删除失败"];
            }

        } catch (\Throwable $e) {
            $this->logWriteError("软删除出处失败", [
                'exception_message' => $e->getMessage(),
                'source_id' => $source_id,
                'uid' => $uid,
                'operation' => 'soft_delete_source'
            ]);
            return ["status" => "error", "msg" => "删除失败"];
        }
    }

    /**
     * @apiName 恢复作者
     * @method restore_author
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param author_id int 作者ID
     * @return {"status":"ok","msg":"恢复成功"} | {"status":"error","msg":"..."}
     */
    public function restore_author($uid, $token, $author_id)
    {
        // {{ AURA-X: Add - 实现作者恢复功能. Confirmed via 寸止 }}
        // 1. 用户验证
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        // 2. 参数验证
        if (!check($author_id, "intgt0")) {
            return ["status" => "error", "msg" => "作者ID无效"];
        }

        try {
            dbConn();

            // 3. 检查作者是否存在且为删除状态
            $author = Db::_fetch("SELECT id, created_by FROM authors WHERE id = :id AND status = 0", [':id' => $author_id]);
            if (!$author) {
                return ["status" => "error", "msg" => "作者不存在或未删除"];
            }

            // 4. 权限检查：只有管理员可以恢复
            $userInfo = Db::_fetch("SELECT role_type FROM user WHERE uid = :uid", [':uid' => $uid]);
            $isAdmin = in_array($userInfo['role_type'], ['0', '1', '4']); // 管理员、分会长、城市分会长

            if (!$isAdmin) {
                return ["status" => "error", "msg" => "无权限恢复此作者"];
            }

            // 5. 执行恢复
            $result = Db::_exec("UPDATE authors SET status = 1, updated_at = NOW() WHERE id = :id", [':id' => $author_id]);

            if ($result) {
                return ["status" => "ok", "msg" => "恢复成功"];
            } else {
                return ["status" => "error", "msg" => "恢复失败"];
            }

        } catch (\Throwable $e) {
            $this->logWriteError("恢复作者失败", [
                'exception_message' => $e->getMessage(),
                'author_id' => $author_id,
                'uid' => $uid,
                'operation' => 'restore_author'
            ]);
            return ["status" => "error", "msg" => "恢复失败"];
        }
    }

    /**
     * @apiName 恢复出处
     * @method restore_source
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param source_id int 出处ID
     * @return {"status":"ok","msg":"恢复成功"} | {"status":"error","msg":"..."}
     */
    public function restore_source($uid, $token, $source_id)
    {
        // {{ AURA-X: Add - 实现出处恢复功能. Confirmed via 寸止 }}
        // 1. 用户验证
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        // 2. 参数验证
        if (!check($source_id, "intgt0")) {
            return ["status" => "error", "msg" => "出处ID无效"];
        }

        try {
            dbConn();

            // 3. 检查出处是否存在且为删除状态
            $source = Db::_fetch("SELECT id, created_by FROM sources WHERE id = :id AND status = 0", [':id' => $source_id]);
            if (!$source) {
                return ["status" => "error", "msg" => "出处不存在或未删除"];
            }

            // 4. 权限检查：只有管理员可以恢复
            $userInfo = Db::_fetch("SELECT role_type FROM user WHERE uid = :uid", [':uid' => $uid]);
            $isAdmin = in_array($userInfo['role_type'], ['0', '1', '4']); // 管理员、分会长、城市分会长

            if (!$isAdmin) {
                return ["status" => "error", "msg" => "无权限恢复此出处"];
            }

            // 5. 执行恢复
            $result = Db::_exec("UPDATE sources SET status = 1, updated_at = NOW() WHERE id = :id", [':id' => $source_id]);

            if ($result) {
                return ["status" => "ok", "msg" => "恢复成功"];
            } else {
                return ["status" => "error", "msg" => "恢复失败"];
            }

        } catch (\Throwable $e) {
            $this->logWriteError("恢复出处失败", [
                'exception_message' => $e->getMessage(),
                'source_id' => $source_id,
                'uid' => $uid,
                'operation' => 'restore_source'
            ]);
            return ["status" => "error", "msg" => "恢复失败"];
        }
    }

    /**
     * @apiName 审核通过作者
     * @method approve_author
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param author_id int 作者ID
     * @return {"status":"ok","msg":"审核通过"} | {"status":"error","msg":"..."}
     */
    public function approve_author($uid, $token, $author_id)
    {
        // {{ AURA-X: Add - 实现作者审核通过功能. Confirmed via 寸止 }}
        // 1. 用户验证
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        // 2. 参数验证
        if (!check($author_id, "intgt0")) {
            return ["status" => "error", "msg" => "作者ID无效"];
        }

        try {
            dbConn();

            // 3. 权限检查：只有管理员可以审核
            $userInfo = Db::_fetch("SELECT role_type FROM user WHERE uid = :uid", [':uid' => $uid]);
            $isAdmin = in_array($userInfo['role_type'], ['0', '1', '4']); // 管理员、分会长、城市分会长

            if (!$isAdmin) {
                return ["status" => "error", "msg" => "无权限审核作者"];
            }

            // 4. 检查作者是否存在且为待审核状态
            $author = Db::_fetch("SELECT id, name FROM authors WHERE id = :id AND status = 2", [':id' => $author_id]);
            if (!$author) {
                return ["status" => "error", "msg" => "作者不存在或不在待审核状态"];
            }

            // 5. 执行审核通过
            $result = Db::_exec("UPDATE authors SET status = 1, updated_at = NOW() WHERE id = :id", [':id' => $author_id]);

            if ($result) {
                $this->user_log($uid, "审核通过作者【{$author['name']}】");
                return ["status" => "ok", "msg" => "审核通过"];
            } else {
                return ["status" => "error", "msg" => "审核失败"];
            }

        } catch (\Throwable $e) {
            $this->logWriteError("审核作者失败", [
                'exception_message' => $e->getMessage(),
                'author_id' => $author_id,
                'uid' => $uid,
                'operation' => 'approve_author'
            ]);
            return ["status" => "error", "msg" => "审核失败"];
        }
    }

    /**
     * @apiName 审核拒绝作者
     * @method reject_author
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param author_id int 作者ID
     * @return {"status":"ok","msg":"审核拒绝"} | {"status":"error","msg":"..."}
     */
    public function reject_author($uid, $token, $author_id)
    {
        // {{ AURA-X: Add - 实现作者审核拒绝功能. Confirmed via 寸止 }}
        // 1. 用户验证
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        // 2. 参数验证
        if (!check($author_id, "intgt0")) {
            return ["status" => "error", "msg" => "作者ID无效"];
        }

        try {
            dbConn();

            // 3. 权限检查：只有管理员可以审核
            $userInfo = Db::_fetch("SELECT role_type FROM user WHERE uid = :uid", [':uid' => $uid]);
            $isAdmin = in_array($userInfo['role_type'], ['0', '1', '4']); // 管理员、分会长、城市分会长

            if (!$isAdmin) {
                return ["status" => "error", "msg" => "无权限审核作者"];
            }

            // 4. 检查作者是否存在且为待审核状态
            $author = Db::_fetch("SELECT id, name FROM authors WHERE id = :id AND status = 2", [':id' => $author_id]);
            if (!$author) {
                return ["status" => "error", "msg" => "作者不存在或不在待审核状态"];
            }

            // 5. 执行审核拒绝（软删除）
            $result = Db::_exec("UPDATE authors SET status = 0, updated_at = NOW() WHERE id = :id", [':id' => $author_id]);

            if ($result) {
                $this->user_log($uid, "审核拒绝作者【{$author['name']}】");
                return ["status" => "ok", "msg" => "审核拒绝"];
            } else {
                return ["status" => "error", "msg" => "审核失败"];
            }

        } catch (\Throwable $e) {
            $this->logWriteError("审核拒绝作者失败", [
                'exception_message' => $e->getMessage(),
                'author_id' => $author_id,
                'uid' => $uid,
                'operation' => 'reject_author'
            ]);
            return ["status" => "error", "msg" => "审核失败"];
        }
    }

    /**
     * @apiName 审核通过出处
     * @method approve_source
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param source_id int 出处ID
     * @return {"status":"ok","msg":"审核通过"} | {"status":"error","msg":"..."}
     */
    public function approve_source($uid, $token, $source_id)
    {
        // {{ AURA-X: Add - 实现出处审核通过功能. Confirmed via 寸止 }}
        // 1. 用户验证
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        // 2. 参数验证
        if (!check($source_id, "intgt0")) {
            return ["status" => "error", "msg" => "出处ID无效"];
        }

        try {
            dbConn();

            // 3. 权限检查：只有管理员可以审核
            $userInfo = Db::_fetch("SELECT role_type FROM user WHERE uid = :uid", [':uid' => $uid]);
            $isAdmin = in_array($userInfo['role_type'], ['0', '1', '4']); // 管理员、分会长、城市分会长

            if (!$isAdmin) {
                return ["status" => "error", "msg" => "无权限审核出处"];
            }

            // 4. 检查出处是否存在且为待审核状态
            $source = Db::_fetch("SELECT id, name FROM sources WHERE id = :id AND status = 2", [':id' => $source_id]);
            if (!$source) {
                return ["status" => "error", "msg" => "出处不存在或不在待审核状态"];
            }

            // 5. 执行审核通过
            $result = Db::_exec("UPDATE sources SET status = 1, updated_at = NOW() WHERE id = :id", [':id' => $source_id]);

            if ($result) {
                $this->user_log($uid, "审核通过出处【{$source['name']}】");
                return ["status" => "ok", "msg" => "审核通过"];
            } else {
                return ["status" => "error", "msg" => "审核失败"];
            }

        } catch (\Throwable $e) {
            $this->logWriteError("审核出处失败", [
                'exception_message' => $e->getMessage(),
                'source_id' => $source_id,
                'uid' => $uid,
                'operation' => 'approve_source'
            ]);
            return ["status" => "error", "msg" => "审核失败"];
        }
    }

    /**
     * @apiName 审核拒绝出处
     * @method reject_source
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param source_id int 出处ID
     * @return {"status":"ok","msg":"审核拒绝"} | {"status":"error","msg":"..."}
     */
    public function reject_source($uid, $token, $source_id)
    {
        // {{ AURA-X: Add - 实现出处审核拒绝功能. Confirmed via 寸止 }}
        // 1. 用户验证
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        // 2. 参数验证
        if (!check($source_id, "intgt0")) {
            return ["status" => "error", "msg" => "出处ID无效"];
        }

        try {
            dbConn();

            // 3. 权限检查：只有管理员可以审核
            $userInfo = Db::_fetch("SELECT role_type FROM user WHERE uid = :uid", [':uid' => $uid]);
            $isAdmin = in_array($userInfo['role_type'], ['0', '1', '4']); // 管理员、分会长、城市分会长

            if (!$isAdmin) {
                return ["status" => "error", "msg" => "无权限审核出处"];
            }

            // 4. 检查出处是否存在且为待审核状态
            $source = Db::_fetch("SELECT id, name FROM sources WHERE id = :id AND status = 2", [':id' => $source_id]);
            if (!$source) {
                return ["status" => "error", "msg" => "出处不存在或不在待审核状态"];
            }

            // 5. 执行审核拒绝（软删除）
            $result = Db::_exec("UPDATE sources SET status = 0, updated_at = NOW() WHERE id = :id", [':id' => $source_id]);

            if ($result) {
                $this->user_log($uid, "审核拒绝出处【{$source['name']}】");
                return ["status" => "ok", "msg" => "审核拒绝"];
            } else {
                return ["status" => "error", "msg" => "审核失败"];
            }

        } catch (\Throwable $e) {
            $this->logWriteError("审核拒绝出处失败", [
                'exception_message' => $e->getMessage(),
                'source_id' => $source_id,
                'uid' => $uid,
                'operation' => 'reject_source'
            ]);
            return ["status" => "error", "msg" => "审核失败"];
        }
    }

    /**
     * @apiName 获取待审核作者列表
     * @method get_pending_authors
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param page int 页码 (可选，默认1)
     * @param limit int 每页数量 (可选，默认20)
     * @return {"status":"ok","data":{"authors": [...], "total": N, "page": N, "limit": N}} | {"status":"error","msg":"..."}
     */
    public function get_pending_authors($uid, $token, $page = 1, $limit = 20)
    {
        // {{ AURA-X: Add - 实现获取待审核作者列表功能. Confirmed via 寸止 }}
        // 1. 用户验证
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        try {
            dbConn();

            // 2. 权限检查：只有管理员可以查看待审核列表
            $userInfo = Db::_fetch("SELECT role_type FROM user WHERE uid = :uid", [':uid' => $uid]);
            $isAdmin = in_array($userInfo['role_type'], ['0', '1', '4']); // 管理员、分会长、城市分会长

            if (!$isAdmin) {
                return ["status" => "error", "msg" => "无权限查看待审核列表"];
            }

            // 3. 参数验证和处理
            $page = max(1, (int)$page);
            $limit = max(1, min(100, (int)$limit));
            $offset = ($page - 1) * $limit;

            // 4. 获取总数
            $totalSql = "SELECT COUNT(*) as total FROM authors WHERE status = 2";
            $totalResult = Db::_fetch($totalSql);
            $total = (int)$totalResult['total'];

            // 5. 获取待审核作者列表
            $sql = "
                SELECT a.id, a.name, a.avatar, a.description, a.birth_year,
                       a.nationality, a.category, a.created_at, a.updated_at,
                       u.nickname as creator_name
                FROM authors a
                LEFT JOIN user u ON a.created_by = u.uid
                WHERE a.status = 2
                ORDER BY a.created_at DESC
                LIMIT :limit OFFSET :offset
            ";

            $authors = Db::_fetchAll($sql, [
                ':limit' => $limit,
                ':offset' => $offset
            ]);

            return ["status" => "ok", "data" => [
                'authors' => $authors ?: [],
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]];

        } catch (\Throwable $e) {
            $this->logWriteError("获取待审核作者列表失败", [
                'exception_message' => $e->getMessage(),
                'uid' => $uid,
                'operation' => 'get_pending_authors'
            ]);
            return ["status" => "error", "msg" => "获取列表失败"];
        }
    }

    /**
     * @apiName 获取待审核出处列表
     * @method get_pending_sources
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param page int 页码 (可选，默认1)
     * @param limit int 每页数量 (可选，默认20)
     * @return {"status":"ok","data":{"sources": [...], "total": N, "page": N, "limit": N}} | {"status":"error","msg":"..."}
     */
    public function get_pending_sources($uid, $token, $page = 1, $limit = 20)
    {
        // {{ AURA-X: Add - 实现获取待审核出处列表功能. Confirmed via 寸止 }}
        // 1. 用户验证
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        try {
            dbConn();

            // 2. 权限检查：只有管理员可以查看待审核列表
            $userInfo = Db::_fetch("SELECT role_type FROM user WHERE uid = :uid", [':uid' => $uid]);
            $isAdmin = in_array($userInfo['role_type'], ['0', '1', '4']); // 管理员、分会长、城市分会长

            if (!$isAdmin) {
                return ["status" => "error", "msg" => "无权限查看待审核列表"];
            }

            // 3. 参数验证和处理
            $page = max(1, (int)$page);
            $limit = max(1, min(100, (int)$limit));
            $offset = ($page - 1) * $limit;

            // 4. 获取总数
            $totalSql = "SELECT COUNT(*) as total FROM sources WHERE status = 2";
            $totalResult = Db::_fetch($totalSql);
            $total = (int)$totalResult['total'];

            // 5. 获取待审核出处列表
            $sql = "
                SELECT s.id, s.name, s.cover_image, s.description, s.publish_year,
                       s.publisher, s.category, s.created_at, s.updated_at,
                       u.nickname as creator_name
                FROM sources s
                LEFT JOIN user u ON s.created_by = u.uid
                WHERE s.status = 2
                ORDER BY s.created_at DESC
                LIMIT :limit OFFSET :offset
            ";

            $sources = Db::_fetchAll($sql, [
                ':limit' => $limit,
                ':offset' => $offset
            ]);

            return ["status" => "ok", "data" => [
                'sources' => $sources ?: [],
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]];

        } catch (\Throwable $e) {
            $this->logWriteError("获取待审核出处列表失败", [
                'exception_message' => $e->getMessage(),
                'uid' => $uid,
                'operation' => 'get_pending_sources'
            ]);
            return ["status" => "error", "msg" => "获取列表失败"];
        }
    }

    /**
     * @apiName 检查作者重复
     * @method check_author_duplicates
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param name string 作者名称
     * @param threshold float 相似度阈值 (可选，默认80.0)
     * @return {"status":"ok","data":{"duplicates": [...], "exact_match": bool}} | {"status":"error","msg":"..."}
     */
    public function check_author_duplicates($uid, $token, $name, $threshold = 80.0)
    {
        // {{ AURA-X: Add - 实现作者重复检查功能. Confirmed via 寸止 }}
        // 1. 用户验证
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        // 2. 参数验证
        $name = trim($name);
        if (empty($name)) {
            return ["status" => "error", "msg" => "作者姓名不能为空"];
        }

        try {
            dbConn();

            // 3. 检查精确匹配
            $exactMatch = false;
            $check_sql = "SELECT id, name FROM authors WHERE name = :name AND status = 1";
            $existing = Db::_fetch($check_sql, [':name' => $name]);
            if ($existing) {
                $exactMatch = true;
            }

            // 4. 查找相似作者
            $threshold = max(0.0, min(100.0, (float)$threshold));
            $similarAuthors = $this->findSimilarAuthors($name, $threshold, 10);

            return ["status" => "ok", "data" => [
                'duplicates' => $similarAuthors,
                'exact_match' => $exactMatch,
                'threshold' => $threshold
            ]];

        } catch (\Throwable $e) {
            $this->logWriteError("检查作者重复失败", [
                'exception_message' => $e->getMessage(),
                'author_name' => $name,
                'uid' => $uid,
                'operation' => 'check_author_duplicates'
            ]);
            return ["status" => "error", "msg" => "检查失败"];
        }
    }

    /**
     * @apiName 检查出处重复
     * @method check_source_duplicates
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param name string 出处名称
     * @param threshold float 相似度阈值 (可选，默认80.0)
     * @return {"status":"ok","data":{"duplicates": [...], "exact_match": bool}} | {"status":"error","msg":"..."}
     */
    public function check_source_duplicates($uid, $token, $name, $threshold = 80.0)
    {
        // {{ AURA-X: Add - 实现出处重复检查功能. Confirmed via 寸止 }}
        // 1. 用户验证
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        // 2. 参数验证
        $name = trim($name);
        if (empty($name)) {
            return ["status" => "error", "msg" => "出处名称不能为空"];
        }

        try {
            dbConn();

            // 3. 检查精确匹配
            $exactMatch = false;
            $check_sql = "SELECT id, name FROM sources WHERE name = :name AND status = 1";
            $existing = Db::_fetch($check_sql, [':name' => $name]);
            if ($existing) {
                $exactMatch = true;
            }

            // 4. 查找相似出处
            $threshold = max(0.0, min(100.0, (float)$threshold));
            $similarSources = $this->findSimilarSources($name, $threshold, 10);

            return ["status" => "ok", "data" => [
                'duplicates' => $similarSources,
                'exact_match' => $exactMatch,
                'threshold' => $threshold
            ]];

        } catch (\Throwable $e) {
            $this->logWriteError("检查出处重复失败", [
                'exception_message' => $e->getMessage(),
                'source_name' => $name,
                'uid' => $uid,
                'operation' => 'check_source_duplicates'
            ]);
            return ["status" => "error", "msg" => "检查失败"];
        }
    }

    /**
     * @apiName 编辑作者
     * @method update_author
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param author_id int 作者ID
     * @param name string 作者姓名 (可选)
     * @param avatar string 头像URL (可选)
     * @param description string 作者描述 (可选)
     * @param birth_year string 出生年份 (可选)
     * @param nationality string 国籍 (可选)
     * @param category string 作者分类 (可选)
     * @return {"status":"ok","msg":"更新成功"} | {"status":"error","msg":"..."} | {"status":"relogin"}
     */
    public function update_author($uid, $token, $author_id, $name = '', $avatar = '', $description = '', $birth_year = '', $nationality = '', $category = '')
    {
        // {{ AURA-X: Add - 实现编辑作者功能. Confirmed via 寸止 }}
        // 1. 用户验证
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        // 2. 参数验证
        if (!check($author_id, "intgt0")) {
            return ["status" => "error", "msg" => "作者ID无效"];
        }

        try {
            dbConn();

            // 3. 检查作者是否存在
            $author = Db::_fetch("SELECT id, name, created_by FROM authors WHERE id = :id AND status IN (1, 2)", [':id' => $author_id]);
            if (!$author) {
                return ["status" => "error", "msg" => "作者不存在"];
            }

            // 4. 权限检查：创建者或管理员可编辑
            $userInfo = Db::_fetch("SELECT role_type FROM user WHERE uid = :uid", [':uid' => $uid]);
            $isAdmin = in_array($userInfo['role_type'], ['0', '1', '4']); // 管理员、分会长、城市分会长
            $isCreator = $author['created_by'] == $uid;

            if (!$isAdmin && !$isCreator) {
                return ["status" => "error", "msg" => "无权限编辑此作者"];
            }

            // 5. 准备更新数据
            $updateData = [];
            $updateFields = [];

            if (!empty($name)) {
                $name = trim($name);
                if (mb_strlen($name) > 100) {
                    return ["status" => "error", "msg" => "作者姓名过长"];
                }
                // 检查名称是否与其他作者重复
                $check_sql = "SELECT id FROM authors WHERE name = :name AND status = 1 AND id != :author_id";
                $existing = Db::_fetch($check_sql, [':name' => $name, ':author_id' => $author_id]);
                if ($existing) {
                    return ["status" => "error", "msg" => "该作者姓名已存在"];
                }
                $updateData[':name'] = htmlspecialchars($name);
                $updateFields[] = "name = :name";
            }

            if ($avatar !== '') {
                if ($avatar && !filter_var($avatar, FILTER_VALIDATE_URL)) {
                    return ["status" => "error", "msg" => "头像URL格式不正确"];
                }
                $updateData[':avatar'] = $avatar ?: null;
                $updateFields[] = "avatar = :avatar";
            }

            if ($description !== '') {
                $updateData[':description'] = $description ? htmlspecialchars($description) : null;
                $updateFields[] = "description = :description";
            }

            if ($birth_year !== '') {
                $updateData[':birth_year'] = $birth_year ?: null;
                $updateFields[] = "birth_year = :birth_year";
            }

            if ($nationality !== '') {
                $updateData[':nationality'] = $nationality ? htmlspecialchars($nationality) : null;
                $updateFields[] = "nationality = :nationality";
            }

            if ($category !== '') {
                $updateData[':category'] = $category ? htmlspecialchars($category) : null;
                $updateFields[] = "category = :category";
            }

            // 6. 检查是否有更新内容
            if (empty($updateFields)) {
                return ["status" => "error", "msg" => "没有需要更新的内容"];
            }

            // 7. 执行更新
            $updateFields[] = "updated_at = NOW()";
            $updateData[':author_id'] = $author_id;

            $sql = "UPDATE authors SET " . implode(", ", $updateFields) . " WHERE id = :author_id";
            $result = Db::_exec($sql, $updateData);

            if ($result) {
                $this->user_log($uid, "编辑作者【{$author['name']}】");

                // 清除作者搜索缓存
                $this->clearSearchCache('authors');

                return ["status" => "ok", "msg" => "更新成功"];
            } else {
                return ["status" => "error", "msg" => "更新失败"];
            }

        } catch (\Throwable $e) {
            $this->logWriteError("编辑作者失败", [
                'exception_message' => $e->getMessage(),
                'author_id' => $author_id,
                'uid' => $uid,
                'operation' => 'update_author'
            ]);
            return ["status" => "error", "msg" => "更新失败"];
        }
    }

    /**
     * @apiName 编辑出处
     * @method update_source
     * @POST
     * @param uid int 用户ID
     * @param token string token
     * @param source_id int 出处ID
     * @param name string 出处名称 (可选)
     * @param cover_image string 封面图片URL (可选)
     * @param description string 出处描述 (可选)
     * @param publish_year string 发布年份 (可选)
     * @param publisher string 出版社/发布方 (可选)
     * @param category string 出处分类 (可选)
     * @return {"status":"ok","msg":"更新成功"} | {"status":"error","msg":"..."} | {"status":"relogin"}
     */
    public function update_source($uid, $token, $source_id, $name = '', $cover_image = '', $description = '', $publish_year = '', $publisher = '', $category = '')
    {
        // {{ AURA-X: Add - 实现编辑出处功能. Confirmed via 寸止 }}
        // 1. 用户验证
        if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
            return ["status" => "error", "msg" => "用户参数错误"];
        }
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        // 2. 参数验证
        if (!check($source_id, "intgt0")) {
            return ["status" => "error", "msg" => "出处ID无效"];
        }

        try {
            dbConn();

            // 3. 检查出处是否存在
            $source = Db::_fetch("SELECT id, name, created_by FROM sources WHERE id = :id AND status IN (1, 2)", [':id' => $source_id]);
            if (!$source) {
                return ["status" => "error", "msg" => "出处不存在"];
            }

            // 4. 权限检查：创建者或管理员可编辑
            $userInfo = Db::_fetch("SELECT role_type FROM user WHERE uid = :uid", [':uid' => $uid]);
            $isAdmin = in_array($userInfo['role_type'], ['0', '1', '4']); // 管理员、分会长、城市分会长
            $isCreator = $source['created_by'] == $uid;

            if (!$isAdmin && !$isCreator) {
                return ["status" => "error", "msg" => "无权限编辑此出处"];
            }

            // 5. 准备更新数据
            $updateData = [];
            $updateFields = [];

            if (!empty($name)) {
                $name = trim($name);
                if (mb_strlen($name) > 255) {
                    return ["status" => "error", "msg" => "出处名称过长"];
                }
                // 检查名称是否与其他出处重复
                $check_sql = "SELECT id FROM sources WHERE name = :name AND status = 1 AND id != :source_id";
                $existing = Db::_fetch($check_sql, [':name' => $name, ':source_id' => $source_id]);
                if ($existing) {
                    return ["status" => "error", "msg" => "该出处名称已存在"];
                }
                $updateData[':name'] = htmlspecialchars($name);
                $updateFields[] = "name = :name";
            }

            if ($cover_image !== '') {
                if ($cover_image && !filter_var($cover_image, FILTER_VALIDATE_URL)) {
                    return ["status" => "error", "msg" => "封面图片URL格式不正确"];
                }
                $updateData[':cover_image'] = $cover_image ?: null;
                $updateFields[] = "cover_image = :cover_image";
            }

            if ($description !== '') {
                $updateData[':description'] = $description ? htmlspecialchars($description) : null;
                $updateFields[] = "description = :description";
            }

            if ($publish_year !== '') {
                $updateData[':publish_year'] = $publish_year ?: null;
                $updateFields[] = "publish_year = :publish_year";
            }

            if ($publisher !== '') {
                $updateData[':publisher'] = $publisher ? htmlspecialchars($publisher) : null;
                $updateFields[] = "publisher = :publisher";
            }

            if ($category !== '') {
                $updateData[':category'] = $category ? htmlspecialchars($category) : null;
                $updateFields[] = "category = :category";
            }

            // 6. 检查是否有更新内容
            if (empty($updateFields)) {
                return ["status" => "error", "msg" => "没有需要更新的内容"];
            }

            // 7. 执行更新
            $updateFields[] = "updated_at = NOW()";
            $updateData[':source_id'] = $source_id;

            $sql = "UPDATE sources SET " . implode(", ", $updateFields) . " WHERE id = :source_id";
            $result = Db::_exec($sql, $updateData);

            if ($result) {
                $this->user_log($uid, "编辑出处【{$source['name']}】");

                // 清除出处搜索缓存
                $this->clearSearchCache('sources');

                return ["status" => "ok", "msg" => "更新成功"];
            } else {
                return ["status" => "error", "msg" => "更新失败"];
            }

        } catch (\Throwable $e) {
            $this->logWriteError("编辑出处失败", [
                'exception_message' => $e->getMessage(),
                'source_id' => $source_id,
                'uid' => $uid,
                'operation' => 'update_source'
            ]);
            return ["status" => "error", "msg" => "更新失败"];
        }
    }

    /**
     * 为搜索结果添加关键词高亮
     * @param string $text 原始文本
     * @param string $keyword 搜索关键词
     * @return string 高亮后的文本
     */
    private function highlightKeyword($text, $keyword)
    {
        // {{ AURA-X: Add - 实现关键词高亮功能. Confirmed via 寸止 }}
        if (empty($keyword) || empty($text)) {
            return $text;
        }

        // 使用不区分大小写的替换，添加高亮标记
        $highlighted = preg_replace('/(' . preg_quote($keyword, '/') . ')/i', '<mark>$1</mark>', $text);
        return $highlighted ?: $text;
    }

    /**
     * 生成搜索缓存键
     * @param string $type 类型 (authors/sources)
     * @param string $keyword 搜索关键词
     * @param int $page 页码
     * @param int $page_size 每页数量
     * @return string 缓存键
     */
    private function getCacheKey($type, $keyword, $page, $page_size)
    {
        // {{ AURA-X: Add - 实现搜索缓存键生成. Confirmed via 寸止 }}
        $key = "search_{$type}_{$keyword}_{$page}_{$page_size}";
        return md5($key);
    }

    /**
     * 编辑动态
     * {{ AURA-X: Add - 实现动态编辑功能. Confirmed via 寸止. }}
     * @param int $uid 用户ID
     * @param string $token 用户token
     * @param int $feed_id 动态ID
     * @param string $content 动态内容
     * @param array $images 图片数组
     * @param string $location 位置信息
     * @param string $tags 标签
     * @param string $privacy 隐私设置
     * @return array {"status":"ok","msg":"更新成功"} | {"status":"error","msg":"..."}
     */
    public function edit_feed($uid, $token, $feed_id, $content, $images = [], $location = "", $tags = "", $privacy = 'public')
    {
        try {
            // 1. 参数验证
            if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
                return ["status" => "error", "msg" => "用户参数错误"];
            }
            if (!$this->auth($uid, $token)) {
                return ["status" => "relogin", "msg" => "登录信息验证失败"];
            }
            if (!check($feed_id, "intgt0")) {
                return ["status" => "error", "msg" => "无效的动态ID"];
            }

            $userId = (int)$uid;
            $feedId = (int)$feed_id;

            // 2. 内容验证
            $content = trim($content);
            if (empty($content)) {
                return ["status" => "error", "msg" => "内容不能为空"];
            }
            if (mb_strlen($content) > 5000) {
                return ["status" => "error", "msg" => "内容过长"];
            }

            // 3. 图片参数处理
            $imagesJson = '';
            if (!empty($images)) {
                if (is_string($images)) {
                    $imagesArray = json_decode($images, true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        return ["status" => "error", "msg" => "图片参数格式错误"];
                    }
                    $images = $imagesArray;
                }
                if (is_array($images) && count($images) > 0) {
                    $imagesJson = json_encode($images, JSON_UNESCAPED_UNICODE);
                }
            }

            // 4. 其他参数处理
            $location = trim($location);
            $tags = trim($tags);
            $privacy = in_array($privacy, ['public', 'private']) ? $privacy : 'public';

            dbConn();

            // 5. 检查动态是否存在且属于当前用户
            $sql = "SELECT id, user_id FROM feeds WHERE id = :feedId";
            $feed = Db::_fetch($sql, [':feedId' => $feedId]);

            if (!$feed) {
                return ["status" => "error", "msg" => "动态不存在"];
            }

            if ($feed['user_id'] != $userId) {
                return ["status" => "error", "msg" => "您无权编辑此动态"];
            }

            // 6. 更新动态数据
            $updateData = [
                ':feedId' => $feedId,
                ':content' => htmlspecialchars($content),
                ':images_json' => $imagesJson,
                ':location' => $location,
                ':tags' => $tags,
                ':privacy' => $privacy,
                ':updated_at' => date('Y-m-d H:i:s')
            ];

            $sql = "UPDATE feeds SET
                    content = :content,
                    images_json = :images_json,
                    location = :location,
                    tags = :tags,
                    privacy = :privacy,
                    updated_at = :updated_at
                    WHERE id = :feedId";

            $result = Db::_exec($sql, $updateData);

            if ($result) {
                $this->user_log($uid, "编辑动态【{$feedId}】");
                return ["status" => "ok", "msg" => "更新成功"];
            } else {
                return ["status" => "error", "msg" => "更新失败"];
            }

        } catch (\Throwable $e) {
            $this->logWriteError("编辑动态失败", [
                'exception_message' => $e->getMessage(),
                'feed_id' => $feed_id,
                'uid' => $uid,
                'operation' => 'edit_feed'
            ]);
            return ["status" => "error", "msg" => "更新失败"];
        }
    }

    /**
     * 编辑日记
     * {{ AURA-X: Add - 实现日记编辑功能. Confirmed via 寸止. }}
     * @param int $uid 用户ID
     * @param string $token 用户token
     * @param int $diary_id 日记ID
     * @param string $content 日记内容
     * @param array $images 图片数组
     * @param string $location 位置信息
     * @param string $tags 标签
     * @param string $privacy 隐私设置
     * @return array {"status":"ok","msg":"更新成功"} | {"status":"error","msg":"..."}
     */
    public function edit_diary($uid, $token, $diary_id, $content, $images = [], $location = "", $tags = "", $privacy = 'public')
    {
        try {
            // 1. 参数验证
            if (!check($uid, "intgt0") || empty($token) || strlen($token) != 32) {
                return ["status" => "error", "msg" => "用户参数错误"];
            }
            if (!$this->auth($uid, $token)) {
                return ["status" => "relogin", "msg" => "登录信息验证失败"];
            }
            if (!check($diary_id, "intgt0")) {
                return ["status" => "error", "msg" => "无效的日记ID"];
            }

            $userId = (int)$uid;
            $diaryId = (int)$diary_id;

            // 2. 内容验证
            $content = trim($content);
            if (empty($content)) {
                return ["status" => "error", "msg" => "内容不能为空"];
            }
            if (mb_strlen($content) > 5000) {
                return ["status" => "error", "msg" => "内容过长"];
            }

            // 3. 图片参数处理
            $imagesJson = '';
            if (!empty($images)) {
                if (is_string($images)) {
                    $imagesArray = json_decode($images, true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        return ["status" => "error", "msg" => "图片参数格式错误"];
                    }
                    $images = $imagesArray;
                }
                if (is_array($images) && count($images) > 0) {
                    $imagesJson = json_encode($images, JSON_UNESCAPED_UNICODE);
                }
            }

            // 4. 其他参数处理
            $location = trim($location);
            $tags = trim($tags);
            $privacy = in_array($privacy, ['public', 'private']) ? $privacy : 'public';

            dbConn();

            // 5. 检查日记是否存在且属于当前用户（日记在feeds表中，type='diary'）
            $sql = "SELECT id, user_id FROM feeds WHERE id = :diaryId AND type = 'diary'";
            $diary = Db::_fetch($sql, [':diaryId' => $diaryId]);

            if (!$diary) {
                return ["status" => "error", "msg" => "日记不存在"];
            }

            if ($diary['user_id'] != $userId) {
                return ["status" => "error", "msg" => "您无权编辑此日记"];
            }

            // 6. 更新日记数据
            $updateData = [
                ':diaryId' => $diaryId,
                ':content' => htmlspecialchars($content),
                ':images_json' => $imagesJson,
                ':location' => $location,
                ':tags' => $tags,
                ':privacy' => $privacy,
                ':updated_at' => date('Y-m-d H:i:s')
            ];

            $sql = "UPDATE feeds SET
                    content = :content,
                    images_json = :images_json,
                    location = :location,
                    tags = :tags,
                    privacy = :privacy,
                    updated_at = :updated_at
                    WHERE id = :diaryId";

            $result = Db::_exec($sql, $updateData);

            if ($result) {
                $this->user_log($uid, "编辑日记【{$diaryId}】");
                return ["status" => "ok", "msg" => "更新成功"];
            } else {
                return ["status" => "error", "msg" => "更新失败"];
            }

        } catch (\Throwable $e) {
            $this->logWriteError("编辑日记失败", [
                'exception_message' => $e->getMessage(),
                'diary_id' => $diary_id,
                'uid' => $uid,
                'operation' => 'edit_diary'
            ]);
            return ["status" => "error", "msg" => "更新失败"];
        }
    }

    /**
     * 获取搜索缓存
     * @param string $cacheKey 缓存键
     * @param int $expireTime 过期时间（秒，默认300秒=5分钟）
     * @return array|null 缓存数据或null
     */
    private function getCache($cacheKey, $expireTime = 300)
    {
        // {{ AURA-X: Add - 实现搜索缓存获取. Confirmed via 寸止 }}
        try {
            $cacheFile = sys_get_temp_dir() . '/search_cache_' . $cacheKey . '.json';

            if (!file_exists($cacheFile)) {
                return null;
            }

            $fileTime = filemtime($cacheFile);
            if (time() - $fileTime > $expireTime) {
                // 缓存过期，删除文件
                @unlink($cacheFile);
                return null;
            }

            $content = file_get_contents($cacheFile);
            $data = json_decode($content, true);

            return $data ?: null;

        } catch (\Throwable $e) {
            // 缓存读取失败，返回null
            return null;
        }
    }

    /**
     * 设置搜索缓存
     * @param string $cacheKey 缓存键
     * @param array $data 要缓存的数据
     * @return bool 是否成功
     */
    private function setCache($cacheKey, $data)
    {
        // {{ AURA-X: Add - 实现搜索缓存设置. Confirmed via 寸止 }}
        try {
            $cacheFile = sys_get_temp_dir() . '/search_cache_' . $cacheKey . '.json';
            $content = json_encode($data, JSON_UNESCAPED_UNICODE);

            return file_put_contents($cacheFile, $content) !== false;

        } catch (\Throwable $e) {
            return false;
        }
    }

    /**
     * 清除搜索缓存
     * @param string $type 类型 (authors/sources/all)
     * @return bool 是否成功
     */
    private function clearSearchCache($type = 'all')
    {
        // {{ AURA-X: Modify - 修复缓存清理逻辑，由于使用MD5哈希，清除所有搜索缓存. Confirmed via 寸止 }}
        try {
            $tempDir = sys_get_temp_dir();
            // 由于缓存键使用MD5哈希，无法按类型区分，清除所有搜索缓存
            $pattern = 'search_cache_*.json';

            $files = glob($tempDir . '/' . $pattern);
            if ($files) {
                foreach ($files as $file) {
                    @unlink($file);
                }
            }

            return true;

        } catch (\Throwable $e) {
            return false;
        }
    }

    /**
     * 计算两个字符串的相似度（使用Levenshtein距离）
     * @param string $str1 字符串1
     * @param string $str2 字符串2
     * @return float 相似度百分比 (0-100)
     */
    private function calculateSimilarity($str1, $str2)
    {
        // {{ AURA-X: Add - 实现字符串相似度计算. Confirmed via 寸止 }}
        $str1 = $this->normalizeString($str1);
        $str2 = $this->normalizeString($str2);

        if ($str1 === $str2) {
            return 100.0;
        }

        $len1 = mb_strlen($str1);
        $len2 = mb_strlen($str2);

        if ($len1 === 0 || $len2 === 0) {
            return 0.0;
        }

        $distance = levenshtein($str1, $str2);
        $maxLen = max($len1, $len2);

        return (1 - $distance / $maxLen) * 100;
    }

    /**
     * 标准化字符串（去除空格、标点、转换小写）
     * @param string $str 原始字符串
     * @return string 标准化后的字符串
     */
    private function normalizeString($str)
    {
        // {{ AURA-X: Add - 实现字符串标准化. Confirmed via 寸止 }}
        // 转换为小写
        $str = mb_strtolower($str);

        // 去除空格和常见标点符号
        $str = preg_replace('/[\s\-_\.\,\!\?\(\)\[\]\{\}\"\']+/', '', $str);

        return trim($str);
    }

    /**
     * 查找相似的作者
     * @param string $name 作者名称
     * @param float $threshold 相似度阈值 (默认80%)
     * @param int $limit 返回数量限制 (默认5)
     * @return array 相似作者列表
     */
    private function findSimilarAuthors($name, $threshold = 80.0, $limit = 5)
    {
        // {{ AURA-X: Add - 实现相似作者查找. Confirmed via 寸止 }}
        try {
            // 获取所有正常状态的作者
            $sql = "SELECT id, name, avatar, description FROM authors WHERE status = 1 ORDER BY name";
            $authors = Db::_fetchAll($sql);

            $similarAuthors = [];

            foreach ($authors as $author) {
                $similarity = $this->calculateSimilarity($name, $author['name']);

                if ($similarity >= $threshold) {
                    $author['similarity'] = round($similarity, 2);
                    $similarAuthors[] = $author;
                }
            }

            // 按相似度降序排序
            usort($similarAuthors, function($a, $b) {
                return $b['similarity'] <=> $a['similarity'];
            });

            return array_slice($similarAuthors, 0, $limit);

        } catch (\Throwable $e) {
            $this->logWriteError("查找相似作者失败", [
                'exception_message' => $e->getMessage(),
                'author_name' => $name,
                'operation' => 'findSimilarAuthors'
            ]);
            return [];
        }
    }

    /**
     * 查找相似的出处
     * @param string $name 出处名称
     * @param float $threshold 相似度阈值 (默认80%)
     * @param int $limit 返回数量限制 (默认5)
     * @return array 相似出处列表
     */
    private function findSimilarSources($name, $threshold = 80.0, $limit = 5)
    {
        // {{ AURA-X: Add - 实现相似出处查找. Confirmed via 寸止 }}
        try {
            // 获取所有正常状态的出处
            $sql = "SELECT id, name, cover_image, description FROM sources WHERE status = 1 ORDER BY name";
            $sources = Db::_fetchAll($sql);

            $similarSources = [];

            foreach ($sources as $source) {
                $similarity = $this->calculateSimilarity($name, $source['name']);

                if ($similarity >= $threshold) {
                    $source['similarity'] = round($similarity, 2);
                    $similarSources[] = $source;
                }
            }

            // 按相似度降序排序
            usort($similarSources, function($a, $b) {
                return $b['similarity'] <=> $a['similarity'];
            });

            return array_slice($similarSources, 0, $limit);

        } catch (\Throwable $e) {
            $this->logWriteError("查找相似出处失败", [
                'exception_message' => $e->getMessage(),
                'source_name' => $name,
                'operation' => 'findSimilarSources'
            ]);
            return [];
        }
    }
}

/*
if (!function_exists('response_error')) {
    function response_error($msg = '操作失败', $data = [], $code = 1)
    {
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode(['code' => $code, 'msg' => $msg, 'data' => $data], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        exit;
    }
}
*/

?>
