<?php
namespace controller;
use core\Controller;
use core\Db;

//守护进程
class Daemon extends Controller{
	
	public $pid_file = BASE_PATH . "daemon.pid";
	public $time_file = BASE_PATH . "daemon.time";
	public $err_file = BASE_PATH . "daemon.err";
	public $sleep_second = 30;//休息秒数
	private $_config = [];//配置参数
	public function __construct(){
		parent::__construct();
		error_reporting(E_ALL);
	}
	
	//nohup php cli controller=daemon action=start_daemon >/dev/null 2>&1 &
	public function start_daemon_huodongbao(){
		if(!IS_CLI){
			$this->output("只支持命令行访问");
			exit;
		}
		//检查配置
		dbConn();
		//写入当前进程pid
		if(strtoupper(substr(PHP_OS,0,3)) !== 'WIN'){
			$pid = posix_getpid();//yum install php-process
			file_put_contents($this->pid_file,$pid);
		}
		//
		while(true){
			//
			$this->go();
			//
			$now_time = date("Y-m-d H:i:s")."\n";
			clearstatcache();
			if(file_exists($this->time_file) && filesize($this->time_file) > 10 * 1024)@unlink($this->time_file);
			file_put_contents($this->time_file,$now_time,FILE_APPEND|LOCK_EX);
			if(file_exists($this->err_file) && filesize($this->err_file) > 100 * 1024)rename($this->err_file,$this->err_file . "." . date("Y_m_d_H_i_s"));
			//
			$hour = date("G");
			if($hour >=0 && $hour <= 23){
				sleep($this->sleep_second);//休息
			}else{
				sleep($this->sleep_second * 5);//休息
			}
		}
	}

	//结束服务进程
	public function stop(){
		if(!IS_CLI){
			$this->output("只支持命令行访问");
			exit;
		}
		if(strtoupper(substr(PHP_OS,0,3)) === 'WIN'){
			$this->output("仅支持Linux系统");exit;
		}
		//
		if(!file_exists($this->pid_file)){
			$this->output("文件不存在");exit;
		}
		$pid = file_get_contents($this->pid_file);
		if(!check($pid,"intgt0")){
			$this->output("进程编号不正确");exit;
		}
		$res = posix_kill($pid,SIGKILL);//yum install php-process
		if($res){
			@unlink($this->pid_file);
			@unlink($this->time_file);
			$this->output("ok");exit;
		}else{
			$this->output("失败");exit;
		}
	}
	
	//主逻辑
	private function go(){
		$datetime = date("Y-m-d H:i:s");
		$this->output("开始执行定时任务循环：{$datetime}");

		// 数据库连接检查
		if(!Db::ping()){
			dbConn("",true);
			if(!Db::ping()){
				$this->err("mysql连接失败");
				return false;
			}
		}

		//****** 任务1 取消/商城/活动报名/充值/购买会员/未支付的订单 ******//
		$this->output("开始执行任务1：订单自动取消");
		try {
			// 获取配置参数
			if(isset($this->_config['goods_nopay_cancel_minute'])){
				$goods_nopay_cancel_minute = $this->_config['goods_nopay_cancel_minute'];
			}else{
				$goods_nopay_cancel_minute = $this->get_config("goods_nopay_cancel_minute");
				if(empty($goods_nopay_cancel_minute) || !check($goods_nopay_cancel_minute,"intgt0")){
					throw new \Exception("获取未支付订单自动取消时间系统配置失败");
				}
				$this->_config['goods_nopay_cancel_minute'] = $goods_nopay_cancel_minute;
			}

			// 执行订单取消操作
			$sql = "UPDATE `goods_order` SET `status`=4,`cancel_time`=NOW() WHERE `status`=0 AND `auto_cancel_time`<=NOW()";
			$goods_count = Db::_exec($sql);

			$sql = "UPDATE `huodong_baoming_order` SET `status`=2 WHERE `status`=0 AND DATE_ADD(`time`,INTERVAL {$goods_nopay_cancel_minute} MINUTE)<=NOW()";
			$huodong_count = Db::_exec($sql);

			$sql = "UPDATE `user_chongzhi_order` SET `status`=2 WHERE `status`=0 AND DATE_ADD(`time`,INTERVAL {$goods_nopay_cancel_minute} MINUTE)<=NOW()";
			$chongzhi_count = Db::_exec($sql);

			$sql = "UPDATE `user_huiyuan_order` SET `status`=2 WHERE `status`=0 AND DATE_ADD(`time`,INTERVAL {$goods_nopay_cancel_minute} MINUTE)<=NOW()";
			$huiyuan_count = Db::_exec($sql);

			$total_count = $goods_count + $huodong_count + $chongzhi_count + $huiyuan_count;
			$this->output("任务1完成：取消了{$total_count}个未支付订单（商品:{$goods_count}, 活动:{$huodong_count}, 充值:{$chongzhi_count}, 会员:{$huiyuan_count}）");

		} catch (\Throwable $e) {
			$this->err("任务1执行失败：" . $e->getMessage());
			$this->exception_log("任务1（订单自动取消）执行失败：" . $e->getMessage());
		}

		//****** 任务2 商城自动确认收货 ******//
		$this->output("开始执行任务2：商城自动确认收货");
		try {
			$hours_ago = date("Y-m-d H:i:s",time() - 60 * 60 * 24);
			$sql = "SELECT `id`,`order_id`,`uid`,`money`,`yongjin`,`yongjin_uid`,`status` FROM `goods_order` WHERE `auto_queren_time`>'{$hours_ago}' AND `auto_queren_time`<NOW() AND `status`=2";
			$goods_order = Db::_fetchAll($sql);

			$processed_count = 0;
			$commission_count = 0;

			if(!empty($goods_order)){
				foreach($goods_order as $order_info){
					Db::begin();
					try{
						$sql = "UPDATE `goods_order` SET `status`=3,`queren_time`=NOW() WHERE `id`={$order_info['id']} AND `status`=2";
						$rowCount = Db::_exec($sql);

						if(!empty($rowCount) && bccomp($order_info['yongjin'],"0.00",2) > 0 && $order_info['yongjin_uid'] > 0){
							//佣金发放
							$sql = "UPDATE `user` SET `money`=`money`+{$order_info['yongjin']} WHERE uid={$order_info['yongjin_uid']}";
							$rowCount = Db::_exec($sql);
							if(empty($rowCount)){
								throw new \Exception("发放佣金失败,订单号:{$order_info['order_id']},佣金:{$order_info['yongjin']},用户编号：{$order_info['yongjin_uid']}");
							}

							$shengyu = Db()->table("user")->where("uid={$order_info['yongjin_uid']}")->getColumn("money",0);
							$zhangdan = [
								"uid"=>$order_info['yongjin_uid'],
								"money"=>$order_info['yongjin'],
								"type"=>4,
								"shengyu"=>$shengyu,
								"msg"=>"订单号:{$order_info['order_id']}",
							];
							Db()->table("user_zhangdan")->insert($zhangdan);

							$yongjin_log = [
								"uid"=>$order_info['yongjin_uid'],
								"type"=>3,
								"money"=>$order_info['yongjin'],
								"order_id"=>":order_id",
							];
							Db()->table("user_yongjin_log")->prepareParam([":order_id"=>$order_info['order_id']])->insert($yongjin_log);

							$sql = "UPDATE `user` SET `zong_yongjin`=`zong_yongjin`+{$order_info['yongjin']} WHERE uid={$order_info['yongjin_uid']}";
							Db::_exec($sql);

							$commission_count++;
						}

						Db::commit();
						$processed_count++;

					}catch(\Throwable $e){
						Db::rollback();
						$this->err("商城订单处理失败，订单ID:{$order_info['id']}, 错误:" . $e->getMessage());
						$this->exception_log("商城订单处理失败，订单ID:{$order_info['id']}, 错误:" . $e->getMessage());
						continue; // 继续处理下一个订单
					}
				}
			}

			$this->output("任务2完成：处理了{$processed_count}个商城订单，发放了{$commission_count}笔佣金");

		} catch (\Throwable $e) {
			$this->err("任务2执行失败：" . $e->getMessage());
			$this->exception_log("任务2（商城自动确认收货）执行失败：" . $e->getMessage());
		}



		//****** 任务3 会员订单结算状态更新 ******//
		$this->output("开始执行任务3：会员订单结算状态更新");
		try {
			// 注意：佣金记录已在会员购买时通过User.php的settle_member_commission方法插入
			// 此任务只负责更新订单的结算状态，不再重复插入佣金记录
			$hours_ago = date("Y-m-d H:i:s",time() - 60 * 60 * 24);
			$sql = "UPDATE `user_huiyuan_order` SET `jiesuan_status`=1 WHERE jiesuan_time>'{$hours_ago}' AND `jiesuan_time`<NOW() AND `jiesuan_status`=0 AND status=1";
			$updated_count = Db::_exec($sql);

			$this->output("任务3完成：更新了{$updated_count}个会员订单的结算状态");

		} catch (\Throwable $e) {
			$this->err("任务3执行失败：" . $e->getMessage());
			$this->exception_log("任务3（会员订单结算状态更新）执行失败：" . $e->getMessage());
		}

		//****** 任务4 已移除：运营佣金记录生成 ******//
		// 注意：运营佣金记录已在会员购买时通过User.php的insert_operation_commission_for_president方法插入
		// 不再需要单独的运营佣金生成任务，避免重复插入

		//****** 任务5 佣金状态自动更新 ******//
		$this->output("开始执行任务5：佣金状态自动更新");
		try {
			$this->settle_commission_status();
			$this->output("任务5完成：佣金状态自动更新执行完毕");

		} catch (\Throwable $e) {
			$this->err("任务5执行失败：" . $e->getMessage());
			$this->exception_log("任务5（佣金状态自动更新）执行失败：" . $e->getMessage());
		}

		//****** 任务6 活动收入状态自动更新 ******//
		$this->output("开始执行任务6：活动收入状态自动更新");
		try {
			$this->settle_activity_income_status();
			$this->output("任务6完成：活动收入状态自动更新执行完毕");

		} catch (\Throwable $e) {
			$this->err("任务6执行失败：" . $e->getMessage());
			$this->exception_log("任务6（活动收入状态自动更新）执行失败：" . $e->getMessage());
		}

		//****** 任务7 体验会员记录过期状态更新 ******//
		$this->output("开始执行任务7：体验会员记录过期状态更新");
		try {
			$this->update_trial_member_expired_status();
			$this->output("任务7完成：体验会员记录过期状态更新执行完毕");

		} catch (\Throwable $e) {
			$this->err("任务7执行失败：" . $e->getMessage());
			$this->exception_log("任务7（体验会员记录过期状态更新）执行失败：" . $e->getMessage());
		}

		//****** 任务8 活动缺席用户扣分处理 ******//
		// {{ AURA-X: Add - 添加缺席扣分定时任务. Confirmed via 寸止. }}
		$this->output("开始执行任务8：活动缺席用户扣分处理");
		try {
			$this->process_activity_absence_penalty();
			$this->output("任务8完成：活动缺席用户扣分处理执行完毕");

		} catch (\Throwable $e) {
			$this->err("任务8执行失败：" . $e->getMessage());
			$this->exception_log("任务8（活动缺席用户扣分处理）执行失败：" . $e->getMessage());
		}

		$this->output("定时任务循环执行完成：" . date("Y-m-d H:i:s"));
	}
	
	// 运营佣金记录现在在会员购买时通过User.php的insert_operation_commission_for_president方法插入
	// 所有佣金（邀请佣金和运营佣金）都在各自的结算任务中直接设为可提取状态

	/**
	 * 佣金状态自动更新（简化版）
	 * 纯粹的状态字段批量更新操作
	 */
	private function settle_commission_status() {
		try {
			$current_time = date('Y-m-d H:i:s');
			$total_settled = 0;

			// 1. 邀请佣金：7天后可提取（批量更新）
			$invite_count = Db::_exec("
				UPDATE user_yongjin_log
				SET status=1, settlement_time='{$current_time}', available_time='{$current_time}'
				WHERE commission_type='invite'
				AND status=0
				AND time <= DATE_SUB(NOW(), INTERVAL 7 DAY)
			");
			$total_settled += $invite_count;

			// 2. 运营佣金：1个月后可提取（批量更新）
			$operation_count = Db::_exec("
				UPDATE user_yongjin_log
				SET status=1, settlement_time='{$current_time}', available_time='{$current_time}'
				WHERE commission_type='operation'
				AND status=0
				AND time <= DATE_SUB(NOW(), INTERVAL 1 MONTH)
			");
			$total_settled += $operation_count;

			// 3. 输出汇总结果
			if ($total_settled > 0) {
				$this->output("佣金状态更新完成：邀请佣金{$invite_count}条，运营佣金{$operation_count}条，共{$total_settled}条");
			}

		} catch (\Throwable $e) {
			$this->err("佣金状态更新异常: " . $e->getMessage());
		}
	}

	/**
	 * 活动收入状态自动更新
	 * 活动结束后指定天数，发布方收入变为可提取状态
	 */
	private function settle_activity_income_status() {
		try {
			$current_time = date('Y-m-d H:i:s');

			// 获取活动收入结算天数配置（默认7天）
			$settlement_days = $this->get_config("activity_settlement_days", "7");

			// 活动收入结算逻辑：活动结束后指定天数，收入变为可提取
			$activity_income_count = Db::_exec("
				UPDATE activity_income_log
				SET status=1, settlement_time='{$current_time}', available_time='{$current_time}'
				WHERE status=0
				AND activity_id IN (
					SELECT id FROM huodong
					WHERE end_time <= DATE_SUB(NOW(), INTERVAL {$settlement_days} DAY)
				)
			");

			if ($activity_income_count > 0) {
				$this->output("活动收入状态更新完成：{$activity_income_count}条记录变为可提取状态");
			}

		} catch (\Throwable $e) {
			$this->err("活动收入状态更新异常: " . $e->getMessage());
		}
	}

	/**
	 * 体验会员记录过期状态更新
	 * 将已过期但状态仍为0（未领取）的记录更新为2（已过期）
	 */
	private function update_trial_member_expired_status() {
		try {
			// 更新已过期但状态仍为未领取的记录
			$sql = "UPDATE `trial_member_records` SET `status`=2 WHERE `status`=0 AND `expire_time`<NOW()";
			$updated_count = Db::_exec($sql);

			$this->output("体验会员记录过期状态更新完成：更新了{$updated_count}条记录");

		} catch (\Throwable $e) {
			$this->err("体验会员记录过期状态更新失败：" . $e->getMessage());
			throw $e;
		}
	}

	// 注意：运营佣金配置和计算方法已移除
	// 这些功能现在在User.php中处理

	private function output($msg){
		echo date("Y-m-d H:i:s") . " : " . $msg . "\n";
	}
	


	private function err($msg){
		$datetime = date("Y-m-d H:i:s");
		$err =  "{$datetime} | {$msg}\n";
		file_put_contents($this->err_file,$err,FILE_APPEND|LOCK_EX);
	}

	/**
	 * {{ AURA-X: Add - 添加活动缺席用户扣分处理方法. Confirmed via 寸止. }}
	 * 处理活动缺席用户扣分（每日凌晨执行）
	 * 检查前一天结束的启用签到活动，对未签到用户扣分
	 */
	private function process_activity_absence_penalty() {
		try {
			// 获取缺席扣分配置
			$absence_penalty = (int)Db()->table("config")
				->where("name='absence_penalty'")
				->getColumn("val") ?: 20;

			// 查找昨天结束且启用签到的活动
			$yesterday = date('Y-m-d', strtotime('-1 day'));
			$activities = Db()->table("huodong")
				->select("id,name,uid")
				->where("enable_checkin=1 AND DATE(end_time)=:yesterday")
				->prepareParam([":yesterday" => $yesterday])
				->fetchAll();

			$processed_count = 0;
			$total_penalties = 0;

			foreach ($activities as $activity) {
				// 获取该活动的所有已报名用户
				$registered_users = Db()->table("huodong_baoming_order")
					->select("uid")
					->where("huodong_id=:activity_id AND status=1")
					->prepareParam([":activity_id" => $activity['id']])
					->fetchAll();

				foreach ($registered_users as $user) {
					// 检查用户是否签到
					$checkin_record = Db()->table("huodong_checkin")
						->select("id")
						->where("huodong_id=:activity_id AND uid=:uid")
						->prepareParam([
							":activity_id" => $activity['id'],
							":uid" => $user['uid']
						])
						->fetch();

					if (empty($checkin_record)) {
						// 用户缺席，扣除积分
						$description = "缺席活动：{$activity['name']}";
						$absence_points = -$absence_penalty; // 扣分为负数

						// 调用User类的add_points方法
						\controller\User::add_points($user['uid'], $absence_points, 'activity_absence', $activity['id'], $description);

						$processed_count++;
						$total_penalties += $absence_penalty;
					}
				}
			}

			$this->output("缺席扣分处理完成：处理了{$processed_count}个缺席用户，总计扣除{$total_penalties}积分");
			return [
				"processed_count" => $processed_count,
				"total_penalties" => $total_penalties,
				"activities_count" => count($activities)
			];

		} catch (\Throwable $e) {
			$this->err("缺席扣分处理失败: " . $e->getMessage());
			throw $e;
		}
	}

	public function _empty(){
		echo __CLASS__." -> _empty";
	}
	function __destruct(){

	}
}
