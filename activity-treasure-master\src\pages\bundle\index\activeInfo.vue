<script setup>
import {ref, reactive, onMounted, nextTick, computed} from "vue";
import {
  huodongget_info,
  huodongadd_baoming,
  huodongcancel_baoming,
  huodongdelete_baoming,
  huodongget_baoming_list,
  huodongget_baoming_list_public,
  huodongshoucang_add,
  huodongshoucang_del,
  huodongzan_add,
  huodongzan_del,
  huodongadd_pingjia,
  huodongget_pingjia,
  huodongreply_pingjia,
  userfenxiang_event,
  payget_weixinpay_sign,
  payweixin_pay,


  userguanzhu_check,
  configget_deep_link,
  huodongcheckin,
  huodongcancel_huodong,
  huodongget_activity_photos
} from "@/api";
import myLine from "@/components/myLine.vue";
import myStitle from "@/components/myStitle.vue";
import { requireLogin, getAuthParams, isLoggedIn, getUserId, getCurrentUser } from "@/utils/auth";
import {
  onLoad,
  onShow,
  onPageScroll,
  onReachBottom,
  onShareAppMessage,
  onShareTimeline,
  onUnload
} from "@dcloudio/uni-app";
import useMescroll from "@/uni_modules/mescroll-uni/hooks/useMescroll.js";
import {store} from "@/store";
import {navto, pay, copy, callPhone} from "@/utils";

const huodong_id = ref(null);
const info = ref({});
const scrollTop = ref(0); // 向上滚动的距离
const popupShow = ref(false); // 报名列表及提问列表弹窗
const type = ref(2); // 1查看更多报名，2全部问题
const popupShows = ref(false); // 提问及报名信息弹窗

const iptType = ref("pingjia"); // pingjia提问，xinxi报名信息，erweima联系人二维码
const content = ref(""); // 问题或答案内容

// 回答相关变量
const replyContent = ref("");
const currentQuestion = ref(null);
const replyPopupShow = ref(false);
const goods = ref([
  {num: 0, money: 20, zongMoney: 0},
  {num: 0, money: 30, zongMoney: 0}
]);

const {mescrollInit, downCallback, getMescroll} = useMescroll(
    onPageScroll,
    onReachBottom
);

const actionShow = ref(false);
const baomingForm = ref({
  is_choujiang: 0,
  lianxi_name: "",
  lianxi_mobile: "",
  lianxi_sex: ""
});

// 安全地初始化用户信息，避免渲染层错误
const initUserInfo = () => {
  try {
    const currentUser = getCurrentUser();
    if (currentUser) {
      baomingForm.value.lianxi_name = currentUser.nickname || "";
      baomingForm.value.lianxi_mobile = currentUser.mobile || "";
      baomingForm.value.lianxi_sex = currentUser.sex == 0 ? "1" : (currentUser.sex || "") + "";
    }
  } catch (error) {
    console.warn('初始化用户信息失败:', error);
  }
};
const sharePopup = ref(false);
const btnDisabled = ref(false);
const copyValue = ref("");

// 新增状态
const membershipPopup = ref(false); // 非会员选择弹窗
const qrcodePopup = ref(false); // 二维码弹窗
const isMember = ref(false); // 是否是会员

const loading = ref(false); // 加载状态
const backButtonDark = ref(false); // 控制返回按钮颜色，true为黑色，false为白色

// 在变量定义部分添加
const showQrCodeButton = ref(false); // 控制二维码按钮的显示
const isLiking = ref(false); // 是否正在点赞
const isFavoriting = ref(false); // 是否正在收藏
const localLikeState = ref({ isLiked: false, likeCount: 0 }); // 本地点赞状态
const localFavoriteState = ref(false); // 本地收藏状态

// {{ AURA-X: Add - 活动相册相关状态. Confirmed via 寸止. }}
const activityPhotos = ref([]); // 活动相册图片列表
const displayPhotos = computed(() => {
  // 最多显示6张图片
  return activityPhotos.value.slice(0, 6);
});

// 添加ref变量
const qrcodeLoadFailed = ref(false); // 图片加载失败标志
const qrcodeLoading = ref(false);    // 图片加载中标志
const qrcodeRenderError = ref(false); // 图片渲染错误标志

// 签到相关状态
const checkinStatus = ref({
  disabled: false,
  icon: 'checkmark-circle',
  color: '#6AC086',
  checked: false
});

const userLocation = ref({
  latitude: null,
  longitude: null
});

// 计算属性：检查是否为活动发布者（用于提问回答功能）
const isActivityCreator = computed(() => {
  return getUserId() == info.value?.user?.uid;
});

// {{ AURA-X: Add - 举报功能相关响应式数据. Confirmed via 寸止. }}
// 举报功能改为跳转新页面，无需弹窗相关数据

// 计算属性：是否显示举报链接
const canShowReportLink = computed(() => {
  // 只有已登录且参加了活动的用户才能举报
  const currentUserId = getUserId();
  if (!currentUserId || !info.value?.baoming_order?.order_id) {
    return false;
  }

  // 不能举报自己发布的活动
  if (currentUserId == info.value?.user?.uid) {
    return false;
  }

  return true;
});


onShareTimeline(() => {
  return {
    title: info.value.name,
    path: `/pages/bundle/index/activeInfo?id=${huodong_id.value}&pid=${
        store().$state.userInfo.uid
    }`,
    imageUrl: info.value.img_url
  };
});
onShareAppMessage(async () => {
  return await handleShareAppMessage();
});

// 增强的分享处理函数
const handleShareAppMessage = async () => {
  const activityId = huodong_id.value;
  const defaultShareData = {
    title: '精彩活动等你来',
    path: `/pages/bundle/index/activeInfo?id=${activityId}&pid=${store().$state.userInfo?.uid || 0}`,
    imageUrl: store().$state.config?.img_config?.app_logo?.val || ''
  };

  try {
    // 1. 检查活动数据完整性
    if (!info.value || !info.value.name) {
      console.warn('活动信息未加载完成，使用默认分享信息');
      await recordShareEvent(activityId, 'default_share');
      return defaultShareData;
    }

    // 2. 构建分享数据
    const shareData = {
      title: info.value.name || defaultShareData.title,
      path: `/pages/bundle/index/activeInfo?id=${activityId}&pid=${store().$state.userInfo?.uid || 0}`,
      imageUrl: info.value.img_url || defaultShareData.imageUrl
    };

    // 3. 记录分享事件（异步处理，不阻塞分享）
    recordShareEvent(activityId, 'activity_share');

    // 4. 更新全局分享状态
    const currentShareState = store().getActivityShareState(activityId);
    const newShareCount = (currentShareState?.shareCount || 0) + 1;
    store().updateActivityShare(activityId, newShareCount);

    console.log('分享数据构建成功:', shareData);
    return shareData;

  } catch (error) {
    console.error('分享配置失败:', error);
    // 降级处理：记录错误并返回默认分享数据
    await recordShareEvent(activityId, 'share_error', error.message);
    return defaultShareData;
  }
};

// 分享事件记录函数（带重试机制）
const recordShareEvent = async (activityId, eventType, errorMsg = null) => {
  const maxRetries = 3;
  let retryCount = 0;

  const attemptRecord = async () => {
    try {
      await userfenxiang_event({
        type: 2,
        item_id: activityId,
        event_type: eventType,
        error_msg: errorMsg
      });
      console.log(`分享统计成功 - 活动ID: ${activityId}, 事件类型: ${eventType}`);
    } catch (error) {
      retryCount++;
      console.warn(`分享统计失败 (第${retryCount}次尝试):`, error);

      if (retryCount < maxRetries) {
        // 延迟重试
        setTimeout(() => attemptRecord(), 1000 * retryCount);
      } else {
        console.error('分享统计最终失败，已达到最大重试次数');
        // 可以在这里添加本地存储，稍后重试
        storeFailedShareEvent(activityId, eventType, errorMsg);
      }
    }
  };

  // 异步执行，不阻塞分享流程
  setTimeout(() => attemptRecord(), 0);
};

// 存储失败的分享事件，用于后续重试
const storeFailedShareEvent = (activityId, eventType, errorMsg) => {
  try {
    const failedEvents = uni.getStorageSync('failedShareEvents') || [];
    failedEvents.push({
      activityId,
      eventType,
      errorMsg,
      timestamp: Date.now()
    });
    // 只保留最近100条失败记录
    if (failedEvents.length > 100) {
      failedEvents.splice(0, failedEvents.length - 100);
    }
    uni.setStorageSync('failedShareEvents', failedEvents);
  } catch (error) {
    console.error('存储失败的分享事件时出错:', error);
  }
};
onPageScroll((e) => {
  scrollTop.value = e.scrollTop;

  // 根据滚动位置检测背景颜色并调整返回按钮颜色
  // 当滚动到封面图区域时（通常前400rpx），使用白色按钮
  // 当滚动到白色内容区域时，使用黑色按钮
  if (e.scrollTop < 300) {
    // 在封面图区域，使用白色按钮
    backButtonDark.value = false;
  } else {
    // 在白色内容区域，使用黑色按钮
    backButtonDark.value = true;
  }
});
onLoad(async (e) => {
  if (e?.id) huodong_id.value = e.id;
  if (e?.pid) store().changePid(e.pid);

  // 安全地初始化用户信息
  initUserInfo();

  // 检查用户是否是会员
  checkMemberStatus();

  // 从本地存储恢复活动状态
  store().loadActivityStateFromLocal();

  // 检查是否有缓存的活动详情
  const cachedDetail = store().getCachedActivityDetail(e.id);
  if (cachedDetail && Date.now() - cachedDetail.cached_at < 300000) { // 5分钟缓存
    console.log('使用缓存的活动详情');
    info.value = cachedDetail;
    // 立即显示缓存内容，然后异步更新
    nextTick(() => {
      // 延迟更新，避免阻塞UI渲染
      setTimeout(() => getInfo(), 50);
    });
    return; // 使用缓存时直接返回
  }

  // 添加弹窗关闭事件监听
  uni.$on('closeAllPopups', closePopups);
});
onShow(() => {
  getInfo();
});

// 检查会员状态
const checkMemberStatus = () => {
  // 这里根据实际的会员判断逻辑来设置isMember的值
  const currentUser = getCurrentUser();
  isMember.value = currentUser?.is_huiyuan === 1;
};



// 单次报名支付
const singleActivityPayment = async () => {
  membershipPopup.value = false;
  iptType.value = "xinxi";
  popupShows.value = true;

  // 不再需要设置单次支付标记，由后端根据会员状态自动计算价格
};

// 跳转会员页面
const navigateToMembership = () => {
  membershipPopup.value = false;
  navto('/pages/bundle/user/vip');
};

// 显示二维码
const showQrCodes = () => {
  // 确保弹窗没有重复弹出
  if (popupShows.value) {
    return;
  }


  // 显示群二维码
  popupShows.value = true;
  qrcodePopup.value = true;
  iptType.value = "erweima";
};

// 专门显示联系人二维码
const showContactQrCode = () => {
  qrcodePopup.value = false;
  iptType.value = "erweima";
  popupShows.value = true;
  // 检查联系人二维码URL有效性

};

// 上拉加载的回调: 其中num:当前页 从1开始, size:每页数据条数,默认10
const upCallback = async (mescroll) => {
  const api =
      type.value === 1
          ? getUserId() == info.value.user.uid
              ? huodongget_baoming_list
              : huodongget_baoming_list_public
          : huodongget_pingjia;

  try {
    const res = await api({
      huodong_id: huodong_id.value,
      page: mescroll.num,
      page_size: mescroll.size
    });

    if (res && res.status === "ok") {
      const curPageData = res.data || []; // 当前页数据
      if (mescroll.num == 1) goods.value = []; // 第一页需手动制空列表
      goods.value = goods.value.concat(curPageData); //追加新数据
      mescroll.endBySize(curPageData.length, res.count || 0);
    } else if (res && res.status === "empty") {
      // 处理空数据情况
      if (mescroll.num == 1) goods.value = [];
      mescroll.endBySize(0, 0);
      console.log('数据为空:', res.msg || '暂无数据');
    } else {
      console.warn('API返回异常:', res);
      mescroll.endErr(); // 请求失败, 结束加载
      // 移除数据加载失败提示，避免误导用户
      // if (mescroll.num == 1) {
      //   uni.$u.toast(res?.msg || '数据加载失败');
      // }
    }
  } catch (error) {
    console.error('请求失败:', error);
    mescroll.endErr(); // 请求失败, 结束加载
    if (mescroll.num == 1) {
      uni.$u.toast('网络错误，请检查网络连接');
    }
  }
};
// 获取活动详情及部分报名列表和评价列表
const getInfo = async () => {
  loading.value = true;
  try {
    // 重置二维码状态
    resetQRCodeState();
    let infoRes;
    try {
      // 修复：确保API调用包含必要参数，支持未登录用户访问
      const authParams = getAuthParams();
      const apiParams = {
        huodong_id: huodong_id.value,
        uid: authParams.uid || 0,  // 未登录时传递0
        token: authParams.token || ''  // 未登录时传递空字符串
      };

      console.log('调用活动详情API，参数:', apiParams);
      infoRes = await huodongget_info(apiParams);

      console.log('活动详情API响应:', infoRes);
      if (infoRes.status !== "ok") {
        console.error('获取活动信息失败:', infoRes);
        // 优化错误处理：区分不同类型的错误
        if (infoRes.status === "relogin") {
          // 认证失败，清理本地登录状态并提示用户
          console.log('用户认证失败，清理本地状态');
          const { logout } = await import('@/utils/auth');
          logout();
          uni.$u.toast('登录已过期，请重新登录');
          // 可以选择跳转到登录页面或继续以游客身份浏览
          setTimeout(() => {
            uni.navigateTo({
              url: '/pages/bundle/common/login'
            });
          }, 1500);
        } else if (infoRes.status === "error") {
          const errorMsg = infoRes.msg || infoRes.message || '活动信息获取失败';
          uni.$u.toast(errorMsg);
        }
        loading.value = false;
        return;
      }
    } catch (error) {
      console.error('获取活动信息异常:', error);
      // 优化异常处理：减少误导性错误提示，只在网络错误时显示
      if (error.name === 'NetworkError' || error.message.includes('network') || error.message.includes('timeout')) {
        uni.$u.toast('网络连接失败，请检查网络后重试');
      } else {
        // 其他错误只记录日志，不显示给用户，避免误导
        console.warn('活动详情加载遇到问题，但可能不影响正常使用:', error);
      }
      loading.value = false;
      return;
    }
    
    // 基础数据获取成功，先赋值基础信息
    info.value = reactive({...infoRes.data});
    
    // 尝试获取关注状态，失败不影响主流程
    try {
      const guanzhuRes = await userguanzhu_check({
        to_uid: infoRes.data.user.uid
      });
      if (guanzhuRes.status === "ok") {
        info.value.is_guanzhu = guanzhuRes.data;
      }
    } catch (error) {
      console.warn('获取关注状态失败:', error);
      // 设置默认未关注状态
      info.value.is_guanzhu = false;
    }

    // 尝试获取报名列表，失败不影响主流程
    try {
      const peopleRes = await huodongget_baoming_list_public({
        huodong_id: huodong_id.value,
        page: 1,
        page_size: 50
      });
      if (peopleRes && peopleRes.data) {
        info.value.peopleList = peopleRes.data;
      } else {
        info.value.peopleList = [];
      }
    } catch (error) {
      console.warn('获取报名列表失败:', error);
      info.value.peopleList = [];
    }

    // 尝试获取评价，失败不影响主流程
    try {
      const pingjiaRes = await huodongget_pingjia({
        page: 1,
        page_size: 10,
        huodong_id: huodong_id.value
      });
      if (pingjiaRes && pingjiaRes.data) {
        info.value.pingjiaList = pingjiaRes.data;
        info.value.pingjiaCount = pingjiaRes.count || 0;
      } else {
        info.value.pingjiaList = [];
        info.value.pingjiaCount = 0;
      }
    } catch (error) {
      console.warn('获取评价失败:', error);
      info.value.pingjiaList = [];
      info.value.pingjiaCount = 0;
    }

    // {{ AURA-X: Add - 加载活动相册. Confirmed via 寸止. }}
    // 尝试获取活动相册，失败不影响主流程
    try {
      await loadActivityPhotos();
    } catch (error) {
      console.warn('获取活动相册失败:', error);
    }

    // 更新二维码按钮显示状态 - 无登录用户显示false
    showQrCodeButton.value = false;
    if (isLoggedIn()) {
      // 检查用户是否在报名列表中
      const currentUserUid = getUserId();
      const userInPeopleList = info.value.peopleList?.some(item => {
        return item.user?.uid === currentUserUid;
      });

      // 从原始数据中检查报名信息
      let hasBaomingOrder = false;
      if (infoRes.data.baoming_order && infoRes.data.baoming_order.order_id) {
        hasBaomingOrder = true;
      }
      // 通过报名列表检查是否报名
      else if (userInPeopleList) {
        hasBaomingOrder = true;
      }
      
      // 如果用户已登录且在报名列表中，才显示二维码按钮
      showQrCodeButton.value = hasBaomingOrder;
    }

    // 尝试获取分享链接
    try {
      const linkRes = await configget_deep_link({
        path: "/pages/bundle/index/activeInfo",
        query: `id=${huodong_id.value}&pid=${store().$state.userInfo?.uid || 0}`
      });

      if (linkRes && linkRes.status === "ok") {
      let people = "";
        if (info.value.peopleList?.length > 0)
          info.value.peopleList.forEach(
            (val, i) => (people = people + `${i + 1}. ${val.user.nickname}\r\n`)
        );
          
        copyValue.value = `${info.value.name}\r\n时间：${info.value.time}\r\n地点：${info.value.sheng}-${info.value.shi}-${info.value.qu}-${info.value.addr}\r\n组织者：${info.value.lianxi_name}\r\n费用：${info.value.money}\r\n已报名：${info.value.baoming_num}/${info.value.num}\r\n${people}报名链接：${linkRes.data}`;
      }
    } catch (error) {
      console.warn('获取分享链接失败:', error);
    }
    
    // 更新签到状态
    updateCheckinStatus();

    // 同步报名状态到全局状态管理
    syncEnrollmentState();

    // 初始化本地状态
    initializeLocalStates();

  } catch (error) {
    console.error("获取活动信息失败:", error);
    // 移除获取活动信息失败提示，避免误导用户
    // uni.$u.toast("获取活动信息失败");
  } finally {
    loading.value = false; // 结束加载
  }
};

// 显示回答对话框
const showReplyDialog = (question) => {
  currentQuestion.value = question;
  replyContent.value = "";
  replyPopupShow.value = true;
};

// 提交回答
const submitReply = async () => {
  if (!replyContent.value.trim()) {
    uni.$u.toast('请输入回答内容');
    return;
  }

  try {
    const res = await huodongreply_pingjia({
      uid: getUserId(),
      token: getToken(),
      pingjia_id: currentQuestion.value.id,
      reply_content: replyContent.value.trim()
    });

    if (res.status === "ok") {
      replyPopupShow.value = false;
      replyContent.value = "";
      currentQuestion.value = null;
      uni.$u.toast('回答成功');
      // 刷新活动信息以显示最新回答
      await getInfo();
    } else {
      uni.$u.toast(res.msg);
    }
  } catch (error) {
    console.error('回答提问失败:', error);
    uni.$u.toast('回答失败，请重试');
  }
};

// 重置二维码状态
const resetQRCodeState = () => {
  qrcodeLoadFailed.value = false;
  qrcodeLoading.value = true; // 默认显示加载中
  qrcodeRenderError.value = false;
};

// 提交提问或报名信息
const submit = async () => {
  if (iptType.value === "pingjia") {
    // 修改：移除登录验证，允许所有用户提问
    if (!requireLogin('', '请先登录后再提问')) {
      return;
    }

    if (!content.value.trim()) {
      uni.$u.toast('请输入提问内容');
      return;
    }

    const res = await huodongadd_pingjia({
      huodong_id: huodong_id.value,
      contents: content.value.trim()
    });
    if (res.status === "ok") {
      popupShows.value = false;
      content.value = "";
      baomingForm.value.lianxi_name = "";
      baomingForm.value.lianxi_mobile = "";
      uni.$u.toast('提问提交成功');
      // 刷新活动信息以显示最新提问
      await getInfo();
    } else uni.$u.toast(res.msg);
  } else if (iptType.value === "xinxi") {
    // 简化验证，系统自动使用用户信息
    popupShows.value = false;

    // 调用报名接口
    try {
      const dingRes = await huodongadd_baoming({
        huodong_id: huodong_id.value,
        ...baomingForm.value
      });

      if (dingRes.status === "ok") {
        // {{ AURA-X: Modify - 根据活动支付方式和金额决定是否调用微信支付. Confirmed via 寸止. }}
        const payAmount = parseFloat(dingRes.money || 0);
        const isOnlinePayment = info.value.pay_type === 1; // 1=线上支付，2=线下收款

        if (payAmount === 0 || !isOnlinePayment) {
          // 免费活动或线下收款活动，直接报名成功
          if (payAmount === 0) {
            uni.$u.toast("报名成功！");
          } else {
            uni.$u.toast("报名成功！请线下向发布方付款");
          }

          // 刷新活动信息
          await getInfo();

          // 显示二维码
          iptType.value = "erweima";
          popupShows.value = true;
        } else {
          // 线上支付活动，调用微信支付
          const obj = {
            pay_type: 1,
            type: 2, // 活动报名类型
            order_id: dingRes.order_id,
            money: dingRes.money
          };

          const wxRes = await payweixin_pay(obj);
          if (wxRes.status === "ok") {
            const signRes = await payget_weixinpay_sign({
              prepay_id: wxRes.prepay_id
            });

            // 调用支付
            const payRes = await pay(signRes);

            if (payRes.errMsg === "requestPayment:ok") {
              // 支付成功
              uni.$u.toast("支付成功！");

              // 刷新活动信息
              await getInfo();

              // 显示二维码
              iptType.value = "erweima";
              popupShows.value = true;
            } else {
              // 支付失败
              uni.$u.toast("支付失败，订单已取消");

              // 删除未支付的订单
              await huodongdelete_baoming({
                order_id: dingRes.order_id
              });
            }
          } else {
            // 微信支付接口调用失败
            uni.$u.toast(wxRes.msg || "支付接口调用失败");

            // 删除未支付的订单
            await huodongdelete_baoming({
              order_id: dingRes.order_id
            });
          }
        }
      } else {
        // 报名失败
        uni.$u.toast(dingRes.msg || "报名失败");
      }
    } catch (error) {
      console.error('报名过程出错:', error);
      uni.$u.toast("报名失败，请稍后重试");
    }
  }
};

//支付
// 选择用什么方式进行支付
const selectClick = async () => {
  // 关闭支付方式选择弹窗
  actionShow.value = false;

  const dingRes = await huodongadd_baoming({
    huodong_id: huodong_id.value,
    ...baomingForm.value
  });

  let payRes;
  if (dingRes.status === "ok") {
    // {{ AURA-X: Modify - 根据活动支付方式决定是否调用微信支付. Confirmed via 寸止. }}
    const payAmount = parseFloat(dingRes.money || 0);
    const isOnlinePayment = info.value.pay_type === 1; // 1=线上支付，2=线下收款

    if (payAmount === 0 || !isOnlinePayment) {
      // 免费活动或线下收款活动，直接报名成功
      if (payAmount === 0) {
        uni.$u.toast("报名成功！");
      } else {
        uni.$u.toast("报名成功！请线下向发布方付款");
      }

      // 刷新活动信息
      await getInfo();

      // 显示二维码
      iptType.value = "erweima";
      popupShows.value = true;
      return;
    }

    // 线上支付活动，调用微信支付
    const obj = {
      pay_type: 1,
      type: 2, // 活动报名类型
      order_id: dingRes.order_id,
      money: dingRes.money
    };

    const wxRes = await payweixin_pay(obj);
    if (wxRes.status === "ok") {
      const signRes = await payget_weixinpay_sign({
        prepay_id: wxRes.prepay_id
      });

      // 调用支付
      payRes = await pay(signRes);

      if (payRes.errMsg === "requestPayment:ok") {
        // 支付成功，设置状态
        payRes.status = "ok";

        // 刷新活动信息
        await getInfo();

        // 显示二维码
        iptType.value = "erweima";
        popupShows.value = true;
      } else {
        // 支付失败
        payRes.msg = "支付失败";

        // 删除未支付的订单
        await huodongdelete_baoming({
          order_id: dingRes.order_id
        });

        uni.$u.toast("支付失败，订单已取消");
      }
    } else {
      // 微信支付接口调用失败
      uni.$u.toast(wxRes.msg || "支付接口调用失败");

      // 删除未支付的订单
      await huodongdelete_baoming({
        order_id: dingRes.order_id
      });
    }
  } else {
    // 报名失败
    uni.$u.toast(dingRes.msg || "报名失败");
  }
};


// 收藏
const collect = async () => {
  const api = info.value.is_shoucang
      ? huodongshoucang_del({ids: huodong_id.value})
      : huodongshoucang_add({huodong_id: huodong_id.value});
  const res = await api;
  if (res.status === "ok")
    info.value.is_shoucang = info.value.is_shoucang ? 0 : 1;
  uni.$u.toast(res.msg);
};

// 签到功能
const handleCheckin = async () => {
  if (checkinStatus.value.checked) {
    uni.$u.toast('您已经签到过了');
    return;
  }

  if (checkinStatus.value.disabled) {
    uni.$u.toast('当前不在签到时间范围内');
    return;
  }

  // 检查是否已报名
  if (!info.value?.baoming_order?.order_id) {
    uni.$u.toast('请先报名参加活动');
    return;
  }

  // 线下活动需要获取地理位置
  if (info.value.is_online === 0) {
    await getUserLocation();
  }

  // 执行签到
  await performCheckin();
};

// 获取用户地理位置
const getUserLocation = () => {
  return new Promise((resolve, reject) => {
    uni.getLocation({
      type: 'gcj02',
      success: (res) => {
        userLocation.value.latitude = res.latitude;
        userLocation.value.longitude = res.longitude;
        resolve(res);
      },
      fail: (err) => {
        console.error('获取位置失败:', err);
        uni.showModal({
          title: '位置权限',
          content: '签到需要获取您的位置信息，请在设置中开启位置权限',
          showCancel: false
        });
        reject(err);
      }
    });
  });
};

// 执行签到
const performCheckin = async () => {
  uni.showLoading({ title: '签到中...' });

  try {
    const params = {
      huodong_id: huodong_id.value
    };

    // 线下活动需要传递位置信息
    if (info.value.is_online === 0) {
      if (!userLocation.value.latitude || !userLocation.value.longitude) {
        uni.hideLoading();
        uni.$u.toast('获取位置信息失败，请重试');
        return;
      }
      params.lat = userLocation.value.latitude;
      params.lng = userLocation.value.longitude;
    }

    const res = await huodongcheckin(params);

    uni.hideLoading();

    if (res.status === 'ok') {
      // 签到成功
      checkinStatus.value.checked = true;
      checkinStatus.value.disabled = true;
      checkinStatus.value.icon = 'checkmark-circle-fill';
      checkinStatus.value.color = '#6AC086';

      const pointsText = res.data?.points_awarded ? `，获得${res.data.points_awarded}积分` : '';
      const distanceText = res.data?.distance ? `，距离活动地点${res.data.distance}米` : '';

      uni.showModal({
        title: '签到成功',
        content: `签到成功${pointsText}${distanceText}`,
        showCancel: false
      });

      // 更新用户积分信息
      if (res.data?.points_balance) {
        store().$state.userInfo.points = res.data.points_balance;
      }
    } else {
      uni.$u.toast(res.msg || '签到失败');
    }
  } catch (error) {
    uni.hideLoading();
    console.error('签到失败:', error);
    uni.$u.toast('签到失败，请稍后重试');
  }
};

// {{ AURA-X: Modify - 改为基于报名截止时间判断活动状态. Confirmed via 寸止. }}
// 获取活动状态
const getActivityStatus = () => {
  if (!info.value || !info.value.start_time) return 'unknown';

  const now = new Date();
  const baomingEndTime = info.value.baoming_end_time || info.value.start_time;
  const startTime = new Date(info.value.start_time?.replaceAll('-', '/'));
  const endTime = info.value.end_time ? new Date(info.value.end_time?.replaceAll('-', '/')) : null;
  const registrationEndTime = new Date(baomingEndTime?.replaceAll('-', '/'));

  if (now < registrationEndTime) {
    return 'registration'; // 报名中
  } else if (endTime && now > endTime) {
    return 'ended'; // 已结束（历史活动）
  } else {
    return 'ongoing'; // 进行中
  }
};

// 检查是否为历史活动
const isHistoryActivity = () => {
  return getActivityStatus() === 'ended';
};

// {{ AURA-X: Add - 活动相册相关方法. Confirmed via 寸止. }}
// 加载活动相册图片
const loadActivityPhotos = async () => {
  try {
    const res = await huodongget_activity_photos({
      activity_id: huodong_id.value,
      page: 1,
      page_size: 10 // 只加载前10张用于预览
    });

    if (res.status === 'ok') {
      activityPhotos.value = res.data || [];
    } else if (res.status === 'empty') {
      activityPhotos.value = [];
    }
  } catch (error) {
    console.error('加载活动相册失败:', error);
    activityPhotos.value = [];
  }
};

// 跳转到活动相册页面
const goToActivityAlbum = () => {
  navto(`/pages/bundle/activity/album?activity_id=${huodong_id.value}`);
};

// 预览活动相册图片
const previewActivityPhotos = (index) => {
  const urls = activityPhotos.value.map(photo => photo.photo_url);
  uni.previewImage({
    urls: urls,
    current: index
  });
};

// 检查是否可以管理活动（取消/修改）
const canManageActivity = () => {
  const status = getActivityStatus();
  // 只有报名中和进行中的活动可以管理，历史活动不能管理
  return status === 'registration' || status === 'ongoing';
};

// {{ AURA-X: Modify - 修改退款政策显示逻辑，支持线上线下不同展示. Confirmed via 寸止. }}
// 检查是否显示退款政策
const shouldShowRefundPolicy = computed(() => {
  if (!info.value) return false;

  // 只要是收费活动就显示退款政策（不管线上线下）
  const hasFee = parseFloat(info.value.money) > 0 || parseFloat(info.value.member_money) > 0;

  return hasFee;
});

// {{ AURA-X: Add - 判断是否为线下收款活动. Confirmed via 寸止. }}
// 检查是否为线下收款活动
const isOfflinePayment = computed(() => {
  if (!info.value) return false;
  return info.value.pay_type === 2; // pay_type = 2 表示线下收款
});

// 获取退款政策详情
const getRefundPolicyDetails = computed(() => {
  if (!info.value || !info.value.refund_rule) return [];

  const refundRule = parseInt(info.value.refund_rule);

  switch (refundRule) {
    case 1: // 随时退款
      return [
        { time: '活动开始前', refund: '100%' },
        { time: '活动开始后', refund: '不支持退款' }
      ];
    case 2: // 12小时规则
      return [
        { time: '开始前12小时前', refund: '100%' },
        { time: '开始前12小时内', refund: '50%' },
        { time: '活动开始后', refund: '不支持退款' }
      ];
    case 3: // 24小时规则
      return [
        { time: '开始前24小时前', refund: '100%' },
        { time: '开始前24小时内', refund: '50%' },
        { time: '活动开始后', refund: '不支持退款' }
      ];
    case 4: // 48小时规则
      return [
        { time: '开始前48小时前', refund: '100%' },
        { time: '开始前48小时内', refund: '30%' },
        { time: '活动开始后', refund: '不支持退款' }
      ];
    case 5: // 72小时规则
      return [
        { time: '开始前72小时前', refund: '100%' },
        { time: '开始前72小时内', refund: '30%' },
        { time: '活动开始后', refund: '不支持退款' }
      ];
    default:
      return [];
  }
});

// 获取退款规则标题
const getRefundRuleTitle = computed(() => {
  if (!info.value || !info.value.refund_rule) return '';

  const refundRule = parseInt(info.value.refund_rule);
  const titles = {
    1: '随时退款',
    2: '12小时退款规则',
    3: '24小时退款规则',
    4: '48小时退款规则',
    5: '72小时退款规则'
  };

  return titles[refundRule] || '';
});

// {{ AURA-X: Add - 计算退款金额信息. Confirmed via 寸止. }}
// 计算退款金额信息
const calculateRefundInfo = () => {
  if (!info.value || !info.value.baoming_order) {
    return { amount: 0, percentage: '0%' };
  }

  const originalAmount = parseFloat(info.value.baoming_order.money || 0);
  if (originalAmount <= 0) {
    return { amount: 0, percentage: '0%' };
  }

  const refundRule = parseInt(info.value.refund_rule || 1);
  const startTime = new Date(info.value.start_time?.replaceAll('-', '/'));
  const currentTime = new Date();

  // 如果活动已开始，不支持退款
  if (currentTime >= startTime) {
    return { amount: 0, percentage: '0%' };
  }

  // 计算距离活动开始的小时数
  const hoursBeforeStart = (startTime.getTime() - currentTime.getTime()) / (1000 * 60 * 60);

  // 根据退款规则计算退款比例
  let refundPercentage = 1.0; // 默认100%退款

  switch (refundRule) {
    case 1: // 随时退款
      refundPercentage = 1.0; // 100%
      break;

    case 2: // 12小时规则
      refundPercentage = hoursBeforeStart >= 12 ? 1.0 : 0.5;
      break;

    case 3: // 24小时规则
      refundPercentage = hoursBeforeStart >= 24 ? 1.0 : 0.5;
      break;

    case 4: // 48小时规则
      refundPercentage = hoursBeforeStart >= 48 ? 1.0 : 0.3;
      break;

    case 5: // 72小时规则
      refundPercentage = hoursBeforeStart >= 72 ? 1.0 : 0.3;
      break;

    default:
      refundPercentage = 1.0; // 默认100%
      break;
  }

  const refundAmount = (originalAmount * refundPercentage).toFixed(2);
  const percentageText = (refundPercentage * 100).toFixed(0) + '%';

  return {
    amount: refundAmount,
    percentage: percentageText
  };
};

// 更新签到状态
const updateCheckinStatus = () => {
  if (!info.value) return;

  // {{ AURA-X: Modify - 检查活动是否启用签到功能. Confirmed via 寸止. }}
  // 检查活动是否启用了签到功能
  if (!info.value.enable_checkin || info.value.enable_checkin === '0') {
    checkinStatus.value.disabled = true;
    checkinStatus.value.icon = 'close-circle';
    checkinStatus.value.color = '#999';
    return;
  }

  const now = new Date();
  const startTime = new Date(info.value.start_time?.replaceAll('-', '/'));
  const checkinStartTime = new Date(startTime.getTime() - 30 * 60 * 1000); // 开始前30分钟
  const checkinEndTime = new Date(startTime.getTime() + 30 * 60 * 1000);   // 开始后30分钟

  // 检查是否在签到时间范围内
  const inTimeRange = now >= checkinStartTime && now <= checkinEndTime;

  if (!inTimeRange) {
    checkinStatus.value.disabled = true;
    checkinStatus.value.icon = 'clock';
    checkinStatus.value.color = '#999';
  } else {
    checkinStatus.value.disabled = false;
    checkinStatus.value.icon = 'checkmark-circle';
    checkinStatus.value.color = '#6AC086';
  }

  // TODO: 这里应该从后端获取用户是否已经签到过
  // 暂时设置为未签到状态
  checkinStatus.value.checked = false;
};

// 报名
const submitActive = async () => {
  const msg = getBtnText();

  let api;
  if (msg === "取消报名") {
    if (!info.value?.baoming_order?.order_id) {
      uni.$u.toast("订单信息有误，请刷新重试");
      return;
    }

    // {{ AURA-X: Modify - 显示退款金额信息的确认弹窗. Confirmed via 寸止. }}
    // 计算退款金额信息
    const refundInfo = calculateRefundInfo();
    const refundText = refundInfo.amount > 0 ?
      `\n\n根据退款规则，您将获得 ${refundInfo.amount} 元退款（${refundInfo.percentage}）` :
      '\n\n根据退款规则，活动已开始不支持退款';

    // 显示确认弹窗
    uni.showModal({
      title: '取消报名',
      content: `确定要取消报名吗？${refundText}`,
      success: async function (res) {
        if (res.confirm) {
          try {
            const cancelRes = await huodongcancel_baoming({
              order_id: info.value?.baoming_order?.order_id
            });

            if (cancelRes.status === "ok") {
              // 成功取消报名
              uni.$u.toast("已取消报名");

              // 立即清除本地报名状态，不等待接口返回
              info.value.baoming_order = null;
              showQrCodeButton.value = false;

              // 强制重新获取数据
              await getInfo();
            } else {
              uni.$u.toast(cancelRes.msg || "取消失败");
            }
          } catch (error) {
            uni.$u.toast("操作失败，请稍后重试");
          }
        }
      }
    });
    return;
  } else if (msg === "我要报名") {
    // 使用统一的登录检查
    if (!requireLogin('', '请先登录后再报名参加活动')) {
      return; // 未登录，已自动跳转到登录页面
    }

    // 检查活动是否仅会员可参加
    if (info.value.member_only == 1) {
      const currentUser = getCurrentUser();
      if (!currentUser || currentUser.is_huiyuan != 1) {
        // 非会员用户，显示会员提示
        uni.showModal({
          title: '会员专享活动',
          content: '该活动仅限会员参加，是否前往开通会员？',
          confirmText: '开通会员',
          cancelText: '取消',
          success: function (res) {
            if (res.confirm) {
              navto('/pages/bundle/user/vip');
            }
          }
        });
        return;
      }
    }

    // 已登录且满足参与条件，直接进入报名信息填写页面
    iptType.value = "xinxi";
    popupShows.value = true;
    return;
  } else {
    uni.$u.toast(msg);
    return;
  }
};



// 获取底部按钮内容
const getBtnText = () => {
  // 调试输出报名状态

  // 确保报名状态正确
  const isEnrolled = !!info.value?.baoming_order?.order_id;

  if (isEnrolled) {
    // {{ AURA-X: Modify - 改为基于报名截止时间判断. Confirmed via 寸止. }}
    // 已报名状态
    const baomingEndTime = info.value.baoming_end_time || info.value.start_time;
    if (new Date(baomingEndTime?.replaceAll("-", "/")) * 1 > Date.now() * 1) {
      // 报名截止时间未到，可以取消报名
      // 修改逻辑：不论支付状态如何，都允许取消报名
      btnDisabled.value = false;
      return "取消报名";
    } else {
      if (info.value.choujiang_status == 0) {
        btnDisabled.value = true;
        return "等待抽奖";
      } else if (info.value.choujiang_status == 1) {
        if (
            +info.value.choujiang_zhekou &&
            0 < +info.value.choujiang_zhekou <= 100
        ) {
          btnDisabled.value = true;
          return `${+info.value.choujiang_zhekou}折`;
        } else {
          btnDisabled.value = true;
          return "暂未中奖";
        }
      } else {
        btnDisabled.value = true;
        return "暂无抽奖";
      }
    }
  } else {
    // 未报名状态保持不变
    // 检查报名人数是否已满
    if (info.value.baoming_num >= info.value.num) {
      btnDisabled.value = true;
      return "报名已满";
    } else {
      btnDisabled.value = false;
      return "我要报名";
    }
  }
};

// 抽奖开关
const switchChange = (e) => (baomingForm.value.is_choujiang = e ? 1 : 0);
const openMap = () => {
  uni.openLocation({
    latitude: Number(info.value.lat),
    longitude: Number(info.value.lng)
  });
};
const openImage = (i) => {
  uni.previewImage({
    current: i,
    urls: info.value.imgs
  });
};

// 预览封面图片 - 参考老版本的简单实现
const previewCoverImage = () => {
  if (info.value?.img_url) {
    uni.previewImage({
      current: 0,
      urls: [info.value.img_url]
    });
  }
};


// 使用新API获取屏幕宽度
import { getWindowInfo } from '@/utils/systemInfo.js';

let screenWidth;
try {
  const windowInfo = getWindowInfo();
  screenWidth = windowInfo.screenWidth;
} catch (error) {
  console.warn('获取屏幕宽度失败，使用默认值:', error);
  screenWidth = 375; // 默认值
}
const rpxValue = (4 / 750) * screenWidth;

// 提示框相关变量
const showPopup = ref(false);
const popupTitle = ref('');
const popupContent = ref('');
const showCancelButton = ref(true);
const popupCallback = ref(null);

// 显示提示框
const showAlert = (title, content, showCancel = true, callback = null) => {
  popupTitle.value = title;
  popupContent.value = content;
  showCancelButton.value = showCancel;
  popupCallback.value = callback;
  showPopup.value = true;
};

// 关闭提示框
const closePopup = () => {
  showPopup.value = false;
};

// 确认提示框
const confirmPopup = () => {
  if (popupCallback.value) {
    popupCallback.value();
  }
  showPopup.value = false;
};

// 处理非会员点击"我要报名"的逻辑
const handleNonMemberRegistrationAlert = () => {
  showAlert(
      '会员提示',
      '您当前不是会员，无法报名参加此活动。是否前往开通会员？',
      true,
      () => {
        // 跳转到会员页面
        uni.navigateTo({
          url: '/pages/bundle/user/vip'
        });
      }
  );
};

// 处理点击"x人报名"的逻辑
const showRegistrationList = () => {
  showAlert(
      '报名列表',
      '查看已报名用户列表？',
      true,
      () => {
        // 跳转到报名列表页面
        uni.navigateTo({
          url: `/pages/bundle/index/registrationList?id=${activityInfo.id}`
        });
      }
  );
};

// 检查用户是否已报名的辅助函数 - 增强版本
const checkUserEnrolled = () => {
  const activityId = huodong_id.value;
  const currentUserUid = getUserId();

  if (!activityId || !currentUserUid) {
    return false;
  }

  // 1. 优先检查全局状态管理中的报名状态
  const enrollmentState = store().getActivityEnrollmentState(activityId);
  if (enrollmentState && enrollmentState.isEnrolled) {
    console.log('从全局状态获取报名状态: 已报名');
    return true;
  }

  // 2. 检查API返回的订单信息
  if (info.value?.baoming_order?.order_id) {
    console.log('从API数据获取报名状态: 已报名 (有订单ID)');
    // 同步到全局状态
    store().updateActivityEnrollment(
      activityId,
      true,
      info.value.baoming_order.order_id,
      info.value.baoming_order.pay_status || 1
    );
    return true;
  }

  // 3. 检查用户是否在报名列表中
  const inPeopleList = info.value?.peopleList?.some(item =>
    item.user?.uid === currentUserUid
  ) || false;

  // 4. 根据联系信息进行推断（备用方案）
  const currentUser = getCurrentUser();
  const hasMyContactInfo = info.value?.baoming_num > 0 &&
      info.value?.peopleList?.some(item =>
          item.lianxi_name === currentUser?.nickname ||
          item.lianxi_mobile === currentUser?.mobile
      );

  const isEnrolled = inPeopleList || hasMyContactInfo;

  if (isEnrolled) {
    console.log('从报名列表推断报名状态: 已报名');
    // 同步到全局状态（使用推断的状态）
    store().updateActivityEnrollment(activityId, true, 'inferred_enrollment', 1);
  }

  return isEnrolled;
};

// 同步报名状态到全局状态管理
const syncEnrollmentState = () => {
  const activityId = huodong_id.value;
  if (!activityId) return;

  const isEnrolled = checkUserEnrolled();
  const orderId = info.value?.baoming_order?.order_id || null;
  const payStatus = info.value?.baoming_order?.pay_status || 0;

  // 更新全局状态
  store().updateActivityEnrollment(activityId, isEnrolled, orderId, payStatus);

  // 更新二维码按钮显示状态
  showQrCodeButton.value = isEnrolled;

  console.log(`同步报名状态完成 - 活动ID: ${activityId}, 已报名: ${isEnrolled}, 订单ID: ${orderId}`);
};

// 优化的点赞功能
const handleLike = async () => {
  if (isLiking.value) return; // 防止重复点击

  // 使用统一的登录校验
  if (!requireLogin('', '请先登录后再点赞')) {
    return;
  }

  const activityId = huodong_id.value;
  if (!activityId) return;

  isLiking.value = true;

  try {
    // 获取当前状态
    const currentState = store().getActivityLikeState(activityId) || localLikeState.value;
    const isCurrentlyLiked = currentState.isLiked || info.value.is_zan;
    const currentCount = currentState.likeCount || info.value.zan_num || 0;

    // 乐观更新UI
    const newIsLiked = !isCurrentlyLiked;
    const newCount = newIsLiked ? currentCount + 1 : Math.max(0, currentCount - 1);

    // 立即更新本地状态
    localLikeState.value = { isLiked: newIsLiked, likeCount: newCount };
    info.value.is_zan = newIsLiked;
    info.value.zan_num = newCount;

    // 更新全局状态
    store().updateActivityLike(activityId, newIsLiked, newCount);

    // 调用API
    const api = isCurrentlyLiked
      ? huodongzan_del({ huodong_ids: activityId })
      : huodongzan_add({ huodong_id: activityId });

    const res = await api;

    if (res.status === 'ok') {
      // API成功，确认状态
      console.log('点赞操作成功');
      uni.$u.toast(newIsLiked ? '点赞成功' : '取消点赞');
    } else {
      // API失败，回滚状态
      console.error('点赞操作失败:', res.msg);
      localLikeState.value = { isLiked: isCurrentlyLiked, likeCount: currentCount };
      info.value.is_zan = isCurrentlyLiked;
      info.value.zan_num = currentCount;
      store().updateActivityLike(activityId, isCurrentlyLiked, currentCount);
      uni.$u.toast(res.msg || '操作失败');
    }
  } catch (error) {
    console.error('点赞操作异常:', error);
    // 发生异常时回滚状态
    const currentState = store().getActivityLikeState(activityId);
    if (currentState) {
      localLikeState.value = currentState;
      info.value.is_zan = currentState.isLiked;
      info.value.zan_num = currentState.likeCount;
    }
    uni.$u.toast('网络错误，请重试');
  } finally {
    isLiking.value = false;
  }
};

// 优化的收藏功能
const handleFavorite = async () => {
  if (isFavoriting.value) return; // 防止重复点击

  // 使用统一的登录校验
  if (!requireLogin('', '请先登录后再收藏')) {
    return;
  }

  const activityId = huodong_id.value;
  if (!activityId) return;

  isFavoriting.value = true;

  try {
    // 获取当前状态
    const currentState = store().getActivityFavoriteState(activityId) || localFavoriteState.value;
    const isCurrentlyFavorited = currentState || info.value.is_shoucang;

    // 乐观更新UI
    const newIsFavorited = !isCurrentlyFavorited;

    // 立即更新本地状态
    localFavoriteState.value = newIsFavorited;
    info.value.is_shoucang = newIsFavorited;

    // 更新全局状态
    store().updateActivityFavorite(activityId, newIsFavorited);

    // 调用API
    const api = isCurrentlyFavorited
      ? huodongshoucang_del({ huodong_ids: activityId })
      : huodongshoucang_add({ huodong_id: activityId });

    const res = await api;

    if (res.status === 'ok') {
      // API成功，确认状态
      console.log('收藏操作成功');
      uni.$u.toast(newIsFavorited ? '收藏成功' : '取消收藏');
    } else {
      // API失败，回滚状态
      console.error('收藏操作失败:', res.msg);
      localFavoriteState.value = isCurrentlyFavorited;
      info.value.is_shoucang = isCurrentlyFavorited;
      store().updateActivityFavorite(activityId, isCurrentlyFavorited);
      uni.$u.toast(res.msg || '操作失败');
    }
  } catch (error) {
    console.error('收藏操作异常:', error);
    // 发生异常时回滚状态
    const currentState = store().getActivityFavoriteState(activityId);
    if (currentState !== null) {
      localFavoriteState.value = currentState;
      info.value.is_shoucang = currentState;
    }
    uni.$u.toast('网络错误，请重试');
  } finally {
    isFavoriting.value = false;
  }
};

// 初始化本地状态
const initializeLocalStates = () => {
  const activityId = huodong_id.value;
  if (!activityId) return;

  // 初始化点赞状态
  const likeState = store().getActivityLikeState(activityId);
  if (likeState) {
    localLikeState.value = likeState;
    info.value.is_zan = likeState.isLiked;
    info.value.zan_num = likeState.likeCount;
  } else {
    localLikeState.value = {
      isLiked: info.value.is_zan || false,
      likeCount: info.value.zan_num || 0
    };
  }

  // 初始化收藏状态
  const favoriteState = store().getActivityFavoriteState(activityId);
  if (favoriteState !== null) {
    localFavoriteState.value = favoriteState;
    info.value.is_shoucang = favoriteState;
  } else {
    localFavoriteState.value = info.value.is_shoucang || false;
  }
};

// 修复报名状态的函数
const fixEnrollmentStatus = () => {
  // 如果用户在报名列表中但无订单信息，修正数据
  if (checkUserEnrolled() && !info.value?.baoming_order?.order_id) {
    if (!info.value.baoming_order) {
      info.value.baoming_order = {order_id: 'restored_id', pay_status: 1};
    } else {
      info.value.baoming_order.order_id = 'restored_id';
      info.value.baoming_order.pay_status = 1;
    }
    // 确保二维码按钮可见
    showQrCodeButton.value = true;
    return true;
  }
  return false;
};

// 弹窗关闭处理
const closePopups = () => {
  // 关闭所有二维码相关弹窗
  qrcodePopup.value = false;

  // 如果当前显示的是二维码弹窗，先关闭弹窗
  if (iptType.value === "erweima") {
    popupShows.value = false;
    // 延迟重置类型，防止视觉跳动
    setTimeout(() => {
      iptType.value = "";
    }, 300);
  }
};

// 在二维码弹窗关闭按钮上添加这个处理程序
// 在对应弹窗处理的地方调用这个函数

// 确保在页面卸载时移除事件监听
onUnload(() => {
  uni.$off('closeAllPopups');
});

// 处理二维码加载错误
const handleQrcodeLoadError = (e) => {
  qrcodeLoadFailed.value = true;
  qrcodeLoading.value = false;

  // 延迟显示错误状态，避免闪烁
  // 移除二维码加载失败提示，避免误导用户
  // setTimeout(() => {
  //   uni.$u.toast("二维码加载失败，请点击重试");
  // }, 300);
};


// 重试加载二维码
const retryLoadQrcode = (qrcode) => {
  if (!qrcode) {
    uni.$u.toast("二维码链接不存在");
    return;
  }


  // 重置状态
  qrcodeLoadFailed.value = false;
  qrcodeLoading.value = true;

  // 使用getImageInfo预加载图片
  uni.getImageInfo({
    src: qrcode,
    success: (res) => {
      qrcodeLoadFailed.value = false;
      qrcodeRenderError.value = false;
      uni.$u.toast("二维码加载成功");

      // 成功后等待一会再预览
      setTimeout(() => {
        uni.previewImage({
          urls: [qrcode],
          current: 0
        });
      }, 500);
    },
    fail: (err) => {
      qrcodeLoadFailed.value = true;
      qrcodeRenderError.value = true;
      // 移除二维码加载失败提示，避免误导用户
      // uni.$u.toast("二维码加载失败，请检查网络");
    },
    complete: () => {
      qrcodeLoading.value = false;
    }
  });
};

// 检查URL是否有效
const isValidUrl = (url) => {
  // 直接返回true，不验证URL格式
  return !!url;
};

// 确保在组件初始化时进行状态重置
onMounted(() => {
  // 初始化状态
  resetQRCodeState();
});

// 返回按钮功能
const goBack = () => {
  uni.navigateBack({
    delta: 1,
    fail: (err) => uni.reLaunch({ url: "/pages/index" }),
  });
};

// 查看用户信息
const viewUserProfile = () => {
  if (info.value.user?.uid) {
    navto(`/pages/bundle/msg/personage?uid=${info.value.user.uid}`);
  }
};

// {{ AURA-X: Add - 举报功能相关方法. Confirmed via 寸止. }}
// 打开举报页面
const openReportDialog = () => {
  const authParams = getAuthParams();
  if (!authParams) {
    uni.$u.toast('请先登录');
    return;
  }

  // 跳转到举报页面，传递活动ID
  navto(`/pages/bundle/report/index?activity_id=${huodong_id.value}`);
};

// 查看报名用户信息
const viewEnrolledUserProfile = (user) => {
  if (user?.uid) {
    navto(`/pages/bundle/msg/personage?uid=${user.uid}`);
  }
};

// 修改活动
const editActivity = () => {
  navto(`/pages/bundle/index/addActive?huodong_id=${huodong_id.value}`);
};

// 取消活动
const cancelActivity = () => {
  uni.showModal({
    title: '确认取消活动',
    content: '取消后活动将无法恢复，已报名用户将收到通知，确定要取消吗？',
    confirmText: '确定取消',
    confirmColor: '#dc3545',
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({ title: '正在取消活动...' });

          // 这里需要调用取消活动的API
          const result = await huodongcancel_huodong({ huodong_id: huodong_id.value });

          uni.hideLoading();

          if (result.status === 'ok') {
            uni.showToast({ title: '活动已取消', icon: 'success' });
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          } else {
            uni.showToast({ title: result.msg || '取消失败', icon: 'none' });
          }
        } catch (error) {
          uni.hideLoading();
          console.error('取消活动失败:', error);
          uni.showToast({ title: '网络错误，请重试', icon: 'none' });
        }
      }
    }
  });
};

</script>
<template>
  <view class="page">
    <!-- 加载中状态显示 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-animation">
        <view class="loading-circle"></view>
        <view class="loading-circle"></view>
        <view class="loading-circle"></view>
      </view>
      <text class="loading-text">正在加载活动信息...</text>
    </view>

    <view v-else>
      <!-- 固定位置的返回按钮 -->
      <view class="fixed-back-btn" :class="{ 'dark': backButtonDark }" @click="goBack">
        <u-icon name="arrow-left" :color="backButtonDark ? '#333' : '#fff'" size="60rpx"></u-icon>
      </view>

      <!-- 自定义封面图容器 - 简化实现确保显示 -->
      <view class="activity-cover-container" @click="previewCoverImage">
        <image
            class="activity-cover-image"
            :src="info.img_url"
            mode="scaleToFill"
        ></image>
      </view>
      <view class="mt20 px30">
        <!-- 头像名称 -->
        <!-- <view class="pr df aie mb40" :style="{ zIndex: scrollTop > 10 ? '1' : '9999' }"> -->
        <view class="df">
          <!-- <view class="pa"> -->
          <!-- 修改后头像区域 -->
          <view class="user-header df aic mb40 title-card" @click="viewUserProfile">
            <u-avatar
                size="140rpx"
                mode="aspectFill"
                :src="info.user?.avatar"
                borderColor="#fff"
                :borderWidth="rpxValue"
            ></u-avatar>
            <view class="ml30">
              <u-text
                  size="32rpx"
                  bold
                  color="#333"
                  :text="info.user?.nickname"
              ></u-text>
              <u-text
                  size="24rpx"
                  color="#666"
                  :text="info.name"
                  class="activity-name"
              ></u-text>
            </view>
          </view>
        </view>

        <!-- 在活动详情区域中添加二维码按钮 - 移动到这里 -->
        <view v-if="showQrCodeButton" class="qrcode-buttons-container mb40">
          <view class="section-title mb20">
            <u-text text="活动联系方式" size="32rpx" bold color="#333"></u-text>
          </view>
          <view class="df aic jcsb">

            <u-button
                text="查看活动群二维码"
                color="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
                shape="circle"
                :customStyle="{
                color: '#fff',
                fontWeight: 'bold',
                fontSize: '24rpx',
                height: '70rpx',
                width: '45%'
              }"
                @click="showQrCodes"
            ></u-button>
          </view>
        </view>

        <view class="bgColor p25 mb40 r50p info-card">
          <view class="df aic jcsb">
            <view class="activity-status-tag"
                  :class="{
                  'status-registration': getActivityStatus() === 'registration',
                  'status-ongoing': getActivityStatus() === 'ongoing',
                  'status-ended': getActivityStatus() === 'ended'
                }"
            >
              {{
                getActivityStatus() === 'registration' ? '报名中' :
                getActivityStatus() === 'ongoing' ? '进行中' : '已结束'
              }}
            </view>
          </view>
          <view class="itemText item-row">
            <view class="item-label">
              <view class="df aic">
                <u-icon name="account-fill" color="#FF6B35" size="32rpx" style="margin-right: 10rpx;"></u-icon>
                <u-text
                    bold
                    margin="0 10rpx 0 0"
                    color="#000"
                    size="28rpx"
                    text="活动人数："
                ></u-text>
              </view>
            </view>
            <view class="item-content">
              <u-text
                  color="#000"
                  bold
                  size="28rpx"
                  :text="`${info.num || 0}人`"
              ></u-text>
            </view>
          </view>
          <view class="itemText item-row">
            <view class="item-label">
              <view class="df aic">
                <u-icon name="clock-fill" color="#6AC086" size="32rpx" style="margin-right: 10rpx;"></u-icon>
                <u-text
                    bold
                    margin="0 10rpx 0 0"
                    color="#000"
                    size="28rpx"
                    text="活动时间： "
                ></u-text>
              </view>
            </view>
            <view class="item-content">
              <u-text
                  color="#000"
                  bold
                  size="28rpx"
                  :text="info.start_time || '待定'"
              ></u-text>
            </view>
          </view>
          <view class="itemText item-row">
            <view class="item-label">
              <view class="df aic">
                <u-icon name="calendar-fill" color="#FF9500" size="32rpx" style="margin-right: 10rpx;"></u-icon>
                <u-text
                    bold
                    margin="0 10rpx 0 0"
                    color="#000"
                    size="28rpx"
                    text="报名截止："
                ></u-text>
              </view>
            </view>
            <view class="item-content">
              <u-text
                  color="#000"
                  bold
                  size="28rpx"
                  :text="info.baoming_end_time || info.start_time"
              ></u-text>
            </view>
          </view>
          <!-- 仅会员可报名标识 -->
          <view class="itemText item-row" v-if="info.member_only == 1">
            <view class="item-label">
              <view class="df aic">
                <u-icon name="star-fill" color="#FFD700" size="32rpx" style="margin-right: 10rpx;"></u-icon>
                <u-text
                    bold
                    margin="0 10rpx 0 0"
                    color="#000"
                    size="28rpx"
                    text="参与权限："
                ></u-text>
              </view>
            </view>
            <view class="item-content">
              <u-text
                  color="#FFD700"
                  bold
                  size="28rpx"
                  text="仅会员可报名"
              ></u-text>
            </view>
          </view>
          <view class="itemText item-row" v-if="info.is_online">
            <view class="item-label">
              <view class="df aic">
                <u-icon name="wifi" color="#4caf50" size="32rpx" style="margin-right: 10rpx;"></u-icon>
                <u-text
                    bold
                    margin="0 10rpx 0 0"
                    color="#000"
                    size="28rpx"
                    text="活动方式："
                ></u-text>
              </view>
            </view>
            <view class="item-content">
              <view class="online-tag">
                <u-text
                    color="#000"
                    bold
                    size="28rpx"
                    text="线上活动"
                ></u-text>
              </view>
            </view>
          </view>
          <view class="itemText item-row" v-if="!info.is_online">
            <view class="item-label">
              <view class="df aic">
                <u-icon name="map-fill" color="#007AFF" size="32rpx" style="margin-right: 10rpx;"></u-icon>
                <u-text
                    bold
                    margin="0 10rpx 0 0"
                    color="#000"
                    size="28rpx"
                    text="活动地点："
                ></u-text>
              </view>
            </view>
            <view class="item-content">
              <u-text
                  suffix-icon="map-fill"
                  color="#000"
                  bold
                  size="28rpx"
                  :text="`${info.sheng}-${info.shi}-${info.qu}-${info.addr}`"
                  @click="openMap"
              ></u-text>
            </view>
          </view>
          <!-- 活动费用 -->
          <view class="itemText item-row">
            <view class="item-label">
              <view class="df aic">
                <u-icon name="rmb-circle-fill" color="#FF3B30" size="32rpx" style="margin-right: 10rpx;"></u-icon>
                <u-text
                    bold
                    margin="0 10rpx 0 0"
                    color="#000"
                    size="28rpx"
                    text="活动费用："
                ></u-text>
              </view>
            </view>
            <view class="item-content">
              <view class="fee-info">
                <!-- 当普通价格和会员价格都为0时显示免费 -->
                <u-text
                    v-if="parseFloat(info.money || 0) === 0 && parseFloat(info.member_money || 0) === 0"
                    color="#000"
                    bold
                    size="28rpx"
                    text="免费"
                ></u-text>
                <!-- 当普通价格和会员价格相同时只显示一个价格 -->
                <u-text
                    v-else-if="parseFloat(info.money || 0) === parseFloat(info.member_money || 0)"
                    color="#000"
                    bold
                    size="28rpx"
                    :text="`¥${info.money || 0}`"
                ></u-text>
                <!-- 当普通价格和会员价格不同时显示格式化的价格 -->
                <u-text
                    v-else
                    color="#000"
                    bold
                    size="28rpx"
                    :text="`普通用户¥${info.money || 0}/会员¥${info.member_money || 0}`"
                ></u-text>
              </view>
            </view>
          </view>
          <u-line direction="row" color="rgba(106, 192, 134, 0.2)" margin="20rpx 0 24rpx"></u-line>
          <myStitle
              color="#333"
              blod
              rTitle="查看报名"
              icon="arrow-down-fill"
              :icon-style="{
              top: '4rpx',
              marginLeft: '10rpx',
              color: '#333',
              fontSize: '16rpx'
            }"
              rSize="26rpx"
              @click="
              popupShow = true;
              type = 1;
              getMescroll().resetUpScroll(true);
            "
          >
            <view class="df aic f1">
              <view v-for="(val, i) in info.peopleList" :key="i">
                <view
                    class="mr10 enrolled-avatar"
                    v-if="i < 5"
                    @click="viewEnrolledUserProfile(val?.user)"
                    style="cursor: pointer;"
                >
                  <u-avatar
                      size="60rpx"
                      mode="aspectFill"
                      :src="val?.user?.avatar"
                  ></u-avatar>
                </view>
              </view>
              <view v-if="info.peopleList?.length > 5" class="more-enrollments">
                <u-text text="..." color="#666" size="30rpx"></u-text>
              </view>
            </view>
          </myStitle>
        </view>

        <!-- {{ AURA-X: Add - 退款政策卡片. Confirmed via 寸止. }} -->
        <view v-if="shouldShowRefundPolicy" class="bgColor p25 mb40 r50p info-card">
          <myLine
              w="6"
              h="28"
              bg="#FF6B35"
              c="#333333"
              title="退款政策"
              size="32"
              blod
              margin="0 0 30rpx 0"
          ></myLine>

          <!-- {{ AURA-X: Modify - 根据支付方式显示不同的退款政策内容. Confirmed via 寸止. }} -->
          <!-- 线下收款活动的退款政策 -->
          <template v-if="isOfflinePayment">
            <view class="refund-rule-title">
              <u-text
                  text="线下收款退款政策"
                  size="28rpx"
                  color="#FF6B35"
                  bold
                  margin="0 0 20rpx 0"
              ></u-text>
            </view>

            <view class="offline-refund-notice">
              <u-icon name="info-circle-fill" color="#FF9500" size="28rpx" style="margin-right: 8rpx;"></u-icon>
              <u-text
                  text="发布方选择线下付款，退款请与发布方协商"
                  size="26rpx"
                  color="#333333"
              ></u-text>
            </view>
          </template>

          <!-- 线上支付活动的退款政策 -->
          <template v-else>
            <view class="refund-rule-title">
              <u-text
                  :text="getRefundRuleTitle"
                  size="28rpx"
                  color="#FF6B35"
                  bold
                  margin="0 0 20rpx 0"
              ></u-text>
            </view>

            <!-- 退款政策表格 -->
            <view class="refund-policy-table">
              <view class="table-header">
                <view class="table-cell header-cell">申请退款时间</view>
                <view class="table-cell header-cell">退款比例</view>
              </view>
              <view
                  v-for="(policy, index) in getRefundPolicyDetails"
                  :key="index"
                  class="table-row"
                  :class="{ 'last-row': index === getRefundPolicyDetails.length - 1 }"
              >
                <view class="table-cell">{{ policy.time }}</view>
                <view class="table-cell refund-amount">{{ policy.refund }}</view>
              </view>
            </view>

            <!-- 温馨提示 -->
            <view class="refund-notice">
              <u-icon name="info-circle-fill" color="#FF9500" size="28rpx" style="margin-right: 8rpx;"></u-icon>
              <u-text
                  text="退款将在3-7个工作日内原路返回"
                  size="24rpx"
                  color="#666666"
              ></u-text>
            </view>
          </template>
        </view>

        <!-- {{ AURA-X: Modify - 修复活动介绍显示逻辑，让所有用户都能看到活动介绍内容. Confirmed via 寸止. }} -->
        <view v-if="info.contents && info.contents.trim()" class="bgColor p25 mb40 r50p info-card">
          <myLine
              w="6"
              h="28"
              bg="#333333"
              c="#333333"
              title="活动介绍"
              size="32"
          ></myLine>
          <u-gap height="20rpx"></u-gap>
          <view class="activity-content">
            <!-- {{ AURA-X: Modify - 修复HTML实体解码函数调用错误. Confirmed via 寸止. }} -->
            <u-parse :content="info.contents?.replaceAll(' ', '&nbsp;')"></u-parse>
          </view>
          <u-gap height="20rpx"></u-gap>
          <template v-for="(val, i) in info.imgs" :key="i">
            <image
                class="activity-intro-image"
                :src="val"
                mode="widthFix"
                lazy-load
                show-menu-by-longpress
                @click="openImage(i)"
            ></image>
            <u-gap height="20rpx"></u-gap>
          </template>
        </view>

        <!-- {{ AURA-X: Add - 添加往期图片卡片. Confirmed via 寸止. }} -->
        <!-- 往期图片 -->
        <view v-if="activityPhotos.length > 0" class="bgColor p25 mb40 r50p info-card">
          <myStitle
              title="往期图片"
              size="28rpx"
              color="#000"
              blod
              rTitle="查看全部"
              icon="arrow-down-fill"
              :icon-style="{
                top: '4rpx',
                marginLeft: '10rpx',
                color: '#000',
                fontSize: '16rpx',
                transform: 'rotate(-90deg)'
              }"
              rSize="20rpx"
              @click="goToActivityAlbum"
          ></myStitle>
          <u-gap height="30rpx"></u-gap>

          <!-- 图片网格预览 -->
          <view class="photo-preview-grid">
            <view
              class="photo-preview-item"
              v-for="(photo, index) in displayPhotos"
              :key="photo.id"
              @click="previewActivityPhotos(index)"
            >
              <image
                :src="photo.photo_url"
                mode="aspectFill"
                class="photo-preview-image"
                lazy-load
              ></image>

              <!-- 更多图片遮罩 -->
              <view v-if="index === 5 && activityPhotos.length > 6" class="more-photos-overlay">
                <text class="more-photos-text">+{{ activityPhotos.length - 6 }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 提问 -->
        <view class="bgColor p25 mb40 r50p info-card">
          <myStitle
              :title="`活动提问${info.pingjiaCount}`"
              size="28rpx"
              color="#000"
              blod
              rTitle="全部提问"
              icon="arrow-down-fill"
              :icon-style="{
            top: '4rpx',
            marginLeft: '10rpx',
            color: '#000',
            fontSize: '16rpx',
            transform: 'rotate(-90deg)'
          }"
              rSize="20rpx"
              @click="
            popupShow = true;
            type = 2;
            getMescroll().resetUpScroll(true);
          "
          ></myStitle>
          <u-gap height="30rpx"></u-gap>
          <view
              class="problemItem mb40"
              v-for="(val, i) in info.pingjiaList"
              :key="i"
          >
            <template v-if="i < 3">
              <view class="df aic top mb10">
                <!-- <view class="left mr30 x28 fb tac c6f r10"> A </view> -->
                <u-avatar
                    size="80rpx"
                    mode="aspectFill"
                    :src="val.user?.avatar"
                ></u-avatar>
                <view class="ml20">
                  <u-text
                      margin="0 10rpx 0"
                      :text="val.user?.nickname"
                      color="#000"
                      bold
                      size="24rpx"
                  ></u-text>
                  <!-- {{ AURA-X: Modify - 修复提问内容HTML标签显示问题，使用u-parse解析. Confirmed via 寸止. }} -->
                  <view class="question-content" style="margin: 0 10rpx 0; font-size: 24rpx; color: #000;">
                    <u-parse :content="val.contents"></u-parse>
                  </view>
                  <u-text
                      margin="0 10rpx 0"
                      :text="val.time"
                      color="#000"
                      size="20rpx"
                  ></u-text>
                </view>
              </view>

              <!-- 回答内容显示 -->
              <view v-if="val.is_replied && val.reply_content" class="reply-section ml100 mt20">
                <view class="reply-header">
                  <u-text text="活动发布者回答：" color="#6AC086" size="22rpx" bold></u-text>
                </view>
                <view class="reply-content mt10">
                  <u-text :text="val.reply_content" color="#333" size="24rpx"></u-text>
                </view>
                <view class="reply-time mt10">
                  <u-text :text="val.reply_time" color="#999" size="20rpx"></u-text>
                </view>
              </view>

              <!-- 回答按钮（仅活动发布者可见且未回答的提问） -->
              <view v-if="isActivityCreator && !val.is_replied" class="reply-button-section ml100 mt20">
                <u-button
                    color="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
                    text="回答"
                    size="mini"
                    :customStyle="{
                      color: '#fff',
                      fontWeight: 'bold',
                      borderRadius: '20rpx',
                      fontSize: '22rpx',
                      width: '120rpx',
                      height: '60rpx'
                    }"
                    @click="showReplyDialog(val)"
                ></u-button>
              </view>
            </template>
          </view>
          <u-button
              color="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
              text="提问"
              :customStyle="{
            color: '#fff',
            fontWeight: 'bold',
            borderRadius: '30rpx',
            fontSize: '26rpx'
          }"
              @click="
            popupShows = true;
            iptType = 'pingjia';
          "
          ></u-button>
        </view>
      </view>
      <u-gap height="60rpx"></u-gap>
      <!-- 底部活动发起人信息 -->
      <view
          class="p30 w organizer-card"
          style="
          border-radius: 70rpx 0rpx 0rpx 0rpx;
          background: linear-gradient(142deg, #88D7A0 0%, #6AC086 50%, #FFFFFF 100%);
        "
      >
        <view class="df aic mb30">
          <u-avatar
              :src="info.user?.avatar"
              mode="aspectFill"
              size="120rpx"
          ></u-avatar>
          <view class="ml20">
            <u-text
                :text="`活动发布者：${info.lianxi_name}`"
                size="30rpx"
                :bold="true"
            ></u-text>
          </view>
        </view>
        <view
            class="mb30 p30 x26 signature-box"
        >
          {{ store().$state.userInfo.gexingqianming }}
        </view>
        <view class="df aic">
          <u-text
              :text="`${info.baoming_num}人已报名`"
              color="#333"
              size="24rpx"
              suffix-icon="arrow-down-fill"
              :icon-style="{
              top: '2rpx',
              fontSize: '20rpx',
              color: '#333',
              transform: 'rotate(-90deg)'
            }"
              @click="
              {
                popupShow = true;
                type = 1;
                getMescroll().resetUpScroll(true);
              }
            "
          ></u-text>
          <u-text
              align="right"
              color="#333"
              size="24rpx"
              prefix-icon="info-circle-fill"
              :icon-style="{
              top: '2rpx',
              fontSize: '30rpx',
              color: '#333'
            }"
              text="报名须知"
              @click="navto('/pages/bundle/common/xieyi?type=2')"
          ></u-text>
        </view>
        <view class="mt40 df aic w">
          <view
              :class="i === 4 ? '' : 'mr50'"
              v-for="(val, i) in info.peopleList"
              :key="i"
              @click="viewEnrolledUserProfile(val.user)"
              style="cursor: pointer;"
          >
            <template v-if="i <= 4">
              <u-avatar
                  size="98rpx"
                  mode="aspectFill"
                  :src="val.user.avatar"
              ></u-avatar>
              <u-text
                  margin="16rpx 0 0"
                  align="center"
                  size="18rpx"
                  :text="val.user.nickname"
              ></u-text>
            </template>
          </view>
        </view>

        <!-- {{ AURA-X: Add - 举报链接（页面底部）. Confirmed via 寸止. }} -->
        <view class="report-section" v-if="canShowReportLink">
          <view class="report-link" @click="openReportDialog">
            <u-text text="投诉/举报" size="28rpx" color="#007AFF" :bold="false"></u-text>
          </view>
        </view>

        <u-gap height="40" bg-color=""></u-gap>
        <u-safe-bottom></u-safe-bottom>
      </view>
      <!-- 底部功能栏 -->
      <view class="pfx bottom0 bottomBox r20" style="left: 20rpx; right: 20rpx; width: auto;">
        <view class="df aic jcsb" style="width: 100%; overflow: hidden;">
          <!-- 左侧按钮组 -->
          <view class="df aic">
            <button
                class="u-reset-button action-btn"
                type=""
                @click="sharePopup = true"
            >
              <u-icon
                name="share-fill"
                size="48rpx"
                color="#6AC086"
              ></u-icon>
            </button>
            <button class="u-reset-button action-btn ml20" type="" @click="collect">
              <u-icon
                  color="red"
                  :name="info.is_shoucang ? 'star-fill' : 'star'"
                  size="60rpx"
              ></u-icon>
            </button>
            <!-- {{ AURA-X: Modify - 只有启用签到且已报名的用户才显示签到按钮. Confirmed via 寸止. }} -->
            <button
                v-if="info.enable_checkin && info.enable_checkin !== '0' && info.baoming_order && info.baoming_order.status === 1"
                class="u-reset-button action-btn ml20"
                type=""
                @click="handleCheckin"
                :disabled="checkinStatus.disabled"
            >
              <u-icon
                  :name="checkinStatus.icon"
                  :color="checkinStatus.color"
                  size="60rpx"
              ></u-icon>
            </button>
          </view>

          <!-- 右侧按钮区域 -->
          <view class="register-btn-container">
            <!-- 非发布者显示报名按钮 -->
            <u-button
                v-if="info.user?.uid != store().$state.userInfo.uid"
                :text="getBtnText()"
                color="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
                :disabled="btnDisabled"
                :customStyle="{
                width: '200rpx',
                height: '80rpx',
                color: '#fff',
                fontSize: '30rpx',
                fontWeight: 'bold',
                borderRadius: '40rpx'
              }"
                @click="submitActive"
            ></u-button>

            <!-- 发布者显示管理按钮 历史活动不显示管理按钮 -->
            <view v-else class="creator-buttons">
              <!-- 只有非历史活动才显示管理按钮 -->
              <template v-if="canManageActivity()">
                <!-- {{ AURA-X: Modify - 进一步缩小按钮宽度，确保自适应屏幕. Confirmed via 寸止. }} -->
                <u-button
                    text="取消活动"
                    color="linear-gradient(135deg, #dc3545 0%, #c82333 100%)"
                    :customStyle="{
                    width: '120rpx',
                    height: '65rpx',
                    color: '#fff',
                    fontSize: '22rpx',
                    fontWeight: '500',
                    borderRadius: '32rpx',
                    boxShadow: '0 4rpx 12rpx rgba(220, 53, 69, 0.25)'
                  }"
                    @click="cancelActivity"
                ></u-button>
                <u-button
                    text="修改活动"
                    color="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
                    :customStyle="{
                    width: '120rpx',
                    height: '65rpx',
                    color: '#fff',
                    fontSize: '22rpx',
                    fontWeight: '500',
                    borderRadius: '32rpx',
                    boxShadow: '0 4rpx 12rpx rgba(106, 192, 134, 0.25)'
                  }"
                    @click="editActivity"
                ></u-button>
              </template>
              <!-- 历史活动显示提示信息 -->
              <view v-else class="history-activity-tip">
                <u-text text="活动已结束" color="#999" size="26rpx"></u-text>
              </view>
            </view>
          </view>
        </view>
        <u-safe-bottom></u-safe-bottom>
      </view>
      <!-- 列表弹窗 -->
      <u-popup
          :show="popupShow"
          :close-on-click-overlay="true"
          round="30rpx"
          :safe-area-inset-bottom="true"
          @close="popupShow = false"
          :bg-color="type === 1 ? '#f7f7f7' : '#333333'"
      >
        <view class="pt50" @touchmove.stop.prevent="() => {}">
          <mescroll-uni
              class="list"
              :up="{
              page: {
                num: 0,
                size: 20,
                time: null
              }
            }"
              @init="mescrollInit"
              @down="downCallback"
              @up="upCallback"
              @topclick="$event.scrollTo(0)"
              height="1000rpx"
          >
            <view class="px30">
              <!-- 报名列表 -->
              <template v-if="type === 1">
                <view
                    class="mb30 p20 df b6f r20"
                    v-for="(val, i) in goods"
                    :key="i"
                >
                  <u-avatar
                      size="80rpx"
                      :src="val.user?.avatar"
                      mode="aspectFill"
                      @click="viewEnrolledUserProfile(val.user)"
                      style="cursor: pointer;"
                  ></u-avatar>
                  <view class="df fdc jcsb">
                    <view class="df aic">
                      <u-text
                          :suffix-icon="
                          val.lianxi_sex
                            ? val.lianxi_sex == 1
                              ? 'man'
                              : 'woman'
                            : val.user?.sex == 0
                            ? '未知'
                            : val.user?.sex == 1
                            ? 'man'
                            : 'woman'
                        "
                          :iconStyle="{
                          marginLeft: '10rpx',
                          fontSize: '26rpx',
                          color: val.lianxi_sex
                            ? val.lianxi_sex == 1
                              ? '#5aa0ee'
                              : '#ec7a9e'
                            : val.user?.sex == 0
                            ? '未知'
                            : val.user?.sex == 1
                            ? '#5aa0ee'
                            : '#ec7a9e'
                        }"
                          margin="0 10rpx 0"
                          :text="val.lianxi_name || val.user?.nickname"
                          color="#000"
                          bold
                          size="28rpx"
                      ></u-text>
                      <u-text
                          v-if="val.lianxi_mobile"
                          margin="0 10rpx 0"
                          :text="val.lianxi_mobile"
                          color="#000"
                          bold
                          size="28rpx"
                          @click="callPhone(val.lianxi_mobile)"
                      ></u-text>
                    </view>
                    <u-text
                        margin="0 10rpx 0"
                        :text="val.pay_time"
                        color="#aaa"
                        size="20rpx"
                    ></u-text>
                  </view>
                </view>
              </template>
              <!-- 提问列表 -->
              <template v-else-if="type === 2">
                <view class="problemItem mb40" v-for="(val, i) in goods" :key="i">
                  <view class="df aic top mb10">
                    <u-avatar
                        size="50rpx"
                        :src="val.user?.avatar"
                        mode="aspectFill"
                    ></u-avatar>
                    <view>
                      <u-text
                          margin="0 10rpx 0"
                          :text="val.user?.nickname"
                          color="#fff"
                          bold
                          size="24rpx"
                      ></u-text>
                      <!-- {{ AURA-X: Modify - 修复提问内容HTML标签显示问题，使用u-parse解析. Confirmed via 寸止. }} -->
                      <view class="question-content-popup" style="margin: 0 10rpx 0; font-size: 24rpx; color: #fff;">
                        <u-parse :content="val.contents"></u-parse>
                      </view>
                      <u-text
                          margin="0 10rpx 0"
                          :text="val.time"
                          color="#aaa"
                          size="20rpx"
                      ></u-text>
                    </view>
                  </view>
                </view>
              </template>
            </view>
          </mescroll-uni>
        </view>
      </u-popup>
      <!-- 输入框弹窗 -->
      <u-popup
          :show="popupShows"
          :close-on-click-overlay="true"
          round="30rpx"
          mode="center"
          :safe-area-inset-bottom="false"
          @close="popupShows = false"
      >
        <view class="pt50 pb50 px30 w690 r30 register-popup">
          <template v-if="iptType != 'erweima'">
            <view class="mb30 df aic jcc fb x34 popup-title">
              {{ iptType === "pingjia" ? "提问输入框" : "报名信息输入框" }}
            </view>
            <template v-if="iptType === 'pingjia'">
              <!-- 修改为textarea，增加高度三倍 -->
              <u-textarea
                  :adjust-position="false"
                  border="surround"
                  placeholder="请输入您的提问..."
                  v-model="content"
                  :height="240"
                  :maxlength="500"
                  :show-confirm-bar="false"
                  :customStyle="{
                    borderColor: '#6AC086',
                    borderRadius: '20rpx',
                    padding: '10rpx 20rpx'
                  }"
              ></u-textarea>
            </template>
            <template v-else-if="iptType === 'xinxi'">
              <view class="input-group">
                <view class="input-label">确认报名</view>
                <u-text
                    text="点击确认即可完成报名，系统将自动使用您的账户信息"
                    color="#666"
                    size="28rpx"
                    align="center"
                ></u-text>
              </view>
            </template>
            <u-gap height="50rpx"></u-gap>
            <!-- {{ AURA-X: Modify - 修复提问弹窗按钮文本，根据类型显示不同文本. Confirmed via 寸止. }} -->
            <u-button
                shape="circle"
                color="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
                :customStyle="{
                color: '#fff',
                fontWeight: 'bold',
                fontSize: '30rpx',
                height: '90rpx'
              }"
                :text="iptType === 'pingjia' ? '提交' : '确认报名'"
                @click="submit"
            ></u-button>
          </template>
          <template v-else>
            <view v-if="!qrcodePopup">
              <view class="mb30 df aic jcc fb x34 popup-title">
                活动发起人联系方式
              </view>
              <view class="df aic jcc mb20">
                <!-- 联系人二维码 - 更可靠的实现 -->
                <view class="lianxi_qrcode-image-wrapper">
                  <image class="lianxi_qrcode-image"
                         v-if="info.lianxi_qrcode"
                         :src="info.lianxi_qrcode.indexOf('http') === -1 ? store().$state.url + info.lianxi_qrcode : info.lianxi_qrcode"
                         mode="aspectFix"
                         width="400rpx" height="700rpx"
                         show-menu-by-longpress="1"
                  ></image>
                </view>
              </view>
              <u-text
                  text="长按识别二维码或保存图片，以便联系活动发起人"
                  align="center"
                  bold
                  color="#333"
                  size="28rpx"
              ></u-text>
              <u-gap height="40rpx"></u-gap>
            </view>
            <view v-else-if="qrcodePopup">
              <view class="mb30 df aic jcc fb x34 popup-title">
                活动群二维码
              </view>
              <view class="df aic jcc mb20">

                <!-- 联系人二维码 - 更可靠的实现 -->
                <view class="lianxi_qrcode-image-wrapper">
                  <image class="lianxi_qrcode-image"
                         v-if="info.qun_qrcode"
                         :src="info.qun_qrcode.indexOf('http') === -1 ? store().$state.url + info.qun_qrcode : info.qun_qrcode"
                         mode="aspectFix"
                         width="400rpx"
                         height="700rpx"
                         show-menu-by-longpress="1"
                  ></image>

                </view>
              </view>
              <u-text
                  text="长按识别二维码或保存图片，以便进群获取详细信息"
                  align="center"
                  bold
                  color="#333"
                  size="28rpx"
              ></u-text>

              <u-gap height="40rpx"></u-gap>
              <u-button
                  text="查看联系人二维码"
                  color="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
                  shape="circle"
                  :customStyle="{
                    color: '#fff',
                    fontWeight: 'bold',
                    fontSize: '24rpx',
                    height: '70rpx',
                    width: '45%'
                  }"
                  @click="showContactQrCode"
              ></u-button>
            </view>

          </template>
        </view>
      </u-popup>

      <u-popup
          :show="sharePopup"
          :close-on-click-overlay="true"
          round="30rpx"
          :safe-area-inset-bottom="true"
          @close="sharePopup = false"
      >
        <view class="df aic pt50 pb50 px30 w690 r30">
          <view class="f1">
            <button class="u-reset-button" type="" open-type="share">
              <u-icon
                  label-pos="bottom"
                  space="20rpx"
                  size="88rpx"
                  name="weixin-circle-fill"
                  color="#17cd19"
                  label="分享页面"
              ></u-icon>
            </button>
          </view>
          <view class="f1">
            <u-icon
                label-pos="bottom"
                space="20rpx"
                size="88rpx"
                name="file-text-fill"
                color="#11bee9"
                label="复制文案"
                @click="copy(copyValue)"
            ></u-icon>
          </view>
        </view>
      </u-popup>

      <!-- 回答提问弹窗 -->
      <u-popup
          :show="replyPopupShow"
          :close-on-click-overlay="false"
          round="30rpx"
          :safe-area-inset-bottom="true"
          @close="replyPopupShow = false"
      >
        <view class="popup-container">
          <view class="popup-header">
            <text class="popup-title">回答提问</text>
            <u-icon name="close" size="20" @click="replyPopupShow = false"></u-icon>
          </view>

          <view class="question-content">
            <text class="question-label">提问内容：</text>
            <text class="question-text">{{ currentQuestion?.contents }}</text>
          </view>

          <view class="reply-input-section">
            <text class="reply-label">您的回答：</text>
            <u-textarea
                v-model="replyContent"
                placeholder="请输入您的回答..."
                :height="200"
                :maxlength="500"
                :show-confirm-bar="false"
                :customStyle="{
                  borderColor: '#6AC086',
                  borderRadius: '20rpx',
                  padding: '20rpx'
                }"
            ></u-textarea>
          </view>

          <view class="popup-buttons">
            <u-button
                text="取消"
                color="#f5f5f5"
                :customStyle="{
                  color: '#666',
                  fontWeight: 'bold',
                  borderRadius: '30rpx',
                  fontSize: '26rpx',
                  width: '45%'
                }"
                @click="replyPopupShow = false"
            ></u-button>
            <u-button
                text="提交回答"
                color="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
                :customStyle="{
                  color: '#fff',
                  fontWeight: 'bold',
                  borderRadius: '30rpx',
                  fontSize: '26rpx',
                  width: '45%'
                }"
                @click="submitReply"
            ></u-button>
          </view>
        </view>
      </u-popup>
      <!-- 非会员选择弹窗 -->
      <u-popup
          :show="membershipPopup"
          :close-on-click-overlay="true"
          round="30rpx"
          mode="center"
          @close="membershipPopup = false"
      >
        <view class="membership-popup px40 py70">
          <view class="mb40 text-center">
            <u-text text="您还不是会员" size="40rpx" bold color="#333"></u-text>
          </view>
          <view class="mb50 text-center">
            <u-text text="成为会员可享受更多权益与优惠" size="30rpx" color="#666" class="mb20"></u-text>
            <u-text text="是否立即加入会员？" size="30rpx" color="#666"></u-text>
          </view>
          <view class="buttons-container df fdc aic">
            <u-button
                text="单次报名(58元)"
                color="linear-gradient(135deg, #FFB6C1 0%, #EE82EE 100%)"
                shape="circle"
                :customStyle="{
                          marginBottom: '30rpx',
                          color: '#fff',
                          fontWeight: 'bold',
                          width: '70%'
                      }"
                @click="singleActivityPayment"
            ></u-button>
            <u-button
                text="成为会员"
                color="linear-gradient(135deg, #E6E6FA 0%, #DDA0DD 100%)"
                shape="circle"
                class="member-btn-animation"
                :customStyle="{
                          color: '#fff',
                          fontWeight: 'bold',
                          width: '70%'
                      }"
                @click="navigateToMembership"
            ></u-button>
          </view>
        </view>
      </u-popup>

      <!-- 统一的提示框组件 -->
      <u-popup v-model="showPopup" mode="center" width="80%" border-radius="16">
        <view class="popup-container">
          <view class="popup-title">{{ popupTitle }}</view>
          <view class="popup-content">{{ popupContent }}</view>
          <view class="popup-buttons">
            <u-button
                v-if="showCancelButton"
                text="取消"
                size="medium"
                @click="closePopup"
                :custom-style="{ margin: '0 10rpx' }"
            ></u-button>
            <u-button
                text="确定"
                type="primary"
                size="medium"
                @click="confirmPopup"
                color="linear-gradient(103deg, #EE82EE 0%, #EE82EE 100%)"
            ></u-button>
          </view>
        </view>
      </u-popup>


    </view>
  </view>
</template>

<style>
.page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #f8f9fb 100%);
  padding: 20rpx;
}

/* 活动封面图容器样式 - 最简化版本，确保显示 */
.activity-cover-container {
  width: 100%;
  height: 400rpx;
  margin: 10rpx 0;
  border-radius: 16rpx;
  overflow: hidden;
  background: #f5f5f5;
}

.activity-cover-image {
  width: 100%;
  height: 100%;
  display: block;
}



/* 固定位置返回按钮样式 */
.fixed-back-btn {
  position: fixed;
  top: 60rpx;
  left: 30rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
  z-index: 1000;
}

.fixed-back-btn.dark {
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.fixed-back-btn:active {
  transform: scale(0.95);
}

.fixed-back-btn.dark:active {
  background: rgba(255, 255, 255, 0.7);
}

/* 活动介绍图片样式 */
.activity-intro-image {
  width: 100%;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 标题卡片样式 */
.title-card {
  width: 100%;
  background: linear-gradient(135deg, rgba(136, 215, 160, 0.05) 0%, rgba(106, 192, 134, 0.1) 100%);
  border-radius: 30rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(106, 192, 134, 0.15);
  margin-bottom: 30rpx;
}

.activity-name {
  font-size: 30rpx;
  font-weight: 500;
  margin-top: 10rpx;
  color: #6AC086;
}

/* 活动信息卡片样式 */
.info-card {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  padding: 30rpx;
  border-radius: 30rpx;
}

.item-row {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  width: 100%;
}

.item-label {
  min-width: 140rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
}

.item-content {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.online-tag {
  display: inline-flex;
  align-items: center;
  background-color: rgba(76, 175, 80, 0.1);
  border-radius: 10rpx;
  padding: 4rpx 12rpx;
}

.activity-status-tag {
  display: inline-block;
  padding: 10rpx 30rpx;
  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);
  color: white;
  font-size: 24rpx;
  font-weight: bold;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
}

/* 不同活动状态的样式 */
.status-registration {
  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);
  box-shadow: 0 4rpx 12rpx rgba(106, 192, 134, 0.25);
}

.status-ongoing {
  background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
  box-shadow: 0 4rpx 12rpx rgba(255, 152, 0, 0.25);
}

.status-ended {
  background: linear-gradient(135deg, #999 0%, #666 100%);
  box-shadow: 0 4rpx 12rpx rgba(153, 153, 153, 0.25);
}

/* 历史活动提示样式 */
.history-activity-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  background: rgba(153, 153, 153, 0.1);
  border-radius: 35rpx;
  border: 2rpx solid rgba(153, 153, 153, 0.2);
}

.enrolled-avatar {
  position: relative;
  margin-right: 10rpx;
  transition: all 0.3s ease;
}

.enrolled-avatar:hover {
  transform: translateY(-5rpx);
}

.more-enrollments {
  margin-left: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 活动发起人卡片样式 */
.organizer-card {
  border-radius: 40rpx 0 0 0;
  box-shadow: 0 -10rpx 30rpx rgba(238, 130, 238, 0.1);
}

/* 会员选择弹窗样式 */
.membership-popup {
  width: 600rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
  padding: 60rpx 40rpx;
}

.membership-popup .text-center {
  margin-bottom: 60rpx;
}

.membership-popup .buttons-container {
  margin-top: 50rpx;
}

.membership-popup .u-button {
  margin-bottom: 40rpx;
  height: 90rpx;
  font-size: 32rpx;
}

/* 会员按钮动画 */
.member-btn-animation {
  animation: pulse 1.5s infinite alternate;
  position: relative;
}

.member-btn-animation::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 100rpx;
  box-shadow: 0 0 0 0 rgba(221, 160, 221, 0.7);
  animation: ripple 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.05);
  }
}

@keyframes ripple {
  0% {
    box-shadow: 0 0 0 0 rgba(221, 160, 221, 0.7);
  }
  70% {
    box-shadow: 0 0 0 15rpx rgba(221, 160, 221, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(221, 160, 221, 0);
  }
}

/* 二维码弹窗样式 */
.qr-code-popup {
  width: 600rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 联系人二维码弹窗样式 */
.contact-qrcode-popup {
  width: 600rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 10rpx 30rpx rgba(238, 130, 238, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 增强二维码图片点击效果 */
.qr-code-popup .u-image,
.contact-qrcode-popup .u-image {
  border: 1px solid rgba(238, 130, 238, 0.2);
  border-radius: 10rpx;
  transition: all 0.3s ease;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.05);
}

.qr-code-popup .u-image:active,
.contact-qrcode-popup .u-image:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
}

.text-center {
  text-align: center;
}

.buttons-container {
  margin-top: 30rpx;
}



/* {{ AURA-X: Modify - 优化底部功能栏，确保自适应不超出屏幕. Confirmed via 寸止. }} */
/* 底部功能栏美化 */
.bottomBox {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 40rpx 40rpx 0 0;
  padding: 30rpx 20rpx; /* 减少左右内边距 */
  box-sizing: border-box; /* 确保padding计算在内 */
}

/* {{ AURA-X: Modify - 优化发布者按钮布局，确保自适应不超出屏幕. Confirmed via 寸止. }} */
/* 发布者按钮容器 */
.creator-buttons {
  display: flex;
  align-items: center;
  justify-content: flex-end; /* 右对齐 */
  gap: 4rpx; /* 最小间距 */
  padding: 0; /* 移除内边距 */
  flex-shrink: 1; /* 允许收缩 */
  min-width: 0; /* 允许收缩到最小 */
}

/* 注册按钮容器 */
.register-btn-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 底部按钮样式 */
.action-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);
}

.register-btn-container {
  display: flex;
  align-items: center;
}

/* 用户头像区域样式 */
.user-header {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 30rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(238, 130, 238, 0.15);
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-header:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(238, 130, 238, 0.2);
}

/* 活动标题和内容区域美化 */
.bgColor {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.bgColor:hover {
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
  transform: translateY(-2rpx);
}

/* 按钮美化 */
.u-button {
  transition: all 0.3s ease;
}

.u-button:active {
  transform: scale(0.98);
}

.activity-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
  overflow: hidden;
}

.activity-header {
  position: relative;
  width: 100%;
}

.cover-image {
  width: 100%;
  height: 400rpx;
  object-fit: cover;
  border-radius: 20rpx 20rpx 0 0;
}

.activity-title {
  padding: 30rpx;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.info-section {
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.info-item {
  display: flex;
  align-items: center;
  gap: 10rpx;
  font-size: 28rpx;
  color: #666;
}

.info-icon {
  font-size: 32rpx;
  color: #ee82ee;
}

.description-section {
  padding: 30rpx;
  line-height: 1.6;
  font-size: 28rpx;
  color: #333;
}

.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
  backdrop-filter: blur(10px);
}

.action-button {
  flex: 1;
  margin: 0 10rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.primary-button {
  background: linear-gradient(135deg, #ee82ee 0%, #da70d6 100%);
  color: white;
}

.secondary-button {
  background: rgba(238, 130, 238, 0.1);
  color: #ee82ee;
}

.gallery-section {
  padding: 20rpx 30rpx;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  margin-top: 20rpx;
}

.gallery-image {
  width: 100%;
  height: 200rpx;
  border-radius: 10rpx;
  object-fit: cover;
}

.contact-section {
  padding: 30rpx;
  background: rgba(238, 130, 238, 0.05);
  border-radius: 15rpx;
  margin: 20rpx 30rpx;
}

.qr-code-container {
  display: flex;
  justify-content: space-around;
  margin-top: 20rpx;
}

.qr-code-item {
  text-align: center;
}

.qr-code-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 10rpx;
  margin-bottom: 10rpx;
}

.qr-code-label {
  font-size: 24rpx;
  color: #666;
}

/* 动画效果 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* 互动按钮样式 */
.interaction-buttons {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 30rpx;
}

.interaction-button {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  background: rgba(238, 130, 238, 0.1);
  color: #ee82ee;
  font-size: 24rpx;
}

.interaction-button.active {
  background: #ee82ee;
  color: white;
}

/* 活动状态标签 */
.status-tag {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
  background: rgba(238, 130, 238, 0.9);
  backdrop-filter: blur(5px);
}

/* 报名弹窗样式 */
.register-popup {
  background: #fff;
  border-radius: 30rpx;
}

.popup-title {
  font-size: 36rpx;
  color: #333;
  margin-bottom: 40rpx;
  position: relative;
}

.popup-title::after {
  content: '';
  position: absolute;
  bottom: -15rpx;
  left: 50%;
  width: 80rpx;
  height: 4rpx;
  background: linear-gradient(135deg, #EE82EE 0%, #FFB6C1 100%);
  transform: translateX(-50%);
  border-radius: 4rpx;
}

.input-group {
  margin-bottom: 10rpx;
}

.input-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: bold;
}

/* 移除底部个性签名背景和边框 */
.signature-box {
  background-color: rgba(255, 255, 255, 0.3);
  border: none;
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(238, 130, 238, 0.1);
}

/* 线上活动样式 */
.online-location {
  display: flex;
  align-items: center;
  color: #4caf50;
  padding: 4rpx 0;
}

/* 费用信息样式 */
.fee-info {
  display: flex;
  align-items: center;
}

.fee-details {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

/* {{ AURA-X: Add - 转发按钮图标样式. Confirmed via 寸止. }} */
.share-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  transition: transform 0.2s ease;
}

.share-icon-container:active {
  transform: scale(0.95);
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
  height: 100vh;
}

.loading-animation {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-circle {
  width: 18rpx;
  height: 18rpx;
  margin: 0 8rpx;
  border-radius: 50%;
  background: linear-gradient(90deg, #6AC086, #88D7A0);
  animation: bounce 1.4s infinite ease-in-out both;
}

.loading-circle:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-circle:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1.0);
  }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #6AC086; /* {{ AURA-X: Modify - 修改加载文本颜色为项目主色调绿色. Confirmed via 寸止. }} */
}

.popup-container {
  padding: 30rpx;
  background: #fff;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 20rpx;
}

.popup-content {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 30rpx;
  padding: 0 20rpx;
}

.popup-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 二维码按钮区域样式 */
.qrcode-buttons-container {
  margin: 20rpx 0;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(238, 130, 238, 0.15);
}

.section-title {
  text-align: center;
  margin-bottom: 20rpx;
}

/* 二维码弹窗样式 */
.contact-qrcode-popup {
  width: 600rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
}

.text-center {
  text-align: center;
}

.qrcode-image-container {
  position: relative;
  border-radius: 10rpx;
  overflow: hidden;
  margin: 0 auto;
  width: 400rpx;
  height: 400rpx;
}

.qrcode-tips {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 0;
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
}

/* {{ AURA-X: Modify - 修复HTML标签选择器，改为类选择器. Confirmed via 寸止. }} */
.qrcode-tips .qrcode-tips-text {
  margin-left: 8rpx;
}

.qrcode-retry {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.qrcode-retry .retry-text {
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #F56C6C;
}

.qrcode-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.qrcode-loading .loading-text {
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #EE82EE;
}

/* 自定义加载动画 */
.loading-spinner {
  width: 80rpx;
  height: 40rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.loading-bounce {
  width: 20rpx;
  height: 20rpx;
  background-color: #EE82EE;
  border-radius: 50%;
  animation: bounce 1.4s infinite ease-in-out both;
}

.loading-bounce:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-bounce:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1.0);
  }
}

/* 强化的二维码容器样式 */
.qrcode-image-container {
  position: relative;
  width: 400rpx;
  height: 400rpx;
  margin: 0 auto;
  border-radius: 10rpx;
  overflow: hidden;
}

.qrcode-box {
  width: 100%;
  height: 100%;
  background-color: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(238, 130, 238, 0.2);
  border-radius: 10rpx;
  overflow: hidden; /* 确保内容不溢出 */
  position: relative; /* 为内部绝对定位提供参考 */
}

.qrcode-img {
  width: 100%;
  height: 100%;
  display: block; /* 防止底部间隙 */
  object-fit: contain;
}

.lianxi_qrcode-image-wrapper {
  width: 400rpx;
  height: 700rpx;
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
  flex-shrink: 0; /* 防止图片容器被压缩 */
}

.lianxi_qrcode-image {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
}

/* 加载指示器 */
.qrcode-loading-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
}

.loading-dot {
  width: 16rpx;
  height: 16rpx;
  margin: 0 8rpx;
  background-color: #EE82EE;
  border-radius: 50%;
  animation: dot-pulse 1.5s infinite ease-in-out;
}

.loading-dot:nth-child(1) {
  animation-delay: 0s;
}

.loading-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes dot-pulse {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* {{ AURA-X: Add - 退款政策卡片样式. Confirmed via 寸止. }} */
.refund-rule-title {
  margin-bottom: 20rpx;
}

.refund-policy-table {
  border: 2rpx solid #E5E5E5;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.table-header {
  display: flex;
  background-color: #F8F9FA;
  border-bottom: 2rpx solid #E5E5E5;
}

.table-row {
  display: flex;
  border-bottom: 2rpx solid #E5E5E5;
}

.table-row.last-row {
  border-bottom: none;
}

.table-cell {
  flex: 1;
  padding: 24rpx 20rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333333;
}

.header-cell {
  font-weight: bold;
  color: #666666;
  background-color: #F8F9FA;
}

.refund-amount {
  color: #FF6B35;
  font-weight: bold;
}

.refund-notice {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #FFF9E6;
  border-radius: 12rpx;
  border-left: 6rpx solid #FF9500;
}

/* {{ AURA-X: Add - 线下收款退款政策样式. Confirmed via 寸止. }} */
.offline-refund-notice {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: #F8F9FA;
  border-radius: 12rpx;
  border-left: 6rpx solid #FF9500;
  border: 2rpx solid #E5E5E5;
}

/* {{ AURA-X: Add - 往期图片卡片样式. Confirmed via 寸止. }} */
/* 往期图片预览网格 */
.photo-preview-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.photo-preview-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 12rpx;
  overflow: hidden;
  background: #f5f5f5;
}

.photo-preview-image {
  width: 100%;
  height: 100%;
}

.more-photos-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
}

.more-photos-text {
  color: #fff;
  font-size: 28rpx;
  font-weight: 600;
}

/* 回答弹窗样式 */
.popup-container {
  padding: 40rpx 30rpx;
  width: 100%;
  box-sizing: border-box;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.question-content {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 20rpx;
}

.question-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.question-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.reply-input-section {
  margin-bottom: 40rpx;
}

.reply-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
  display: block;
}

.popup-buttons {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

/* 回答内容显示样式 */
.reply-section {
  background-color: #f0f8f4;
  padding: 20rpx;
  border-radius: 20rpx;
  border-left: 6rpx solid #6AC086;
}

.reply-header {
  margin-bottom: 10rpx;
}

.reply-content {
  margin-bottom: 10rpx;
}

.reply-time {
  text-align: right;
}

/* {{ AURA-X: Add - 举报链接样式. Confirmed via 寸止. }} */
.report-section {
  padding: 30rpx;
  text-align: center;
}

.report-link {
  display: inline-block;
  padding: 10rpx 20rpx;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.report-link:active {
  opacity: 0.7;
}

.reply-button-section {
  text-align: right;
}
</style>
