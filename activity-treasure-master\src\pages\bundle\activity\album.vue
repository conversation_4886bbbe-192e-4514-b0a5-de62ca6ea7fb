<template>
  <view class="album-page">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="nav-left" @click="goBack">
        <u-icon name="arrow-left" color="#333" size="20"></u-icon>
      </view>
      <view class="nav-title">活动相册</view>
      <view class="nav-right"></view>
    </view>
    
    <!-- 活动信息卡片 -->
    <view class="activity-info" v-if="activityInfo.name">
      <text class="activity-name">{{ activityInfo.name }}</text>
      <text class="photo-count">共{{ totalPhotos }}张图片</text>
    </view>
    
    <!-- 图片网格 -->
    <view class="photo-grid" v-if="photos.length > 0">
      <view 
        class="photo-item" 
        v-for="(photo, index) in photos" 
        :key="photo.id"
        @click="previewImage(index)"
        @longpress="showDeleteMenu(photo)"
      >
        <image 
          :src="photo.photo_url" 
          mode="aspectFill"
          class="photo-image"
          lazy-load
        ></image>
        
        <!-- 用户信息 -->
        <view class="photo-user">
          <image 
            :src="photo.user?.avatar || '/static/default-avatar.png'" 
            class="user-avatar"
            mode="aspectFill"
          ></image>
          <text class="user-name">{{ photo.user?.nickname || '匿名用户' }}</text>
        </view>
        
        <!-- 删除按钮（仅对有权限的用户显示） -->
        <view 
          v-if="canDeletePhoto(photo)" 
          class="delete-btn"
          @click.stop="confirmDelete(photo)"
        >
          <u-icon name="close-circle-fill" color="#ff4757" size="16"></u-icon>
        </view>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" v-else-if="!loading">
      <u-icon name="photo" color="#ccc" size="60"></u-icon>
      <text class="empty-text">暂无图片</text>
      <text class="empty-hint">活动开始后可以上传图片</text>
    </view>
    
    <!-- 加载状态 -->
    <view class="loading-state" v-if="loading">
      <u-loading-icon mode="flower"></u-loading-icon>
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 底部上传按钮 -->
    <view class="upload-fab" v-if="canUpload" @click="showUploadMenu">
      <u-icon name="plus" color="#fff" size="24"></u-icon>
    </view>
    
    <!-- 上传选择弹窗 -->
    <view v-if="showUploadDialog" class="upload-dialog-overlay" @click="closeUploadDialog">
      <view class="upload-dialog" @click.stop>
        <view class="upload-option" @click="chooseFromCamera">
          <u-icon name="camera" color="#6AC086" size="20"></u-icon>
          <text class="option-text">拍照</text>
        </view>
        <view class="upload-option" @click="chooseFromAlbum">
          <u-icon name="photo" color="#6AC086" size="20"></u-icon>
          <text class="option-text">从相册选择</text>
        </view>
        <view class="upload-cancel" @click="closeUploadDialog">
          <text class="cancel-text">取消</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { huodongget_activity_photos, huodongupload_activity_photo, huodongdelete_activity_photo, upload_img } from '@/api'
import { store } from '@/store/index.js'

// 页面数据
const activityId = ref(0)
const photos = ref([])
const activityInfo = ref({ name: '', publisher_uid: 0 }) // {{ AURA-X: Fix - 初始化activityInfo结构. Confirmed via 寸止. }}
const loading = ref(false)
const showUploadDialog = ref(false)
const totalPhotos = ref(0)
const userInfo = computed(() => store().$state.userInfo)

// 页面加载
onLoad((options) => {
  if (options.activity_id) {
    activityId.value = parseInt(options.activity_id)
    loadPhotos()
  } else {
    uni.showToast({
      title: '参数错误',
      icon: 'error'
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})

// 加载图片列表
const loadPhotos = async () => {
  try {
    loading.value = true
    const res = await huodongget_activity_photos({
      activity_id: activityId.value,
      page: 1,
      page_size: 100 // 一次加载所有图片
    })

    // {{ AURA-X: Modify - 修复空数据处理，避免报错. Confirmed via 寸止. }}
    if (res.status === 'ok') {
      photos.value = res.data || []
      totalPhotos.value = res.count || 0

      // 获取活动基本信息用于显示
      if (photos.value.length > 0) {
        // 可以从图片数据中获取活动信息，或者调用活动详情API
        activityInfo.value.name = `活动相册`; // 临时设置，实际应该获取真实活动名称
      }
    } else if (res.status === 'empty' || res === 'n' || !res.data) {
      // 处理空数据情况，不显示错误提示
      photos.value = []
      totalPhotos.value = 0
      console.log('活动相册暂无图片')
    } else {
      uni.showToast({
        title: res.msg || '加载失败',
        icon: 'error'
      })
    }
  } catch (error) {
    console.error('加载图片失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 预览图片
const previewImage = (index) => {
  const urls = photos.value.map(photo => photo.photo_url)
  uni.previewImage({
    urls: urls,
    current: index
  })
}

// 检查是否可以上传图片
const canUpload = computed(() => {
  // 需要用户登录且活动已开始
  return userInfo.value && userInfo.value.uid
})

// 检查是否可以删除图片
const canDeletePhoto = (photo) => {
  if (!userInfo.value || !userInfo.value.uid) return false
  
  // 图片上传者或活动发布者可以删除
  return photo.user_id === userInfo.value.uid || 
         activityInfo.value.publisher_uid === userInfo.value.uid
}

// 显示上传菜单
const showUploadMenu = () => {
  showUploadDialog.value = true
}

// 关闭上传弹窗
const closeUploadDialog = () => {
  showUploadDialog.value = false
}

// 从相机拍照
const chooseFromCamera = () => {
  closeUploadDialog()
  chooseImage('camera')
}

// 从相册选择
const chooseFromAlbum = () => {
  closeUploadDialog()
  chooseImage('album')
}

// 选择图片
const chooseImage = (sourceType) => {
  uni.chooseImage({
    count: 1,
    sourceType: [sourceType],
    success: (res) => {
      uploadImage(res.tempFilePaths[0])
    },
    fail: (error) => {
      console.error('选择图片失败:', error)
      uni.showToast({
        title: '选择图片失败',
        icon: 'error'
      })
    }
  })
}

// 上传图片
const uploadImage = async (tempFilePath) => {
  try {
    uni.showLoading({
      title: '上传中...'
    })

    // {{ AURA-X: Fix - 修复上传参数，使用正确的字段名. Confirmed via 寸止. }}
    // 1. 先上传图片文件到服务器
    const uploadRes = await upload_img(tempFilePath)

    if (uploadRes.status !== 'ok') {
      throw new Error(uploadRes.msg || '图片上传失败')
    }

    // 2. 调用活动相册API保存图片记录
    const res = await huodongupload_activity_photo({
      uid: userInfo.value.uid,
      token: userInfo.value.token,
      activity_id: activityId.value,
      photo_url: uploadRes.data // 修复：直接使用data字段，不是data.url
    })

    if (res.status === 'ok') {
      uni.showToast({
        title: '上传成功',
        icon: 'success'
      })
      // 重新加载图片列表
      loadPhotos()
    } else {
      uni.showToast({
        title: res.msg || '上传失败',
        icon: 'error'
      })
    }
  } catch (error) {
    console.error('上传图片失败:', error)
    uni.showToast({
      title: error.message || '上传失败',
      icon: 'error'
    })
  } finally {
    uni.hideLoading()
  }
}

// 确认删除图片
const confirmDelete = (photo) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这张图片吗？',
    success: (res) => {
      if (res.confirm) {
        deletePhoto(photo.id)
      }
    }
  })
}

// 删除图片
const deletePhoto = async (photoId) => {
  try {
    uni.showLoading({
      title: '删除中...'
    })
    
    const res = await huodongdelete_activity_photo({
      uid: userInfo.value.uid,
      token: userInfo.value.token,
      photo_id: photoId
    })
    
    if (res.status === 'ok') {
      uni.showToast({
        title: '删除成功',
        icon: 'success'
      })
      // 重新加载图片列表
      loadPhotos()
    } else {
      uni.showToast({
        title: res.msg || '删除失败',
        icon: 'error'
      })
    }
  } catch (error) {
    console.error('删除图片失败:', error)
    uni.showToast({
      title: '删除失败',
      icon: 'error'
    })
  } finally {
    uni.hideLoading()
  }
}

// 显示删除菜单（长按）
const showDeleteMenu = (photo) => {
  if (!canDeletePhoto(photo)) return
  
  uni.showActionSheet({
    itemList: ['删除图片'],
    success: (res) => {
      if (res.tapIndex === 0) {
        confirmDelete(photo)
      }
    }
  })
}
</script>

<style scoped>
.album-page {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 120rpx;
  margin-top: 100rpx; /* {{ AURA-X: Add - 整体页面下移100rpx. Confirmed via 寸止. }} */
}

/* 导航栏样式 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  position: fixed;
  top: 100rpx; /* 导航栏下移100rpx */
  left: 0;
  right: 0;
  z-index: 100;
}

.nav-left, .nav-right {
  width: 88rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 活动信息卡片 */
.activity-info {
  margin: 208rpx 32rpx 32rpx; /* 调整上边距：100rpx(页面下移) + 88rpx(导航栏高度) + 20rpx(间距) */
  padding: 32rpx;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.activity-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.photo-count {
  font-size: 26rpx;
  color: #666;
}

/* 图片网格样式 */
.photo-grid {
  padding: 0 32rpx;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.photo-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 16rpx;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.photo-image {
  width: 100%;
  height: 100%;
}

.photo-user {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 16rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.user-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  border: 2rpx solid #fff;
}

.user-name {
  font-size: 24rpx;
  color: #fff;
  font-weight: 500;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.delete-btn {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  width: 48rpx;
  height: 48rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  text-align: center;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
  margin-top: 24rpx;
  font-weight: 500;
}

.empty-hint {
  font-size: 26rpx;
  color: #ccc;
  margin-top: 12rpx;
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
  margin-top: 24rpx;
}

/* 上传按钮样式 */
.upload-fab {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  width: 120rpx;
  height: 120rpx;
  background: #6AC086;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(106, 192, 134, 0.3);
  z-index: 50;
  transition: all 0.3s ease;
}

.upload-fab:active {
  transform: scale(0.95);
}

/* 上传弹窗样式 */
.upload-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.upload-dialog {
  width: 100%;
  max-width: 750rpx;
  background: #fff;
  border-radius: 32rpx 32rpx 0 0;
  padding: 40rpx 32rpx;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.upload-option {
  display: flex;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.upload-option:last-of-type {
  border-bottom: none;
}

.option-text {
  margin-left: 24rpx;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.upload-cancel {
  text-align: center;
  padding: 32rpx 0 16rpx;
  border-top: 1rpx solid #f0f0f0;
  margin-top: 24rpx;
}

.cancel-text {
  font-size: 30rpx;
  color: #666;
  font-weight: 500;
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .photo-grid {
    padding: 0 24rpx;
    gap: 16rpx;
  }

  .activity-info {
    margin: 108rpx 24rpx 24rpx;
    padding: 24rpx;
  }
}
</style>
