<script setup>
import {watch, ref, reactive, nextTick, computed} from "vue";
import {
  userget_guanzhu_list,
  userget_fans_list,
  userget_daijiesuan_status,
  configqrcode,
  usercreate_trial_share,
  configget_deep_link,
  getProfileBgMedia
} from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
  onShareAppMessage,
  onShareTimeline
} from "@dcloudio/uni-app";
import {store} from "@/store";
import {navto, getAge, copy, login, getUserInfo} from "@/utils";
import { isLoggedIn, getAuthParams, handleAuthFailure, safeApiCall } from "@/utils/auth";
import CustomTabBar from "../components/CustomTabBar.vue";


const cellList = ref([
  {
    photo: "huodong",
    icon: "calendar",
    title: "我的活动",
    url: "/pages/bundle/user/myActivity"
  },


  {
    photo: "tuiguang",
    icon: "list",
    title: "我的推广列表",
    url: "/pages/bundle/user/extensionList"
  },

  {
    photo: "qrcode",
    icon: "qrcode",
    title: "我的推广码",
    isQrcode: true
  },

]);

// 分会长功能列表
const branchPresidentCellList = [
  {
    photo: "branch-manage",
    icon: "home",
    title: "分会管理",
    url: "/pages/bundle/branch_president/manage"
  }
];



// 动态计算cellList
const dynamicCellList = ref([]);
const guanzhu = ref(0);
const fensi = ref(0);
const money = ref(0);
const show = ref(false);
const erweima = ref("");
const showTrialShare = ref(false);
// 存储分享数据
const shareData = ref(null);

// 背景媒体相关状态
const bgMedia = ref(null);
const bgMediaLoading = ref(false);
const bgMediaError = ref(false);

// 获取个人中心背景媒体配置
const loadBgMedia = async () => {
  try {
    bgMediaLoading.value = true;
    bgMediaError.value = false;

    const result = await getProfileBgMedia();

    if (result.status === 'ok') {
      bgMedia.value = result.data;
    } else if (result.status === 'empty') {
      // 没有配置背景媒体，使用默认样式
      bgMedia.value = null;
    } else {
      console.warn('获取背景媒体配置失败:', result.msg);
      bgMediaError.value = true;
    }
  } catch (error) {
    console.error('获取背景媒体配置异常:', error);
    bgMediaError.value = true;
  } finally {
    bgMediaLoading.value = false;
  }
};

const handleVideoError = (e) => {
  console.error('视频播放失败:', e);
  bgMediaError.value = true;
  uni.showToast({ title: '视频加载失败，使用默认背景', icon: 'none', duration: 2000 });
};

// 视频加载开始
const handleVideoLoadStart = () => {
  console.log('视频开始加载');
};

// 视频可以播放
const handleVideoCanPlay = () => {
  console.log('视频可以播放');
};

// 视频开始播放
const handleVideoPlay = () => {
  console.log('视频开始播放');
};

// 🆕 新增：分会长联系相关数据
const branchContactShow = ref(false);
const branchLeaderInfo = ref({
  has_branch: false,
  has_leader: false,
  branch_name: '',
  leader_name: '',
  leader_qr_image: '',
  leader_mobile: '',
  has_qr_code: false,
  message: ''
});

// 分享配置 - 分享给好友
onShareAppMessage((e) => {
  try {
    console.log('onShareAppMessage 被触发, 来源:', e?.from, '目标:', e?.target);

    // 如果有体验券分享数据，优先使用
    if (shareData.value) {
      console.log('使用体验券分享数据:', shareData.value);

      const shareConfig = {
        title: '赠送您一张30天会员体验券',
        path: shareData.value.path || `/pages/bundle/user/trialClaim?code=${shareData.value.share_code}`,
        imageUrl: shareData.value.imageUrl || store().$state.config?.img_config?.app_logo?.val || ''
      };

      console.log('体验券分享配置:', shareConfig);

      // 分享完成后清除分享数据，避免影响下次分享
      setTimeout(() => {
        shareData.value = null;
        console.log('已清除体验券分享数据');
      }, 1000);

      return shareConfig;
    }

    // 默认分享个人页面
    const defaultShareConfig = {
      title: store().$state.config?.config?.app_name?.val || '精彩活动等你来',
      imageUrl: store().$state.config?.img_config?.app_logo?.val || '',
      path: e?.from === "button"
        ? "/pages/bundle/shop/goodInfo?vip=true"
        : `/pages/my?pid=${store().$state.userInfo?.uid || 0}`
    };

    console.log('使用默认分享配置:', defaultShareConfig);
    return defaultShareConfig;

  } catch (error) {
    console.error('分享配置失败:', error);
    const fallbackConfig = {
      title: '精彩活动等你来',
      imageUrl: '',
      path: '/pages/my'
    };
    console.log('使用备用分享配置:', fallbackConfig);
    return fallbackConfig;
  }
});

// 分享配置 - 分享到朋友圈
onShareTimeline(() => {
  // 如果有体验券分享数据，优先使用
  if (shareData.value) {
    return {
      title: shareData.value.title,
      query: `code=${shareData.value.path.split('code=')[1] || ''}`,
      imageUrl: shareData.value.imageUrl
    };
  }
  // 默认分享个人页面
  return {
    title: store().$state.config.config.app_name.val,
    imageUrl: store().$state.config.img_config.app_logo.val,
    path: `/pages/my?pid=${store().$state.userInfo.uid}`
  };
});
onLoad(async (e) => {
  if (e?.pid) store().changePid(e.pid);

  // 初始化时也更新一次cellList
  updateDynamicCellList();
});
onShow(async () => {
  // 首先获取用户信息，这会自动处理认证失败的情况
  getUserInfo();

  // {{ AURA-X: Add - 页面显示时加载背景媒体配置. Confirmed via 寸止. }}
  // 加载背景媒体配置
  loadBgMedia();

  // 使用统一的登录状态检查，避免频繁的服务器验证
  if (isLoggedIn()) {
    console.log("用户已登录，开始获取个人数据");

    // 使用safeApiCall包装API调用，自动处理认证失败
    try {
      // 获取关注数量
      const res = await safeApiCall(
        () => userget_guanzhu_list({page: 1}),
        '获取关注数量'
      );
      if (res) {
        guanzhu.value = res.count || 0;
      } else {
        guanzhu.value = 0;
      }
    } catch (error) {
      console.error("获取关注数量失败:", error);
      guanzhu.value = 0;
    }

    // 获取粉丝数量 - 使用真实的粉丝API
    try {
      const fansRes = await safeApiCall(
        () => userget_fans_list({page: 1}),
        '获取粉丝数量'
      );
      if (fansRes) {
        fensi.value = fansRes.count || 0;
      } else {
        fensi.value = 0;
      }
    } catch (error) {
      console.error("获取粉丝数量失败:", error);
      fensi.value = 0;
    }

    // 未读通知数量现在在通知页面管理，个人中心不再处理

    // 获取提现金额
    try {
      money.value = 0;
      const moneyRes = await safeApiCall(
        () => userget_daijiesuan_status(getAuthParams()),
        '获取提现金额'
      );
      if (moneyRes && moneyRes.data) {
        for (let i in moneyRes.data) {
          money.value += (moneyRes.data[i] * 100) / 100;
        }
      }
    } catch (error) {
      console.error("获取提现金额失败:", error);
      money.value = 0;
    }
  } else {
    // 用户未登录，重置所有数据为默认值
    console.log("用户未登录，跳过API调用");
    guanzhu.value = 0;
    fensi.value = 0;
    // 未读通知数量现在在通知页面管理
    money.value = 0;
  }

  // 动态计算cellList
  updateDynamicCellList();

  // 延迟再次更新，确保用户信息完全加载后更新cellList
  setTimeout(() => {
    console.log('延迟更新cellList，当前用户信息:', store().$state.userInfo);
    updateDynamicCellList();
  }, 500);
});

// 更新动态cellList的函数
const updateDynamicCellList = () => {
  const userInfo = store().$state.userInfo;
  let newCellList = [...cellList.value];

  if (userInfo && userInfo.role_type !== undefined && userInfo.role_type !== null) {
    const roleType = parseInt(userInfo.role_type);

    // 🆕 修改：支持新角色类型
    if ([1, 3, 4].includes(roleType)) {
      // 分会长(1)、场地与活动第三方(3)、城市分会长(4) 显示分会管理功能
      newCellList = [...newCellList, ...branchPresidentCellList];
    }
    // role_type === 0 是管理员，不显示额外功能
  }

  dynamicCellList.value = newCellList;
};



// 监听用户信息变化，动态更新cellList
watch(() => store().$state.userInfo, () => {
  updateDynamicCellList();
}, { deep: true });

const logined = async () => {
  if (store().$state.userInfo?.name) copy(store().$state.userInfo?.name);
  else login({fun: getInfo()});
};
const openPhoto = async (i, item) => {
  console.log('openPhoto called with:', i, item);

  // 🆕 新增：处理分会长联系功能
  if (item.isBranchContact) {
    await getBranchLeaderInfo();
    return;
  }

  if (item.isQrcode) {
    uni.showLoading({ title: '加载中...' });
    try {
      const path = 'pages/my';
      const query = `pid=${store().$state.userInfo.uid}`;

      erweima.value = `https://api.linqingkeji.com/config/wxacode?path=${encodeURIComponent(path)}&query=${encodeURIComponent(query)}`;
      console.log('New QR Code URL:', erweima.value);

      nextTick(() => {
        show.value = true;
      });

    } catch (error) {
      console.error('获取小程序码处理错误:', error);
      uni.hideLoading();
      uni.$u.toast('获取小程序码失败，请重试');
    }
  } else if (item.url) {
      navto(item.url);
  }
};
// Define image event handlers
const onImageLoad = (e) => {
  uni.hideLoading();
}
const onImageError = (e) => {
  uni.hideLoading();
  uni.$u.toast('二维码图片加载失败');
}

// 🆕 新增：获取分会长信息
const getBranchLeaderInfo = async () => {
  try {
    uni.showLoading({
      title: '获取分会长信息...'
    });

    const userInfo = store().$state.userInfo;
    if (!userInfo?.uid || !userInfo?.token) {
      uni.hideLoading();
      uni.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    const response = await uni.request({
      url: `${store().$state.url}user/get_branch_leader_info`,
      method: 'POST',
      data: {
        uid: userInfo.uid,
        token: userInfo.token
      }
    });

    uni.hideLoading();

    if (response.data.status === 'ok') {
      branchLeaderInfo.value = response.data.data;
      branchContactShow.value = true;
    } else {
      uni.showToast({
        title: response.data.msg || '获取分会长信息失败',
        icon: 'none'
      });
    }
  } catch (error) {
    uni.hideLoading();
    console.error('获取分会长信息失败:', error);
    uni.showToast({
      title: '网络错误，请稍后重试',
      icon: 'none'
    });
  }
};

// 🆕 新增：保存分会长二维码到相册
const saveBranchLeaderQrCode = () => {
  if (!branchLeaderInfo.value.leader_qr_image) {
    uni.showToast({
      title: '没有可保存的二维码',
      icon: 'none'
    });
    return;
  }

  uni.showLoading({
    title: '保存中...'
  });

  uni.downloadFile({
    url: branchLeaderInfo.value.leader_qr_image,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            uni.hideLoading();
            uni.showToast({
              title: '保存成功',
              icon: 'success'
            });
          },
          fail: (error) => {
            uni.hideLoading();
            console.error('保存到相册失败:', error);
            uni.showToast({
              title: '保存失败，请检查相册权限',
              icon: 'none'
            });
          }
        });
      } else {
        uni.hideLoading();
        uni.showToast({
          title: '下载图片失败',
          icon: 'none'
        });
      }
    },
    fail: (error) => {
      uni.hideLoading();
      console.error('下载图片失败:', error);
      uni.showToast({
        title: '下载失败，请稍后重试',
        icon: 'none'
      });
    }
  });
};

// 🆕 新增：二维码加载错误处理
const onQrCodeError = () => {
  uni.showToast({
    title: '二维码加载失败',
    icon: 'none'
  });
};

// 🆕 新增：昵称截断处理
const truncatedNickname = computed(() => {
  const nickname = store().$state.userInfo?.nickname;
  if (!nickname) {
    return "登录/注册";
  }

  return truncateNickname(nickname);
});

// 🆕 新增：昵称截断工具函数
// 截断规则：
// - 中文字符：超过5个汉字时截断（每个中文字符占2个单位）
// - 英文字符：超过10个英文字母时截断（每个英文字符占1个单位）
// - 混合字符：总长度超过10个单位时截断
// 测试用例：
// - "张三李四王五" (5个中文) -> "张三李四王五" (10单位，不截断)
// - "张三李四王五赵六" (6个中文) -> "张三李四王五..." (截断)
// - "HelloWorld" (10个英文) -> "HelloWorld" (10单位，不截断)
// - "HelloWorld!" (11个英文) -> "HelloWorld..." (截断)
// - "张三Hello" (2中文+5英文) -> "张三Hello" (9单位，不截断)
// - "张三HelloWorld" (2中文+10英文) -> "张三HelloWor..." (截断)
const truncateNickname = (text) => {
  if (!text || typeof text !== 'string') return '';

  // 去除首尾空格
  text = text.trim();
  if (!text) return '';

  let totalLength = 0;
  let truncatedText = '';

  for (let i = 0; i < text.length; i++) {
    const char = text[i];

    // 判断是否为中文字符（包括中文标点符号、全角字符等）
    const isChinese = /[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff\u3000-\u303f\uff00-\uffef\u2e80-\u2eff\u31c0-\u31ef\u2f00-\u2fdf]/.test(char);

    // 中文字符占2个单位，英文字符占1个单位
    const charLength = isChinese ? 2 : 1;

    // 检查是否超过限制（总长度10个单位）
    if (totalLength + charLength > 10) {
      break;
    }

    totalLength += charLength;
    truncatedText += char;
  }

  // 如果原文本被截断了，添加省略号
  // 确保截断后的文本不为空
  if (truncatedText.length === 0 && text.length > 0) {
    // 如果第一个字符就超过限制，至少显示一个字符
    truncatedText = text[0];
  }

  return truncatedText.length < text.length ? truncatedText + '...' : truncatedText;
};

// 🆕 新增：显示完整昵称
const showFullNickname = () => {
  const fullNickname = store().$state.userInfo?.nickname;
  if (fullNickname && fullNickname.length > truncatedNickname.value.replace('...', '').length) {
    uni.showModal({
      title: '完整昵称',
      content: fullNickname,
      showCancel: false,
      confirmText: '知道了',
      confirmColor: '#6AC086'
    });
  }
};







// 准备体验券分享数据
const prepareTrialShare = async () => {
  console.log('准备体验券分享数据...');

  if (!isLoggedIn()) {
    uni.$u.toast('请先登录');
    return;
  }

  // 检查用户权限
  const userRole = store().$state.userInfo?.role_type;
  if (userRole !== '0' && userRole !== '1' && userRole !== 0 && userRole !== 1) {
    uni.$u.toast('您暂无权限分享会员体验券');
    return;
  }

  uni.showLoading({ title: '生成分享链接中...' });

  try {
    const res = await usercreate_trial_share({
      ...getAuthParams(),
      trial_days: 30
    });

    console.log('创建分享链接结果:', res);

    if (res?.status === 'ok') {
      // 存储分享数据供 onShareAppMessage 使用
      shareData.value = {
        title: `赠送您一张30天会员体验券`,
        desc: `我邀请您免费体验30天会员服务，快来领取吧！`,
        path: `/pages/bundle/user/trialClaim?code=${res.data.share_code}`,
        imageUrl: store().$state.config?.img_config?.app_logo?.val || '',
        share_url: res.data.share_url,
        share_code: res.data.share_code
      };

      console.log('体验券分享数据已准备完成:', shareData.value);

      // #ifdef MP-WEIXIN
      // 微信小程序环境，数据已准备好，等待 onShareAppMessage 被触发
      uni.hideLoading();

      // 显示更友好的分享引导弹窗
      uni.showModal({
        title: '分享体验券',
        content: '体验券已准备好！\n\n请点击右上角"..."按钮，选择"发送给朋友"来分享30天会员体验券给好友。',
        showCancel: true,
        cancelText: '复制链接',
        confirmText: '知道了',
        success: (res) => {
          if (res.cancel) {
            // 用户选择复制链接
            uni.setClipboardData({
              data: shareData.value.share_url,
              success: () => {
                uni.$u.toast('链接已复制到剪贴板，请粘贴分享给好友');
              },
              fail: () => {
                uni.$u.toast('复制失败，请重试');
              }
            });
          }
        }
      });

      // 显示分享菜单
      try {
        uni.showShareMenu({
          withShareTicket: true,
          menus: ['shareAppMessage']
        });
      } catch (error) {
        console.log('显示分享菜单失败:', error);
      }
      // #endif

      // #ifndef MP-WEIXIN
      // 非微信小程序环境，使用系统分享或复制链接
      uni.hideLoading();
      if (uni.canIUse('shareWithSystem')) {
        uni.shareWithSystem({
          summary: shareData.value.desc,
          href: shareData.value.share_url,
          success() {
            uni.$u.toast('分享成功');
          },
          fail(err) {
            console.log('分享失败:', err);
            // 分享失败时复制链接
            uni.setClipboardData({
              data: shareData.value.share_url,
              success: () => {
                uni.$u.toast('链接已复制到剪贴板，请粘贴分享');
              }
            });
          }
        });
      } else {
        // 不支持系统分享，直接复制链接
        uni.setClipboardData({
          data: shareData.value.share_url,
          success: () => {
            uni.$u.toast('链接已复制到剪贴板，请粘贴分享');
          }
        });
      }
      // #endif
    } else {
      uni.hideLoading();
      uni.$u.toast(res?.msg || '生成分享链接失败');
    }
  } catch (error) {
    uni.hideLoading();
    console.error('准备分享体验券失败:', error);
    uni.$u.toast('分享失败，请重试');
  }
};

// 分享会员体验券 - 跳转到赠送页面
const goToMemberGift = () => {
  console.log('跳转到体验会员赠送页面');

  // 检查用户权限
  const userRole = store().$state.userInfo?.role_type;
  if (userRole !== '0' && userRole !== '1' && userRole !== 0 && userRole !== 1) {
    uni.$u.toast('您暂无权限分享会员体验券');
    return;
  }

  if (!isLoggedIn()) {
    uni.$u.toast('请先登录');
    return;
  }

  // 跳转到赠送体验会员页面
  try {
    navto('/pages/bundle/user/trialGift');
  } catch (error) {
    console.error('跳转到赠送页面失败:', error);
    uni.$u.toast('页面跳转失败');
  }
};

// 检查是否显示分享功能
const shouldShowTrialShare = () => {
  const userRole = store().$state.userInfo?.role_type;
  return userRole === '0' || userRole === '1' || userRole === 0 || userRole === 1;
};


</script>
<template>
  <view class="page">
    <!-- {{ AURA-X: Modify - 替换为支持背景媒体的导航栏组件. Confirmed via 寸止. }} -->
    <!-- 背景媒体导航栏 -->
    <view class="profile-header" :class="{ 'has-media': bgMedia && !bgMediaError }">
      <!-- 背景媒体层 -->
      <view v-if="bgMedia && !bgMediaError" class="bg-media-container">
        <!-- 图片背景 -->
        <image
          v-if="bgMedia.media_type === 'image'"
          :src="bgMedia.media_url"
          class="bg-media-image"
          mode="aspectFill"
          @error="bgMediaError = true"
        ></image>

        <!-- GIF背景 -->
        <image
          v-else-if="bgMedia.media_type === 'gif'"
          :src="bgMedia.media_url"
          class="bg-media-image"
          mode="aspectFill"
          @error="bgMediaError = true"
        ></image>

        <!-- {{ AURA-X: Fix - 修复视频背景播放配置. Confirmed via 寸止 }} -->
        <!-- 视频背景 -->
        <video
          v-else-if="bgMedia.media_type === 'video'"
          :src="bgMedia.media_url"
          class="bg-media-video"
          :autoplay="true"
          :loop="true"
          :muted="true"
          :show-center-play-btn="false"
          :show-play-btn="false"
          :controls="false"
          :enable-progress-gesture="false"
          :show-loading="false"
          :show-fullscreen-btn="false"
          :show-progress="false"
          object-fit="cover"
          @error="handleVideoError"
          @loadstart="handleVideoLoadStart"
          @canplay="handleVideoCanPlay"
          @play="handleVideoPlay"
        ></video>

        <!-- 遮罩层 -->
        <view class="bg-media-overlay"></view>
      </view>

      <!-- 默认渐变背景（当没有媒体或加载失败时） -->
      <view
        v-if="!bgMedia || bgMediaError"
        class="default-bg"
        :style="{ background: 'linear-gradient(135deg, #ffffff 0%, #88D7A0 50%, #6AC086 100%)' }"
      ></view>

      <!-- 导航栏内容 - 只在加载失败时显示 -->
      <view v-if="!bgMedia || bgMediaError" class="header-content">
        <text class="header-title">我的</text>
      </view>
    </view>
    <view class="pa left50 tl50 p30 w690 b6f r30 user-info-card" :style="{ top: bgMedia && !bgMediaError ? '-80rpx' : '-420rpx' }">
      <!-- 编辑资料按钮 - 右上角，纯图标按钮 -->
      <view class="edit-profile-btn" @click="navto(`/pages/bundle/user/edit`)" style="top: 25rpx;">
        <u-icon name="edit-pen" size="40rpx" color="#6AC086"></u-icon>
      </view>

      <view class="df ais">
        <view style="position: relative;">
          <u-avatar
              size="87rpx"
              mode="aspectFill"
              :src="store().$state.userInfo?.avatar"
              shape="circle"
              bg-color="#F5F5F5"
          ></u-avatar>
          <!-- VIP标识 - 位于头像上方 -->
          <view
              v-if="store().$state.userInfo?.is_huiyuan"
              class="vip-badge-top"
              style="position: absolute; right: -10rpx; top: -25rpx;"
          >
            <image
                src="/static/vip.svg"
                mode="aspectFit"
                style="width: 50rpx; height: 50rpx;"
                lazy-load
            ></image>
          </view>
        </view>
        <!-- 原有VIP标识位置 -->
        <view
            class="pa"
            style="top: 115rpx; left: 47rpx"
            v-if="store().$state.userInfo?.is_huiyuan"
        >
          <image
              style="width: 76rpx; height: 34rpx;"
              :src="`${store().$state.url}vip.png`"
              mode="aspectFill"
              lazy-load
          ></image>
        </view>
        <view class="ml10 df fdc f1" style="margin-left: 24rpx;">
          <view class="df aic">
            <view class="df fdc">
              <!-- 昵称和性别图标同行 -->
              <view class="df aic" style="margin-bottom: 12rpx;">
                <text
                    style="font-size: 36rpx; font-weight: normal; color: #333; max-width: 300rpx; margin-right: 12rpx;"
                    @click.stop="store().$state.userInfo?.nickname ? showFullNickname() : navto('/pages/bundle/common/login')"
                    class="nickname-text"
                >
                    {{ truncatedNickname }}
                </text>
                <!-- 性别图标移动到昵称右侧 -->
                <u-icon
                    v-if="store().$state.userInfo?.sex && store().$state.userInfo?.sex != 0"
                    :name="store().$state.userInfo?.sex == 1 ? 'man' : 'woman'"
                    size="30rpx"
                    :color="store().$state.userInfo?.sex == 1 ? '#007AFF' : '#FF69B4'"
                ></u-icon>
              </view>
              <!-- 关注、粉丝移动到昵称下方，与昵称左对齐 -->
              <view class="df aic" style="margin-bottom: 8rpx;">
                <!-- 关注和粉丝链接 -->
                <view class="df aic" style="font-size: 24rpx;">
                  <text
                    class="link-text"
                    @click.stop="navto('/pages/bundle/user/myFollow')"
                    style="color: #6AC086; margin-right: 8rpx;"
                  >
                    {{ guanzhu || 0 }}关注
                  </text>
                  <text
                    class="link-text"
                    @click.stop="navto('/pages/bundle/user/myFans')"
                    style="color: #6AC086;"
                  >
                    {{ fensi || 0 }}粉丝
                  </text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <u-line
          color="#EEEEEE"
          direction="row"
          length="630rpx"
          margin="20rpx 30rpx"
      ></u-line>
      <!-- 积分和提现选项 - 同一行 -->
      <view class="points-withdraw-section">
        <!-- 积分功能 -->
        <view class="f1 stats-item" @click="navto('/pages/bundle/user/pointsLog')">
          <u-text
            :text="store().$state.userInfo?.points || '0'"
            align="center"
            size="32rpx"
            :bold="true"
            color="#FF9500"
          ></u-text>
          <u-text
            align="center"
            text="我的积分"
            size="24rpx"
            color="#666"
            margin="4rpx 0 0 0"
          ></u-text>
        </view>

        <view class="stats-divider"></view>

        <!-- 提现功能 -->
        <view class="f1 stats-item" @click="navto('/pages/bundle/user/withDraw')">
          <u-text
            :text="$u.priceFormat(money, 2)"
            align="center"
            size="32rpx"
            :bold="true"
            color="#6AC086"
          ></u-text>
          <u-text
            align="center"
            text="我的提现"
            size="24rpx"
            color="#666"
            margin="4rpx 0 0 0"
          ></u-text>
        </view>
      </view>

      <!-- 四个活动选项 - 放在用户信息卡片内 -->
      <view class="user-activity-section">
        <view class="activity-grid">
          <view class="activity-item" @click="navto('/pages/bundle/user/userActivity?type=likes')">
            <view class="activity-icon">
              <u-icon name="thumb-up-fill" size="40rpx" color="#88D7A0"></u-icon>
            </view>
            <text class="activity-text">赞过</text>
          </view>
          <view class="activity-item" @click="navto('/pages/bundle/user/userActivity?type=favorites')">
            <view class="activity-icon">
              <u-icon name="star-fill" size="40rpx" color="#88D7A0"></u-icon>
            </view>
            <text class="activity-text">收藏</text>
          </view>
          <view class="activity-item" @click="navto('/pages/bundle/user/userActivity?type=comments')">
            <view class="activity-icon">
              <u-icon name="chat-fill" size="40rpx" color="#88D7A0"></u-icon>
            </view>
            <text class="activity-text">评论</text>
          </view>
          <view class="activity-item" @click="navto('/pages/bundle/user/userActivity?type=published')">
            <view class="activity-icon">
              <u-icon name="edit-pen-fill" size="40rpx" color="#88D7A0"></u-icon>
            </view>
            <text class="activity-text">发布</text>
          </view>
        </view>
      </view>

      <!-- 分享会员体验券功能 - 仅对role_type 0/1用户显示，移动到四个活动选项下方 -->
      <view v-if="shouldShowTrialShare()" class="trial-share-section">
        <view class="trial-share-item" @click="goToMemberGift">
          <view class="trial-share-icon">
            <u-icon name="gift" size="32rpx" color="#6AC086"></u-icon>
          </view>
          <view class="trial-share-content">
            <text class="trial-share-title">分享会员体验券</text>
            <text class="trial-share-desc">邀请好友免费体验30天会员</text>
          </view>
          <view class="trial-share-arrow">
            <u-icon name="arrow-right" color="#999" size="14"></u-icon>
          </view>
        </view>
      </view>

      <!-- 会员选项 - 移动到分享会员体验券下方 -->
      <view class="member-section">
        <!-- 会员功能 -->
        <view class="stats-item vip-item full-width" @click="navto('/pages/bundle/user/vip')">
          <u-text
            :text="store().$state.userInfo?.is_huiyuan ? '已开通' : '开通会员'"
            align="center"
            size="28rpx"
            :bold="true"
            color="#D19C69"
          ></u-text>
          <u-text
            align="center"
            text="会员服务"
            size="24rpx"
            color="#666"
            margin="4rpx 0 0 0"
          ></u-text>
        </view>
      </view>
    </view>



    <u-gap height="30rpx"></u-gap>
    <view class="pr px30 r30">

    </view>
    <template>
      <view class="my-cells">
        <view
            v-for="(val, i) in dynamicCellList"
            :key="i"
            class="custom-cell"
            @click="val.url ? navto(val.url) : openPhoto(i, val)"
        >
          <!-- 图片容器 -->
          <view class="icon-image-wrapper">
            <image
                v-if="val.photo === 'tongzhi'"
                class="icon-image"
                src="/static/tongzhi.svg"
                mode="aspectFit"
                lazy-load
            ></image>
            <image
                v-else-if="val.photo === 'branch-contact'"
                class="icon-image"
                src="/static/branch-contact.svg"
                mode="aspectFit"
                lazy-load
            ></image>
            <image
                v-else
                class="icon-image"
                :src="`${store().$state.url}${val.photo}.png`"
                mode="aspectFill"
                lazy-load
            ></image>
          </view>

          <view class="cell-content">
            <text class="cell-title">{{ val.title }}</text>
          </view>
          <!-- 通知红点已移除，未读数量在通知页面显示 -->
          <view class="cell-arrow">
            <u-icon name="arrow-right" color="#999" size="14"></u-icon>
          </view>
        </view>
      </view>
    </template>
    <!--    </view>-->

    <u-popup
        :show="show"
        close-on-click-overlay
        mode="center"
        @close="show = false"
        class="qrcode-popup"
    >
      <view class="qrcode-container">
        <view class="qrcode-image-container">
          <template v-if="erweima">
            <image
              style="width: 500rpx; height: 500rpx;"
              :src="erweima"
              mode="aspectFit"
              lazy-load
              @load="onImageLoad"
              @error="onImageError"
              show-menu-by-longpress="true"
              ></image>
          </template>
          <template v-else>
            <view class="qrcode-placeholder">
              <view class="loading-circle"></view>
              <text class="loading-text">二维码加载中...</text>
            </view>
          </template>
        </view>
        <u-text
            margin="20rpx 0"
            align="center"
            size="32rpx"
            color="#333"
            text="MindfulMeetUp & 小聚会"
        ></u-text>
        <u-text
            align="center"
            size="28rpx"
            color="#919191"
            text="长按二维码可以分享"
        ></u-text>
      </view>
    </u-popup>

    <!-- 🆕 新增：分会长联系弹窗 -->
    <u-popup
        :show="branchContactShow"
        close-on-click-overlay
        mode="center"
        @close="branchContactShow = false"
        class="branch-contact-popup"
    >
      <view class="branch-contact-container">
        <!-- 弹窗标题 -->
        <view class="popup-header">
          <u-text
              size="36rpx"
              bold
              color="#333"
              text="联系分会长"
              align="center"
          ></u-text>
        </view>

        <!-- 内容区域 -->
        <view class="popup-content">
          <!-- 没有分会的情况 -->
          <template v-if="!branchLeaderInfo.has_branch">
            <view class="no-branch-container">
              <u-icon name="info-circle" size="80rpx" color="#999" class="info-icon"></u-icon>
              <u-text
                  size="32rpx"
                  color="#666"
                  :text="branchLeaderInfo.message || '您暂未加入任何分会'"
                  align="center"
                  margin="20rpx 0"
              ></u-text>
            </view>
          </template>

          <!-- 有分会但没有分会长的情况 -->
          <template v-else-if="branchLeaderInfo.has_branch && !branchLeaderInfo.has_leader">
            <view class="no-leader-container">
              <u-icon name="account" size="80rpx" color="#999" class="info-icon"></u-icon>
              <u-text
                  size="32rpx"
                  color="#333"
                  :text="`分会：${branchLeaderInfo.branch_name}`"
                  align="center"
                  margin="20rpx 0"
              ></u-text>
              <u-text
                  size="28rpx"
                  color="#666"
                  :text="branchLeaderInfo.message || '当前分会暂无分会长'"
                  align="center"
              ></u-text>
            </view>
          </template>

          <!-- 有分会长的情况 -->
          <template v-else-if="branchLeaderInfo.has_branch && branchLeaderInfo.has_leader">
            <view class="leader-info-container">
              <!-- 分会信息 -->
              <view class="branch-info">
                <u-text
                    size="28rpx"
                    color="#666"
                    text="所属分会"
                    margin="0 0 8rpx 0"
                ></u-text>
                <u-text
                    size="32rpx"
                    color="#333"
                    :text="branchLeaderInfo.branch_name"
                    bold
                ></u-text>
              </view>

              <!-- 分会长信息 -->
              <view class="leader-info">
                <u-text
                    size="28rpx"
                    color="#666"
                    text="分会长"
                    margin="20rpx 0 8rpx 0"
                ></u-text>
                <u-text
                    size="32rpx"
                    color="#333"
                    :text="branchLeaderInfo.leader_name"
                    bold
                ></u-text>
              </view>

              <!-- 微信二维码 -->
              <template v-if="branchLeaderInfo.has_qr_code">
                <view class="qr-code-section">
                  <u-text
                      size="28rpx"
                      color="#666"
                      text="微信二维码"
                      margin="20rpx 0 16rpx 0"
                  ></u-text>
                  <view class="qr-code-container">
                    <image
                        class="qr-code-image"
                        :src="branchLeaderInfo.leader_qr_image"
                        mode="aspectFit"
                        @error="onQrCodeError"
                    ></image>
                  </view>
                </view>
              </template>

              <!-- 没有二维码的提示 -->
              <template v-else>
                <view class="no-qr-container">
                  <u-text
                      size="28rpx"
                      color="#999"
                      text="分会长暂未设置联系方式"
                      align="center"
                      margin="20rpx 0"
                  ></u-text>
                </view>
              </template>
            </view>
          </template>
        </view>

        <!-- 操作按钮 -->
        <view class="popup-actions">
          <template v-if="branchLeaderInfo.has_qr_code">
            <u-button
                text="保存到相册"
                shape="circle"
                color="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
                :customStyle="{
                  width: '200rpx',
                  height: '72rpx',
                  fontSize: '28rpx',
                  color: '#ffffff',
                  marginRight: '20rpx',
                  boxShadow: '0 4rpx 12rpx rgba(106, 192, 134, 0.3)'
                }"
                @click="saveBranchLeaderQrCode"
            ></u-button>
          </template>
          <u-button
              text="关闭"
              shape="circle"
              color="#f8f9fa"
              :customStyle="{
                width: '120rpx',
                height: '72rpx',
                fontSize: '28rpx',
                color: '#666',
                border: '1rpx solid #e9ecef'
              }"
              @click="branchContactShow = false"
          ></u-button>
        </view>
      </view>
    </u-popup>

    <!-- 自定义底部导航栏 -->
    <CustomTabBar :current="4" />
  </view>
</template>

<style lang="less">
.page {
  min-height: 100vh;
  background: var(--color-background, #f8f9fa);
  padding-bottom: 220rpx; /* 增加底部内边距，确保底部内容不被遮挡 */
  /* 为不同设备的安全区域预留额外空间 */
  padding-bottom: calc(220rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(220rpx + env(safe-area-inset-bottom));
  position: relative;
  /* 确保页面可以正常滚动 */
  overflow-y: auto;
  box-sizing: border-box;
}

.my-cells {
  margin-top: -110rpx; /* {{ AURA-X: Modify - 向上移动150rpx (40-150=-110). Confirmed via 寸止 }} */
  padding: 20rpx 30rpx;
}

.custom-cell {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(106, 192, 134, 0.08);
  margin-bottom: 20rpx;
  padding: 32rpx;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1rpx solid rgba(106, 192, 134, 0.08);
  position: relative;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 16rpx rgba(106, 192, 134, 0.2);
    background: rgba(106, 192, 134, 0.05);
  }
}

.cell-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg,
  rgba(136, 215, 160, 0.1),
  rgba(106, 192, 134, 0.1));
  border-radius: 20rpx;
  margin-right: 20rpx;
}

.cell-content {
  flex: 1;
}

.cell-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.cell-arrow {
  padding-left: 10rpx;
}

.pa.left50.tl50.p30.w690.b6f.r30 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  box-shadow: 0 12rpx 32rpx rgba(106, 192, 134, 0.15);
  backdrop-filter: blur(10px);
  border: 1rpx solid rgba(106, 192, 134, 0.1);
  position: relative;
  top: 70rpx; /* 用户卡片上移10rpx */
  left: 50%;
  transform: translateX(-50%);
  width: calc(100% - 60rpx);
  margin-bottom: 40rpx;
  z-index: 2;
  padding: 24rpx;
}

.u-avatar {
  border: 3rpx solid rgba(106, 192, 134, 0.3);
  box-shadow: 0 6rpx 16rpx rgba(106, 192, 134, 0.2);
}

/* {{ AURA-X: Modify - 修复HTML标签选择器，改为类选择器. Confirmed via 寸止. }} */
/* VIP徽章顶部样式 */
.vip-badge-top {
  position: absolute;
  top: -25rpx;
  left: 40rpx;
  z-index: 5;
  transform: rotate(15deg);
}

.vip-badge-top .vip-badge-image {
  width: 70rpx;
  height: 32rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
}

.df.aic.jcsb {
  padding: 10rpx 0;
}

.df.aic.jcsb .f1 {
  text-align: center;
  padding: 12rpx 20rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(106, 192, 134, 0.08);
  transition: all 0.3s ease;
  border: 1rpx solid rgba(106, 192, 134, 0.04);

  &:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 6rpx 16rpx rgba(106, 192, 134, 0.15);
  }
}

.u-line {
  margin: 10rpx 30rpx;
}

.u-popup {
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.u-image {
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.myTitle {
  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);
  backdrop-filter: blur(10px);
  box-shadow: 0 4rpx 12rpx rgba(106, 192, 134, 0.2);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1;
}

.u-button {
  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%) !important;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4rpx 12rpx rgba(106, 192, 134, 0.2);
  backdrop-filter: blur(5px);

  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 2rpx 6rpx rgba(106, 192, 134, 0.2);
  }
}

.df.aic {
  margin: 10rpx 0;
}

.mr20.x30 {
  font-weight: 600;
  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.qrcode-popup {
  border-radius: 30rpx;
  overflow: hidden;
}

.qrcode-container {
  padding: 30rpx;
  background: #ffffff;
  border-radius: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 10rpx 30rpx rgba(238, 130, 238, 0.1);
}

.qrcode-image-container {
  width: 500rpx;
  height: 500rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(238, 130, 238, 0.03);
  border-radius: 20rpx;
  overflow: hidden;
}

.qrcode-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 24rpx;
  color: #999;
}

.loading-circle {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid rgba(238, 130, 238, 0.1);
  border-top-color: rgba(255, 105, 180, 0.8);
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
}

@keyframes loading-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.mr10.df.aic.x24.c6f.r20 {
  backdrop-filter: blur(5px);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2rpx 6rpx rgba(106, 192, 134, 0.1);
}










.icon-image-wrapper {
  width: 48rpx;
  height: 48rpx;
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
  flex-shrink: 0; /* 防止图片容器被压缩 */
}

.icon-image {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
}

.edit-profile-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background: rgba(106, 192, 134, 0.1);
  border-radius: 20rpx;
  border: 1rpx solid rgba(106, 192, 134, 0.2);
  transition: all 0.3s ease;
  z-index: 10;

  &:active {
    transform: scale(0.95);
    background: rgba(106, 192, 134, 0.15);
  }
}

/* 统计项样式 */
.stats-item {
  text-align: center;
  padding: 16rpx 20rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(106, 192, 134, 0.08);
  transition: all 0.3s ease;
  border: 1rpx solid rgba(106, 192, 134, 0.04);
  cursor: pointer;

  &:active {
    transform: translateY(-2rpx);
    box-shadow: 0 6rpx 16rpx rgba(106, 192, 134, 0.15);
    background: rgba(106, 192, 134, 0.05);
  }
}

/* 分隔线样式 */
.stats-divider {
  width: 2rpx;
  height: 60rpx;
  background: linear-gradient(to bottom, transparent, rgba(106, 192, 134, 0.2), transparent);
  margin: 0 16rpx;
  align-self: center;
}

/* 用户活动选项样式 */
.user-activity-section {
  margin: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(106, 192, 134, 0.08);
  backdrop-filter: blur(10px);
  border: 1rpx solid rgba(106, 192, 134, 0.08);
  padding: 30rpx;
}

.activity-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.activity-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  background: rgba(106, 192, 134, 0.05);
  border-radius: 20rpx;
  border: 1rpx solid rgba(106, 192, 134, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;

  &:active {
    transform: scale(1.08);
    background: rgba(106, 192, 134, 0.15);
    box-shadow: 0 8rpx 20rpx rgba(106, 192, 134, 0.25);
  }
}

.activity-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  background: rgba(106, 192, 134, 0.1);
  border-radius: 50%;
  margin-bottom: 12rpx;
  border: 1rpx solid rgba(106, 192, 134, 0.2);
  transition: all 0.3s ease;
}

.activity-item:active .activity-icon {
  transform: scale(1.15);
  background: rgba(106, 192, 134, 0.25);
}

.activity-text {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  text-align: center;
}

/* 通知红点样式 */
.notification-badge {
  position: absolute;
  top: -8rpx;
  right: 60rpx;
  background: linear-gradient(135deg, #FF6B6B 0%, #FF5252 100%);
  border-radius: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #fff;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
  z-index: 10;
}

.badge-text {
  font-size: 20rpx;
  color: #fff;
  font-weight: 600;
  line-height: 1;
  padding: 0 6rpx;
}

/* 分享会员体验券样式 */
.trial-share-section {
  margin-top: 20rpx;
  padding: 0 30rpx;
}

.trial-share-item {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, rgba(106, 192, 134, 0.05) 0%, rgba(136, 215, 160, 0.05) 100%);
  border-radius: 24rpx;
  padding: 24rpx;
  border: 2rpx solid rgba(106, 192, 134, 0.15);
  box-shadow: 0 8rpx 24rpx rgba(106, 192, 134, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 12rpx rgba(106, 192, 134, 0.2);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2rpx;
    background: linear-gradient(90deg, #6AC086 0%, #88D7A0 100%);
  }
}

.share-trial-button {
  width: 100%;
  text-align: left;
}

.trial-share-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64rpx;
  height: 64rpx;
  background: linear-gradient(135deg, rgba(106, 192, 134, 0.1) 0%, rgba(136, 215, 160, 0.1) 100%);
  border-radius: 20rpx;
  margin-right: 20rpx;
  border: 1rpx solid rgba(106, 192, 134, 0.2);
}

.trial-share-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.trial-share-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.trial-share-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.trial-share-arrow {
  padding-left: 10rpx;
}

/* 会员和提现区域样式优化 */
.member-withdraw-section {
  display: flex;
  align-items: center;
  margin: 20rpx 30rpx;
  padding: 0;
}

/* VIP项特殊样式 */
.vip-item {
  background: linear-gradient(135deg, rgba(209, 156, 105, 0.05) 0%, rgba(234, 210, 175, 0.05) 100%);
  border: 1rpx solid rgba(209, 156, 105, 0.1);

  &:active {
    background: linear-gradient(135deg, rgba(209, 156, 105, 0.1) 0%, rgba(234, 210, 175, 0.1) 100%);
  }
}

/* 链接文字样式 */
.link-text {
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    opacity: 0.7;
    transform: scale(0.95);
  }
}

/* 会员选项容器 - 移动到分享会员体验券下方 */
.member-section {
  display: flex;
  align-items: center;
  justify-content: center;
  width: calc(100% - 60rpx); /* 与下方按钮保持一致的宽度 */
  margin: 20rpx auto 10rpx auto; /* 增加顶部间距，与分享体验券保持适当距离 */
  padding: 10rpx 0;
}

/* 积分和提现选项容器 - 同一行 */
.points-withdraw-section {
  display: flex;
  align-items: center;
  justify-content: center;
  width: calc(100% - 60rpx); /* 与下方按钮保持一致的宽度 */
  margin: 0 auto;
  padding: 10rpx 0;
}

/* 全宽度样式 */
.full-width {
  width: 100%;
}

/* 活动选项容器 */
.activity-options-section {
  margin-top: 20rpx;
  padding: 20rpx 0;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 20rpx;
  backdrop-filter: blur(5px);
}

.activity-grid {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 0 20rpx;
}

.activity-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  min-width: 80rpx;

  &:active {
    transform: scale(0.95);
    background: rgba(106, 192, 134, 0.1);
  }
}

.activity-icon {
  margin-bottom: 8rpx;
}

.activity-text {
  font-size: 22rpx;
  color: #666;
  font-weight: 500;
}

/* 统计项目样式 */
.stats-item {
  text-align: center;
  padding: 12rpx 20rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(106, 192, 134, 0.08);
  transition: all 0.3s ease;
  border: 1rpx solid rgba(106, 192, 134, 0.04);

  &:active {
    transform: translateY(-2rpx);
    box-shadow: 0 6rpx 16rpx rgba(106, 192, 134, 0.15);
  }
}

.stats-divider {
  width: 2rpx;
  height: 60rpx;
  background: linear-gradient(to bottom, transparent, #e0e0e0, transparent);
  margin: 0 20rpx;
}

// 🆕 新增：昵称文本样式
.nickname-text {
  display: inline-block;
  max-width: 300rpx;
  word-break: break-all;
  word-wrap: break-word;
  line-height: 1.2;
  vertical-align: top;
  cursor: pointer;
  transition: opacity 0.2s ease;

  &:active {
    opacity: 0.7;
  }

  // 响应式适配
  @media (max-width: 750rpx) {
    max-width: 250rpx;
  }

  @media (max-width: 600rpx) {
    max-width: 200rpx;
  }
}

// 🆕 新增：分会长联系弹窗样式
.branch-contact-popup {
  .branch-contact-container {
    width: 600rpx;
    background: #ffffff;
    border-radius: 24rpx;
    padding: 40rpx 32rpx 32rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  }

  .popup-header {
    margin-bottom: 32rpx;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid #f0f0f0;
  }

  .popup-content {
    min-height: 200rpx;
    margin-bottom: 32rpx;
  }

  .no-branch-container,
  .no-leader-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40rpx 20rpx;

    .info-icon {
      margin-bottom: 20rpx;
    }
  }

  .leader-info-container {
    .branch-info,
    .leader-info {
      margin-bottom: 16rpx;
    }

    .qr-code-section {
      margin-top: 24rpx;
      text-align: center;

      .qr-code-container {
        display: flex;
        justify-content: center;
        margin-top: 16rpx;

        .qr-code-image {
          width: 300rpx;
          height: 300rpx;
          border-radius: 16rpx;
          border: 1rpx solid #f0f0f0;
          background: #fafafa;
        }
      }
    }

    .no-qr-container {
      margin-top: 24rpx;
      padding: 32rpx 20rpx;
      background: #f8f9fa;
      border-radius: 16rpx;
      text-align: center;
    }
  }

  .popup-actions {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20rpx;
    padding-top: 20rpx;
    border-top: 1rpx solid #f0f0f0;
  }
}

// {{ AURA-X: Add - 个人中心背景媒体样式. Confirmed via 寸止. }}
// 个人中心背景媒体样式
.profile-header {
  position: relative;
  width: 100%;
  height: 200rpx; // 默认高度（失败时）
  overflow: hidden;
  background: linear-gradient(135deg, #ffffff 0%, #88D7A0 50%, #6AC086 100%); // 白色到绿色渐变，避免黑色闪烁
  border-radius: 0 0 40rpx 40rpx; // 添加底部圆角

  // 媒体加载成功时的样式
  &.has-media {
    height: 500rpx; // 成功时高度为500rpx
  }
}

// 用户信息卡片样式优化
.user-info-card {
  z-index: 10; // 确保在背景之上
  transition: top 0.3s ease; // 平滑过渡动画
}

.bg-media-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  border-radius: 0 0 40rpx 40rpx; // 与父容器保持一致的圆角
  overflow: hidden;
}

.bg-media-image,
.bg-media-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 隐藏视频组件的所有默认UI元素 */
.bg-media-video {
  background: transparent !important;

  /* 隐藏视频组件内部的加载状态 */
  &::before,
  &::after {
    display: none !important;
  }

  /* 隐藏可能的加载遮罩 */
  .uni-video-cover,
  .uni-video-loading,
  .uni-video-toast {
    display: none !important;
  }
}

.bg-media-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
  z-index: 2;
}

.default-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  border-radius: 0 0 40rpx 40rpx; // 与父容器保持一致的圆角
}

.header-content {
  position: relative;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 0 32rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

</style>