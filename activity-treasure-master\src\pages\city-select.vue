<template>
  <view class="city-select-page">
    <!-- 导航栏 -->
    <view class="nav-bar">
      <view class="nav-left" @click="goBack">
        <u-icon name="arrow-left" size="40rpx" color="#333"></u-icon>
      </view>
      <view class="nav-title">选择城市</view>
      <view class="nav-right"></view>
    </view>

    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-box">
        <u-icon name="search" size="32rpx" color="#999" class="search-icon"></u-icon>
        <input
          class="search-input"
          placeholder="搜索城市名称"
          v-model="searchKeyword"
          @input="onSearchInput"
          confirm-type="search"
          @confirm="onSearch"
        />
        <view v-if="searchKeyword" class="clear-btn" @click="clearSearch">
          <u-icon name="close-circle-fill" size="32rpx" color="#ccc"></u-icon>
        </view>
      </view>
    </view>

    <!-- 当前定位城市 -->
    <view v-if="currentCity && !searchKeyword" class="current-location">
      <view class="section-title">当前定位</view>
      <view class="city-item" @click="selectCity(currentCity)">
        <u-icon name="map-pin" size="32rpx" color="#6AC086" class="location-icon"></u-icon>
        <text class="city-name">{{ currentCity.name }}</text>
      </view>
    </view>

    <!-- 城市列表容器 -->
    <view class="city-list-container">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <u-loading-icon mode="spinner" size="60rpx" color="#6AC086"></u-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 城市列表主体 -->
      <view v-else class="city-content">
        <!-- 搜索结果列表 -->
        <scroll-view
          v-if="searchKeyword"
          class="city-scroll"
          scroll-y
          :style="{ height: scrollHeight + 'rpx' }"
        >
          <view v-if="searchResults.length > 0" class="search-results">
            <view class="section-title">搜索结果</view>
            <view
              v-for="city in searchResults"
              :key="city.adcode"
              class="city-item"
              @click="selectCity(city)"
            >
              <text class="city-name">{{ city.name }}</text>
              <text class="city-pinyin">{{ city.pinyin }}</text>
            </view>
          </view>

          <!-- 搜索无结果 -->
          <view v-else class="no-data">
            <u-icon name="search" size="80rpx" color="#ccc"></u-icon>
            <text class="no-data-text">未找到相关城市</text>
          </view>
        </scroll-view>

        <!-- 字母分组城市列表 -->
        <view v-else class="grouped-city-container">
          <!-- 城市列表滚动区域 -->
          <scroll-view
            class="city-scroll"
            scroll-y
            :style="{ height: scrollHeight + 'rpx' }"
            :scroll-into-view="scrollIntoView"
            @scroll="onScroll"
          >
            <view v-if="Object.keys(groupedCities).length > 0" class="grouped-city-list">
              <view
                v-for="(cities, letter) in groupedCities"
                :key="letter"
                :id="`letter-${letter}`"
                class="letter-group"
              >
                <!-- 字母标题 -->
                <view class="letter-header">{{ letter }}</view>

                <!-- 该字母下的城市列表 -->
                <view class="cities-in-letter">
                  <view
                    v-for="city in cities"
                    :key="city.adcode"
                    class="city-item"
                    @click="selectCity(city)"
                  >
                    <text class="city-name">{{ city.name }}</text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 无数据提示 -->
            <view v-else class="no-data">
              <u-icon name="list" size="80rpx" color="#ccc"></u-icon>
              <text class="no-data-text">暂无城市数据</text>
            </view>
          </scroll-view>

          <!-- 右侧字母索引导航 -->
          <view class="letter-index">
            <view
              v-for="letter in availableLetters"
              :key="letter"
              class="letter-index-item"
              :class="{ active: currentLetter === letter }"
              @click="scrollToLetter(letter)"
            >
              {{ letter }}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 字母提示浮层 -->
    <view v-if="showLetterTip" class="letter-tip">
      {{ currentLetter }}
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { getCityList, getAddr } from '@/api'

// {{ AURA-X: Modify - 重构城市选择页面，支持字母索引导航和分组显示. Confirmed via 寸止. }}
// 响应式数据
const searchKeyword = ref('')
const cities = ref([])
const groupedCities = ref({})
const currentCity = ref(null)
const loading = ref(false)
const scrollHeight = ref(1000)
const scrollIntoView = ref('')
const currentLetter = ref('A')
const showLetterTip = ref(false)
const letterTipTimer = ref(null)

// 计算属性
const searchResults = computed(() => {
  if (!searchKeyword.value) {
    return []
  }
  return cities.value.filter(city =>
    city.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    (city.pinyin && city.pinyin.toLowerCase().includes(searchKeyword.value.toLowerCase()))
  )
})

// 可用的字母列表
const availableLetters = computed(() => {
  return Object.keys(groupedCities.value).sort()
})

// 页面加载
onLoad(() => {
  calculateScrollHeight()
  getCurrentLocation()
  loadCityList()
})

onShow(() => {
  calculateScrollHeight()
})

// 计算滚动区域高度 - 修复废弃API警告
const calculateScrollHeight = async () => {
  await nextTick()
  // 使用新的API替代已废弃的getSystemInfoSync
  const windowInfo = uni.getWindowInfo()
  const windowHeight = windowInfo.windowHeight
  // 导航栏(88rpx) + 搜索框(120rpx) + 当前定位(可选,100rpx) + 标题(80rpx) + 底部安全区域(40rpx)
  const usedHeight = 88 + 120 + (currentCity.value ? 100 : 0) + 80 + 40
  scrollHeight.value = (windowHeight * 2) - usedHeight // 转换为rpx
}

// 获取当前定位
const getCurrentLocation = () => {
  uni.getLocation({
    type: 'wgs84',
    isHighAccuracy: true,
    success: async (res) => {
      try {
        const addrRes = await getAddr({
          longitude: res.longitude,
          latitude: res.latitude
        })
        
        if (addrRes && addrRes.status == 1) {
          const add = addrRes.regeocode.addressComponent
          const cityName = uni.$u.test.isEmpty(add.city) ? add.province : add.city
          // {{ AURA-X: Modify - 统一使用市级adcode格式，包括直辖市. Confirmed via 寸止. }}
          if (add.adcode && add.adcode.length >= 4) {
            const provinceCode = add.adcode.substring(0, 2) + '0000';
            // 直辖市映射到对应的市级adcode
            const municipalityMap = {
              '110000': '110100', // 北京市 → 北京城区
              '120000': '120100', // 天津市 → 天津城区
              '310000': '310100', // 上海市 → 上海城区
              '500000': '500100'  // 重庆市 → 重庆城区
            };

            let cityCode;
            if (municipalityMap[provinceCode]) {
              // 直辖市使用对应的市级adcode
              cityCode = municipalityMap[provinceCode];
            } else {
              // 其他城市使用市级adcode（前4位+00）
              cityCode = add.adcode.substring(0, 4) + '00';
            }

            currentCity.value = {
              adcode: cityCode,
              name: cityName
            }
            calculateScrollHeight()
          }
        }
      } catch (error) {
        console.log('获取定位失败:', error)
      }
    },
    fail: (error) => {
      console.log('定位失败:', error)
    }
  })
}

// 加载城市列表
const loadCityList = async () => {
  try {
    loading.value = true
    // 请求按首字母分组的城市数据
    const res = await getCityList('', 1) // 第二个参数为1表示按首字母分组

    if (res.status === 'ok') {
      if (res.data && typeof res.data === 'object') {
        // 分组数据
        groupedCities.value = res.data
        // 同时保存平铺的城市列表用于搜索
        cities.value = []
        Object.values(res.data).forEach(letterCities => {
          cities.value.push(...letterCities)
        })
      } else {
        // 兼容旧版本API返回的数组格式
        cities.value = res.data || []
        groupCitiesByLetter()
      }
    } else {
      uni.$u.toast(res.msg || '获取城市列表失败')
    }
  } catch (error) {
    console.error('加载城市列表失败:', error)
    uni.$u.toast('网络错误，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 手动按首字母分组（兼容旧版本API）
const groupCitiesByLetter = () => {
  const grouped = {}
  cities.value.forEach(city => {
    const letter = city.first_letter || getFirstLetter(city.name)
    if (!grouped[letter]) {
      grouped[letter] = []
    }
    grouped[letter].push(city)
  })
  groupedCities.value = grouped
}

// 获取中文首字母（简单实现）
const getFirstLetter = (name) => {
  const firstChar = name.charAt(0)
  // 简单的中文首字母映射
  const letterMap = {
    '阿': 'A', '安': 'A', '鞍': 'A',
    '北': 'B', '保': 'B', '包': 'B', '蚌': 'B', '滨': 'B', '亳': 'B', '白': 'B', '百': 'B',
    '成': 'C', '重': 'C', '长': 'C', '常': 'C', '沧': 'C', '承': 'C', '赤': 'C',
    '大': 'D', '东': 'D', '丹': 'D', '德': 'D',
    '佛': 'F', '福': 'F', '抚': 'F', '阜': 'F', '防': 'F',
    '广': 'G', '贵': 'G', '桂': 'G', '赣': 'G',
    '杭': 'H', '哈': 'H', '合': 'H', '海': 'H', '惠': 'H', '邯': 'H', '衡': 'H', '呼': 'H', '葫': 'H', '鹤': 'H', '黑': 'H', '淮': 'H', '湖': 'H', '黄': 'H', '菏': 'H', '怀': 'H', '贺': 'H',
    '济': 'J', '吉': 'J', '锦': 'J', '佳': 'J', '荆': 'J', '九': 'J', '吉': 'J', '景': 'J', '金': 'J', '嘉': 'J',
    '昆': 'K', '开': 'K',
    '兰': 'L', '拉': 'L', '洛': 'L', '柳': 'L', '廊': 'L', '聊': 'L', '临': 'L', '莱': 'L', '辽': 'L', '连': 'L', '丽': 'L', '泸': 'L', '乐': 'L', '凉': 'L', '娄': 'L',
    '绵': 'M', '牡': 'M', '马': 'M', '梅': 'M', '眉': 'M',
    '南': 'N', '宁': 'N', '内': 'N',
    '平': 'P', '盘': 'P', '萍': 'P', '莆': 'P',
    '青': 'Q', '秦': 'Q', '齐': 'Q', '泉': 'Q', '曲': 'Q',
    '日': 'R',
    '上': 'S', '深': 'S', '沈': 'S', '石': 'S', '苏': 'S', '三': 'S', '绍': 'S', '宿': 'S', '遂': 'S',
    '天': 'T', '太': 'T', '唐': 'T', '铁': 'T', '台': 'T', '通': 'T', '泰': 'T',
    '武': 'W', '无': 'W', '温': 'W', '潍': 'W', '威': 'W', '芜': 'W', '梧': 'W',
    '西': 'X', '厦': 'X', '西': 'X', '徐': 'X', '邢': 'X', '信': 'X', '新': 'X', '许': 'X', '孝': 'X', '襄': 'X', '湘': 'X',
    '银': 'Y', '烟': 'Y', '扬': 'Y', '盐': 'Y', '岳': 'Y', '益': 'Y', '永': 'Y', '玉': 'Y', '宜': 'Y', '雅': 'Y',
    '郑': 'Z', '珠': 'Z', '湛': 'Z', '中': 'Z', '张': 'Z', '淄': 'Z', '枣': 'Z', '镇': 'Z', '舟': 'Z', '株': 'Z', '遵': 'Z', '自': 'Z', '资': 'Z'
  }
  return letterMap[firstChar] || 'Z'
}

// 搜索输入处理
const onSearchInput = (e) => {
  searchKeyword.value = e.detail.value
}

// 搜索确认
const onSearch = () => {
  // 实时搜索，无需额外处理
}

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = ''
}

// 选择城市
const selectCity = (city) => {
  // 存储选择的城市到全局状态
  uni.setStorageSync('selectedCity', {
    adcode: city.adcode,
    name: city.name
  })
  
  // 返回首页并传递城市信息
  uni.navigateBack({
    success: () => {
      // 通过事件总线通知首页更新城市
      uni.$emit('citySelected', city)
    }
  })
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 滚动到指定字母
const scrollToLetter = (letter) => {
  scrollIntoView.value = `letter-${letter}`
  currentLetter.value = letter
  showLetterTip.value = true

  // 清除之前的定时器
  if (letterTipTimer.value) {
    clearTimeout(letterTipTimer.value)
  }

  // 1秒后隐藏字母提示
  letterTipTimer.value = setTimeout(() => {
    showLetterTip.value = false
  }, 1000)
}

// 监听滚动事件，更新当前字母
const onScroll = (e) => {
  // 这里可以根据滚动位置计算当前显示的字母
  // 由于微信小程序的限制，这里简化处理
}

// 滚动到底部
const onReachBottom = () => {
  // 如果需要分页加载，在这里实现
}
</script>

<style lang="scss" scoped>
.city-select-page {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  
  .nav-left, .nav-right {
    width: 80rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .nav-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }
}

.search-container {
  padding: 24rpx 32rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  
  .search-box {
    position: relative;
    display: flex;
    align-items: center;
    height: 72rpx;
    background-color: #f5f5f5;
    border-radius: 36rpx;
    padding: 0 32rpx;
    
    .search-icon {
      margin-right: 16rpx;
    }
    
    .search-input {
      flex: 1;
      font-size: 28rpx;
      color: #333;
      
      &::placeholder {
        color: #999;
      }
    }
    
    .clear-btn {
      margin-left: 16rpx;
      padding: 8rpx;
    }
  }
}

.current-location {
  background-color: #fff;
  margin-bottom: 16rpx;
  
  .section-title {
    padding: 24rpx 32rpx 16rpx;
    font-size: 24rpx;
    color: #666;
    background-color: #f8f9fa;
  }
  
  .city-item {
    display: flex;
    align-items: center;
    padding: 24rpx 32rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    .location-icon {
      margin-right: 16rpx;
    }
    
    .city-name {
      font-size: 28rpx;
      color: #333;
    }
  }
}

.city-list-container {
  background-color: #fff;
  flex: 1;

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80rpx 0;

    .loading-text {
      margin-top: 16rpx;
      font-size: 24rpx;
      color: #666;
    }
  }

  .city-content {
    height: 100%;

    // 搜索结果样式
    .search-results {
      .section-title {
        padding: 24rpx 32rpx 16rpx;
        font-size: 24rpx;
        color: #666;
        background-color: #f8f9fa;
      }

      .city-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 24rpx 32rpx;
        border-bottom: 1rpx solid #f0f0f0;

        &:active {
          background-color: #f5f5f5;
        }

        .city-name {
          font-size: 28rpx;
          color: #333;
          flex: 1;
        }

        .city-pinyin {
          font-size: 24rpx;
          color: #999;
          margin-left: 16rpx;
        }
      }
    }

    // 分组城市列表容器
    .grouped-city-container {
      position: relative;
      height: 100%;

      .city-scroll {
        height: 100%;

        .grouped-city-list {
          .letter-group {
            .letter-header {
              position: sticky;
              top: 0;
              z-index: 10;
              padding: 16rpx 32rpx;
              font-size: 24rpx;
              font-weight: 600;
              color: #666;
              background-color: #f8f9fa;
              border-bottom: 1rpx solid #e0e0e0;
            }

            .cities-in-letter {
              .city-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 32rpx;
                border-bottom: 1rpx solid #f0f0f0;
                min-height: 88rpx; // 确保最小触摸区域

                &:active {
                  background-color: #f5f5f5;
                }

                .city-name {
                  font-size: 30rpx;
                  color: #333;
                  font-weight: 500;
                }
              }
            }
          }
        }
      }

      // 右侧字母索引导航
      .letter-index {
        position: fixed;
        right: 16rpx;
        top: 50%;
        transform: translateY(-50%);
        z-index: 100;
        display: flex;
        flex-direction: column;
        align-items: center;
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 24rpx;
        padding: 16rpx 8rpx;
        box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);

        .letter-index-item {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 48rpx;
          height: 48rpx;
          margin: 2rpx 0;
          font-size: 22rpx;
          font-weight: 600;
          color: #666;
          border-radius: 50%;
          transition: all 0.2s ease;

          &.active {
            background-color: #6AC086;
            color: #fff;
            transform: scale(1.1);
          }

          &:active {
            background-color: #6AC086;
            color: #fff;
            transform: scale(0.95);
          }
        }
      }
    }

    // 无数据提示
    .no-data {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 120rpx 0;

      .no-data-text {
        margin-top: 24rpx;
        font-size: 28rpx;
        color: #999;
      }
    }
  }
}

// 字母提示浮层
.letter-tip {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 120rpx;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  font-size: 48rpx;
  font-weight: 600;
  border-radius: 16rpx;
  pointer-events: none;
}
</style>
