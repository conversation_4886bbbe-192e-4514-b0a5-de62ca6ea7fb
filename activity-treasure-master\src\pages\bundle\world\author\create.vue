<script setup>
import { ref, onMounted } from 'vue';
import { createAuthor, upload_img } from '@/api/index.js';
import { store } from '@/store';
import customNavbar from '@/components/customNavbar.vue';

// {{ AURA-X: Add - 创建作者页面. Confirmed via 寸止 }}
const formData = ref({
  name: '',
  category: '',
  description: '',
  birth_year: '',
  death_year: '',
  nationality: '',
  avatar: ''
});

const isSubmitting = ref(false);
const avatarList = ref([]);

// 获取页面参数
const pages = getCurrentPages();
const currentPageInstance = pages[pages.length - 1];
const isSelectMode = currentPageInstance.options.type === 'select';
const presetKeyword = currentPageInstance.options.keyword || '';

// 常用作者类别
const categoryOptions = [
  '作家', '诗人', '哲学家', '思想家', '政治家', 
  '科学家', '艺术家', '音乐家', '历史学家', '经济学家',
  '心理学家', '社会学家', '教育家', '企业家', '其他'
];

// 处理头像上传
const handleAvatarRead = async (event) => {
  let lists = [].concat(event.file);
  
  // 只能上传一张头像
  if (avatarList.value.length > 0) {
    uni.showToast({ title: '只能上传一张头像，将替换现有头像', icon: 'none' });
    avatarList.value = [];
  }
  
  if (lists.length > 1) {
    uni.showToast({ title: '只能上传一张头像，已自动选择第一张', icon: 'none' });
    lists = [lists[0]];
  }
  
  let fileListLen = avatarList.value.length;
  
  lists.map((item) => {
    avatarList.value.push({
      ...item,
      status: 'uploading',
      message: '上传中'
    });
  });
  
  for (let i = 0; i < lists.length; i++) {
    const currentFileIndex = fileListLen + i;
    try {
      const res = await upload_img(lists[i]);
      if (res && res.url) {
        avatarList.value[currentFileIndex].status = 'success';
        avatarList.value[currentFileIndex].url = res.url;
        formData.value.avatar = res.url;
      } else {
        throw new Error('上传失败');
      }
    } catch (error) {
      avatarList.value[currentFileIndex].status = 'failed';
      avatarList.value[currentFileIndex].message = '上传失败';
      uni.showToast({ title: '头像上传失败', icon: 'none' });
    }
  }
};

// 删除头像
const handleDeleteAvatar = (event) => {
  avatarList.value.splice(event.index, 1);
  formData.value.avatar = '';
};

// 选择类别
const selectCategory = () => {
  uni.showActionSheet({
    itemList: categoryOptions,
    success: (res) => {
      formData.value.category = categoryOptions[res.tapIndex];
    }
  });
};

// 提交表单
const submitForm = async () => {
  // 验证必填字段
  if (!formData.value.name.trim()) {
    uni.showToast({ title: '请输入作者姓名', icon: 'none' });
    return;
  }
  
  if (isSubmitting.value) return;
  isSubmitting.value = true;
  
  try {
    const params = {
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token,
      name: formData.value.name.trim(),
      category: formData.value.category.trim(),
      description: formData.value.description.trim(),
      birth_year: formData.value.birth_year.trim(),
      death_year: formData.value.death_year.trim(),
      nationality: formData.value.nationality.trim(),
      avatar: formData.value.avatar
    };
    
    const response = await createAuthor(params);
    
    if (response.status === 'ok') {
      uni.showToast({ title: '创建成功', icon: 'success' });
      
      // 如果是选择模式，发送创建结果给父页面
      if (isSelectMode) {
        const newAuthor = {
          id: response.data.author_id,
          name: formData.value.name,
          category: formData.value.category,
          description: formData.value.description,
          avatar: formData.value.avatar,
          quote_count: 0
        };
        uni.$emit('authorCreated', newAuthor);
      }
      
      setTimeout(() => {
        uni.navigateBack();
      }, 1000);
    } else if (response.status === 'relogin') {
      uni.showToast({ title: '请先登录', icon: 'none' });
    } else {
      uni.showToast({ title: response.msg || '创建失败', icon: 'none' });
    }
  } catch (error) {
    console.error('创建作者失败:', error);
    uni.showToast({ title: '创建失败，请稍后重试', icon: 'none' });
  } finally {
    isSubmitting.value = false;
  }
};

// 页面加载时预填关键词
onMounted(() => {
  if (presetKeyword) {
    formData.value.name = decodeURIComponent(presetKeyword);
  }
});
</script>

<template>
  <view class="create-author-page">
    <!-- 统一导航栏 -->
    <customNavbar 
      :title="isSelectMode ? '创建作者' : '新建作者'" 
      backIcon="arrow-left"
    />
    
    <!-- 表单内容 -->
    <view class="form-container">
      <!-- 头像上传 -->
      <view class="form-section">
        <view class="section-title">头像 (选填)</view>
        <view class="avatar-upload">
          <u-upload
            :fileList="avatarList"
            @afterRead="handleAvatarRead"
            @delete="handleDeleteAvatar"
            name="file"
            :maxCount="1"
            :previewImage="true"
            width="160rpx"
            height="160rpx"
            uploadIconColor="#6AC086"
          ></u-upload>
        </view>
      </view>
      
      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-title">基本信息</view>
        
        <view class="form-item">
          <view class="item-label">姓名 *</view>
          <input 
            v-model="formData.name" 
            placeholder="请输入作者姓名"
            class="item-input"
            maxlength="50"
          />
        </view>
        
        <view class="form-item" @click="selectCategory">
          <view class="item-label">类别</view>
          <view class="item-input" :class="{ 'placeholder': !formData.category }">
            {{ formData.category || '请选择作者类别' }}
          </view>
          <u-icon name="arrow-right" size="16" color="#ccc"></u-icon>
        </view>
        
        <view class="form-item">
          <view class="item-label">国籍</view>
          <input 
            v-model="formData.nationality" 
            placeholder="请输入国籍"
            class="item-input"
            maxlength="30"
          />
        </view>
      </view>
      
      <!-- 时间信息 -->
      <view class="form-section">
        <view class="section-title">时间信息 (选填)</view>
        
        <view class="form-item">
          <view class="item-label">出生年份</view>
          <input 
            v-model="formData.birth_year" 
            placeholder="如：1900"
            class="item-input"
            type="number"
            maxlength="4"
          />
        </view>
        
        <view class="form-item">
          <view class="item-label">逝世年份</view>
          <input 
            v-model="formData.death_year" 
            placeholder="如：1980（在世可不填）"
            class="item-input"
            type="number"
            maxlength="4"
          />
        </view>
      </view>
      
      <!-- 描述信息 -->
      <view class="form-section">
        <view class="section-title">描述信息 (选填)</view>
        
        <view class="form-item">
          <view class="item-label">简介</view>
          <textarea 
            v-model="formData.description" 
            placeholder="请输入作者简介或主要成就"
            class="item-textarea"
            maxlength="500"
            auto-height
          ></textarea>
        </view>
      </view>
    </view>
    
    <!-- 提交按钮 -->
    <view class="submit-section">
      <view class="submit-btn" @click="submitForm" :class="{ 'submitting': isSubmitting }">
        <u-loading-icon v-if="isSubmitting" mode="spinner" color="white" size="20"></u-loading-icon>
        <text class="submit-text" v-if="!isSubmitting">创建作者</text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.create-author-page {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: 120rpx;
  
  .form-container {
    padding: 32rpx;
    
    .form-section {
      background-color: white;
      border-radius: 20rpx;
      padding: 32rpx;
      margin-bottom: 32rpx;
      
      .section-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 32rpx;
      }
      
      .avatar-upload {
        display: flex;
        justify-content: center;
      }
      
      .form-item {
        display: flex;
        align-items: center;
        padding: 24rpx 0;
        border-bottom: 1rpx solid #f0f0f0;
        min-height: 88rpx;
        
        &:last-child {
          border-bottom: none;
        }
        
        .item-label {
          width: 160rpx;
          font-size: 32rpx;
          color: #333;
          flex-shrink: 0;
        }
        
        .item-input {
          flex: 1;
          font-size: 32rpx;
          color: #333;
          
          &.placeholder {
            color: #999;
          }
        }
        
        .item-textarea {
          flex: 1;
          font-size: 32rpx;
          color: #333;
          min-height: 120rpx;
          line-height: 1.6;
        }
      }
    }
  }
  
  .submit-section {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 32rpx;
    background-color: white;
    border-top: 1rpx solid #f0f0f0;
    
    .submit-btn {
      width: 100%;
      height: 88rpx;
      background-color: #6AC086;
      border-radius: 50rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      
      &.submitting {
        opacity: 0.7;
      }
      
      .submit-text {
        font-size: 32rpx;
        color: white;
        font-weight: 500;
        margin-left: 16rpx;
      }
    }
  }
}
</style>
