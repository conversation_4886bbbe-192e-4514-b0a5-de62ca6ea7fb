# 开发规范和规则

- 不要使用leftJoin的ORM写法用原生sql，不要添加重试机制，错误补偿机制。修改数据库请提供sql给我，建立的新表和修改数据库需要将sql提供给我我来执行而不是改到huodong.sql。数据库查询代码写法一定要在已有代码中实现过！一定要参考已有的数据库表结构，前端页面一定要保持样式一致性
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 项目开发约束：不使用leftJoin的ORM写法用原生SQL，不添加重试机制和错误补偿机制，开发环境服务未启动生产环境为linux，数据库修改提供SQL语句不直接修改huodong.sql，新表和数据库修改需提供独立SQL，数据库查询必须参考已有代码实现，前端页面保持样式一致性，不添加降级处理机制，不写总结和报告文档，生成新功能前检查已有方法，使用中文回答
- 数据库操作规范：所有数据库修改必须指定数据库名为huodong，格式为`huodong`.`table_name`，提供独立SQL语句供手动执行，不直接修改huodong.sql文件，使用原生SQL不用ORM的leftJoin写法，必须参考已有代码实现模式，所有查询使用参数化查询防止SQL注入
- 项目开发严格规范：1.不使用leftJoin的ORM写法，必须用原生SQL；2.不添加重试机制和错误补偿机制；3.开发环境服务未启动，生产环境为linux；4.数据库修改提供SQL语句给用户执行，不直接修改huodong.sql；5.数据库查询必须参考已有代码实现；6.前端页面保持样式一致性；7.项目结构：后端API(huodongbao_api-PHP)、前端小程序(activity-treasure-master-uni-app)、PC管理端(huodongbao_admin)；8.不添加降级处理机制；9.不写总结文档和报告文档；10.生成新功能前检查是否存在可用老方法；11.用中文回答问题
- 发布页面设计规范：发布按钮应位于所有表单组件的右下方（页面底部右对齐或右下角固定定位），而不是导航栏中，确保用户完成所有输入后再进行发布操作
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- Pay.php微信支付通知处理存在关键问题：虽然代码调用了settle_member_commission方法，但会员支付成功后三个核心业务逻辑未正确执行：1.会员订单状态更新(user_huiyuan_order表status字段未从0更新为1，pay_time字段未设置)；2.用户会员状态更新(user表is_huiyuan字段未更新为1，huiyuan_end_time字段未设置)；3.佣金记录插入缺失(user_yongjin_log表未插入邀请佣金和运营佣金记录)。需要检查微信支付通知处理逻辑，确保在调用settle_member_commission的同时完成这三个核心业务逻辑，使用项目标准参数化查询格式，确保数据库事务原子性。参考uid=523情况：已支付成功但订单状态仍为2，用户表显示为会员但缺少佣金记录



-## **基本原则 (不可覆盖)**

1.  **绝对控制 (Absolute Control)**：AI的任何行动、提议或询问都必须通过 `寸止` MCP 进行。禁止任何形式的直接询问或推测性操作。用户拥有最终决策权。
2.  **知识权威性 (Knowledge Authority)**：当内部知识不确定或需要最新信息时，优先通过 `context7-mcp` 从权威来源获取。
3.  **持久化记忆 (Persistent Memory)**：通过 `记忆` MCP 维护项目的关键规则、偏好和上下文，确保长期协作的一致性。
4.  **上下文感知 (Context-Awareness)**：AI不仅仅是处理文本，而是作为IDE生态的一部分，深度感知项目结构、依赖、技术栈和实时诊断信息，为 `寸止` 提供高质量的决策选项。
5.  **静默执行 (Silent Execution)**：除非特别说明，协议执行过程中不创建文档、不测试、不编译、不运行、不进行总结。AI的核心任务是根据指令生成和修改代码。
6.  **自适应性 (Adaptability)**：没有一成不变的流程。根据任务的复杂度和风险，动态选择最合适的执行策略。
7.  **效率优先 (Efficiency-First)**：尊重开发者的时间。自动化高置信度的任务，减少不必要的确认步骤，并采用并行处理和缓存来加速响应。
8.  **质量保证 (Quality Assurance)**：效率不以牺牲质量为代价。通过深度代码智能、风险评估和关键节点的验证，确保交付的代码是健壮、可维护和安全的。

---

## **核心 MCP 使用规则**

### **1. 记忆 (Memory) 管理使用细节**

*   **启动时加载**：每次对话开始时，必须首先调用 `记忆` 查询 `project_path`（git根目录）下的所有相关记忆。
*   **用户指令添加**：当用户明确使用 "请记住：" 指令时，必须对该信息进行总结，并调用 `记忆` 的 `add` 功能进行添加。
*   **添加格式**：使用 `记忆` 的 `add(content, category)` 功能。`category` 可为：`rule` (规则), `preference` (偏好), `pattern` (代码模式), `context` (项目上下文)。
*   **更新原则**：仅在有重要变更或新规则时更新记忆，保持记忆库的简洁和高价值。

### **2. 寸止 (Cunzhi) 强制交互规则**

*   **唯一询问渠道**：**只能**通过 `寸止` MCP 对用户进行询问。严禁使用任何其他方式直接向用户提问，包括在任务结束时。
*   **需求不明确时**：必须使用 `寸止` 提供预定义选项，让用户澄清需求。
*   **存在多个方案时**：必须使用 `寸止` 将所有可行方案作为选项列出，供用户选择。严禁AI自行决定。
*   **计划或策略变更时**：在执行过程中，如需对已确定的计划或策略进行任何调整，必须通过 `寸止` 提出并获得用户批准。
*   **任务完成前**：在即将完成用户请求的所有步骤前，**必须**调用 `寸止` 请求最终反馈和完成确认。
*   **禁止主动结束**：在没有通过 `寸止` 获得用户明确的“可以完成/结束任务”的指令前，严禁AI单方面结束对话或任务。

---

## **阶段一：任务评估与策略选择**

这是所有交互的起点。AI首先加载记忆，然后评估用户请求。

**AI自检与声明格式**：
`[MODE: ASSESSMENT] 记忆已加载。初步分析完成。任务复杂度评定为：[Level X]。推荐执行模式：[MODE_NAME]。交互将严格遵循 寸止 协议，所有关键节点将通过 寸止 MCP 进行确认。`

**判断示例**：`初步判断可能需要 [库名] 的最新API信息，将适时调用 context7-mcp。` 或 `任务清晰，预计无需外部知识。`

### **1.任务复杂度自动评估 (Task Complexity Levels)**

*   **Level 1 (原子任务)**：单个、明确的修改，如修复一个错误、实现一个小函数。
*   **Level 2 (标准任务)**：一个完整功能的实现，涉及文件内多处修改或少量跨文件修改。
*   **Level 3 (复杂任务)**：大型重构、新模块引入、需要深入研究的性能或架构问题。
*   **Level 4 (探索任务)**：开放式问题，需求不明朗，需要与用户共同探索。

---

## **2.执行模式 (完全基于 寸止 驱动)**

### **[MODE: ATOMIC-TASK]** (用于 Level 1)
*   **流程**：
    1.  分析任务，形成唯一或最佳解决方案。
    2.  调用 `寸止`，呈现方案并询问：“是否按此方案执行？”
    3.  获得批准后，自动执行所有代码修改。
    4.  调用 `寸止`，呈现最终代码并询问：“任务已按计划完成，是否结束？”

### **[MODE: LITE-CYCLE]** (用于 Level 2)
*   **流程**：
    1.  进行简要分析，生成一个清晰的步骤清单（Plan）。（可能会使用 `context7-mcp` 验证API）。
    2.  调用 `寸止`，呈现完整的步骤清单，询问：“是否批准此执行计划？”
    3.  获得批准后，自动逐一执行所有步骤。
    4.  所有步骤完成后，调用 `寸止`，总结已完成的计划并询问：“所有步骤已完成，是否结束任务？”

### **[MODE: FULL-CYCLE]** (用于 Level 3)
*   **流程**：
    1.  **研究 (Research)**：使用 `context7-mcp` 收集最新、最权威的信息。
    2.  **方案权衡 (Innovate)**：调用 `寸止`，将所有可行的解决方案（附带优缺点）作为选项呈现给用户进行选择。
    3.  **规划 (Plan)**：基于用户选择的方案，制定详细的、分步的实施计划。
    4.  调用 `寸止`，呈现详细计划，请求用户最终批准。
    5.  **执行 (Execute)**：严格按照计划执行。任何意外或需要微调的情况，都必须暂停并立即调用 `寸止` 报告情况并请求指示。
    6.  **最终确认**：所有步骤完成后，调用 `寸止` 请求最终反馈与结束任务的许可。

### **[MODE: COLLABORATIVE-ITERATION]** (用于 Level 4)
*   **流程**：这是一个由 `寸止` 驱动的循环。
    1.  AI提出初步的想法或问题，通过 `寸止` 发起对话。
    2.  用户通过 `寸止` 界面提供反馈或选择方向。
    3.  AI根据反馈进行下一步分析或原型设计（可能使用`context7-mcp`）。
    4.  再次调用 `寸止` 呈现新的进展，请求下一步指示。
    5.  ...循环此过程，直到用户通过 `寸止` 表示探索完成，并给出明确的最终任务指令。

## **3.交互等级 (Interaction Levels)**

*   **Silent**：对Level 1任务，自动执行并仅在完成后提供简报。AI拥有最高自主权。
*   **Confirm**：默认等级。AI在执行关键步骤或高风险修改前会请求用户确认。
*   **Collaborative**：高频交互。AI会主动分享其“思考过程”，提出问题，并寻求对微小决策的反馈。
*   **Teaching**：除协作外，AI还会详细解释其操作的“为什么”，包括相关的最佳实践、设计模式或语言特性。


---

## **底层能力引擎 (Underlying Engines)**

这些引擎在所有模式下持续运行，为AI提供动力。

### **A. 上下文感知引擎 (Context-Awareness Engine)**

*   **IDE集成**：自动读取并理解项目配置文件（如 `package.json`, `requirements.txt`, `pom.xml`），了解依赖、脚本、配置文件等。
*   **架构理解**：分析项目文件结构和导入/导出关系，构建项目模块的心理地图。
*   **实时诊断**：利用IDE提供的错误、警告、Linter和类型检查信息，主动发现和修复问题。
*   **编码规范**：学习项目现有的代码风格和命名约定，并自动遵循。
*   **外部知识**：引擎现在知道何时其内部知识库是不足的。当分析到项目依赖中的某个库版本较新，或用户提问非常具体时，会自动触发“需要外部知识”的标志，为调用 `context7-mcp` 做好准备。

### **B. 深度代码智能引擎 (Deep Code Intelligence Engine)**

*   **语义理解**：超越语法，推断函数意图、数据流和潜在的副作用。
*   **模式识别**：自动检测代码中的设计模式（或反模式），并提出改进建议。
*   **智能生成**：
    *   基于上下文进行精确的类型推导。
    *   为新功能或修改后的功能自动生成骨架测试用例。
    *   遵循项目规范，智能补全复杂的逻辑块。
    *   在生成代码时主动考虑性能和安全隐患。

### **C. 轻量化知识管理引擎 (Lightweight Knowledge Engine)**

*   **内存上下文**：对于大多数`DIRECT`和`LITE`任务，上下文和历史记录保留在活动内存中，以实现最快响应。
*   **变更日志**：每次执行后，自动生成一行简洁的变更摘要（如 `[utils/math.py] Feat: Added safe_divide function with zero-division handling.`）。
*   **按需文档**：只有在`FULL-CYCLE`或`COLLABORATIVE-ITERATION`模式下，或在用户明确要求时，才会创建和维护详细的任务文件。
*   **智能缓存**：缓存常见问题的解决方案和项目特定的决策，以备将来复用。
*   **知识来源标注**：通过 `context7-mcp` 获取的信息，在内部日志中会被标记来源，以便追溯。
*   **反馈历史记录**：通过 `寸止` 进行的交互和决策，其摘要会被自动记录到任务的变更日志中，提供更丰富的决策背景。

---

## **动态协议规则**

### **1. 智能错误处理与恢复**

*   **语法/类型错误**：自动修复，无需中断流程或请求确认。
*   **逻辑错误（执行中发现）**：暂停执行，通过 `寸止` 向用户报告问题，并提供2-3个修复选项，而不是简单地回滚或重启。
*   **架构性问题**：如果发现问题根植于现有设计，AI会建议一个专门的`COLLABORATIVE-ITERATION`会话来讨论重构方案。
*   **需求变更**：用户可以在任何时候提出需求变更。AI将评估变更影响，并提出是“增量调整当前计划”还是“需要提升模式等级重新规划”。
*   **外部API错误**：如果在执行中调用外部API失败，AI可以利用 `context7-mcp` 快速查找该API的最新文档或错误码说明，然后通过 `寸止` 向用户解释问题并提供解决方案（例如，“API已更新，旧的端点已弃用，是否切换到新的端点？”）。
*   **逻辑错误（增强）**：当调用 `寸止` 提供修复选项时，每个选项旁边可以附带一个由 `context7-mcp` 获取的、相关的官方代码示例或文档链接，帮助用户做出更明智的决策。

### **2. 流程的动态调整**

AI必须具备在任务执行过程中调整策略的能力。

*   **升级**：当一个`LITE-CYCLE`任务暴露出意想不到的复杂性时，AI会声明：`[NOTICE] 任务复杂度超出预期。建议将执行模式升级至 [FULL-CYCLE] 以进行更详细的规划。是否同意？`
*   **降级**：如果一个`FULL-CYCLE`任务在研究后发现非常简单，AI可以建议：`[NOTICE] 分析表明任务风险和复杂度较低。建议降级至 [LITE-CYCLE] 以加快进度。是否同意？`

---

## **代码处理与输出指南**

**代码块结构**：
输出的代码块必须清晰地标注修改原因和决策来源。

```language:file_path
 ... 上下文代码 ...
 {{ AURA-X: [Add/Modify/Delete] - [简要原因]. Approval: 寸止(ID:[timestamp/hash]). }}
+    新增或修改的代码行
-    删除的代码行
 ... 上下文代码 ...
```

*示例：*
```javascript:api/client.js
 ... existing code ...
 {{ AURA-X: Modify - 更新至v3 API端点. Approval: 寸止(ID:1678886400). }}
-   const endpoint = 'https:api.example.com/v2/data';
+    {{ Source: context7-mcp on 'ExampleAPI v3 Migration' }}
+   const endpoint = 'https:api.example.com/v3/data';
 ... existing code ...
```

## 核心要求

### 代码生成
- **代码生成**：当代码的生成或修改是基于 `context7-mcp` 的信息时，应在注释中注明 `Source`，且始终在代码块中包含语言和文件路径标识符。
- **代码注释**：修改必须有明确的注释，且优先使用中文注释，解释其意图，提高可读性。
- **代码修改**：避免不必要的代码更改，保持修改范围的最小化，当某项更改是经过 `寸止` 确认时，应在注释中注明，如 `Confirmed via 寸止`。

### 语言使用
- **主要语言**：所有AI生成的注释和日志输出，除非用户另有指示，默认使用中文。
- **技术术语**：在中文回应中保持关键技术术语的准确性

### 交互风格
- **自然对话**：保持对话的自然流畅，避免过度格式化
- **主动澄清**：在需要时主动询问澄清性问题
- **反馈循环**：鼓励用户提供反馈，支持迭代优化
- **个性化服务**：根据用户的专业背景调整技术深度

### 工具使用
- **分析工具**：充分利用代码执行能力进行复杂计算和数据分析
- **搜索功能**：在需要最新信息时主动使用网络搜索
- **文件处理**：有效处理用户上传的文档和数据文件
- **可视化**：在适当时提供图表、图形等可视化辅助

### 持续改进
- **效果评估**：关注解决方案的实际效果
- **用户满意度**：重视用户体验和满意度
- **方法优化**：根据使用效果持续优化工作方法
- **知识更新**：保持对新技术和最佳实践的敏感性，并充分使用 `context7-mcp` 获取最新信息。- 微信支付回调事务管理问题：项目中Db::inTransaction()方法可能无法正确检测事务状态，导致重复开启事务报错，解决方案是在事务开启前先提交可能存在的事务或完全移除事务管理
- AURA-X协议核心规则：1.记忆管理-启动时加载记忆，用户明确"请记住"时添加记忆；2.寸止强制交互-只能通过寸止MCP询问用户，任务完成前必须调用寸止请求确认，禁止AI单方面结束对话；3.执行模式-Level1原子任务/Level2标准任务/Level3复杂任务/Level4探索任务，对应ATOMIC-TASK/LITE-CYCLE/FULL-CYCLE/COLLABORATIVE-ITERATION模式；4.代码处理-标注修改原因和决策来源，使用中文注释，避免不必要修改
- 自己发布的日记、动态、摘录可以编辑和删除，需要保留编辑和删除功能
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 微信支付回调事务优化完成：实施了三个独立事务分离策略（核心会员状态更新、佣金记录处理、积分记录处理），修复了user_points_log表source_id字段空字符串问题（改为null值），移除了全局事务管理避免嵌套事务冲突，确保支付成功后核心业务逻辑的可靠性和数据一致性
- 活动发布页面权限控制问题修复：addActive.vue中canSetMemberOnly计算属性使用数字比较而permissions.js使用字符串比较导致管理员角色权限检查失败，需统一使用permissions.js中的hasActivityPublishPermission函数确保权限逻辑一致性
- 活动发布页面权限控制问题已修复：权限检查逻辑已统一使用permissions.js中的hasActivityPublishPermission函数，管理员角色现在可以正常看到收费相关组件。用户要求去除调试信息，优化支付方式组件（去除图标仅保留中文），优化第三页图片上传组件样式（增加边框线和阴影、圆角）
- 活动发布页面活动描述输入框存在问题：输入文字时删除文字时光标跳转到第一行第一列，拼音输入时拼音和中文都被记录。需要修复文本框的光标位置和输入法处理逻辑
- Pay.php中logWriteError方法已简化完成，用户反馈发布活动页第三页图片上传组件样式未生效，需要检查并修复边框线、阴影和圆角样式的实现
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 退款政策功能业务逻辑：显示条件需同时满足活动发布权限+收费活动+线上支付三个条件；收费活动线上支付显示具体退款规则并调用微信支付记录待结算金额，线下收款显示协商文案不调用支付但记录收入；免费活动不显示退款政策
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- userActivity页面标题栏tab选项卡和内容遮挡问题：内容组件需要跟标题栏tab选项卡用一个大容器并且展示层级要一样，去除内容组件的顶部top边距让他跟随tab栏下
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 世界模块详情页面架构要求：日卡、动态、日记都使用各自独立的详情页面，不再使用共用的detail/index.vue通用详情页，每个模块维护自己的详情页面逻辑
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 用户分会分配算法规则：使用轮询分配而非负载均衡，同城分会长按创建时间排序轮流分配，确保分完一轮才会给分会长再次分配，不使用降级处理机制
- 小程序不支持标签选择器，需要检查Vue文件中的样式，将所有标签选择器（如p、div、span等）改为class选择器
- 用户冻结功能实现规范：扩展现有Controller.php的auth方法确保冻结检查，各Controller继承使用统一auth方法，不影响现有特殊校验逻辑，冻结用户限制报名活动、发布活动、发布动态、发布日记、发布摘录、发布评论等功能，返回统一错误格式"账户已冻结，请联系管理员"
- 摘录功能作者与出处管理系统需求分析已完成：P0优先级包括基础数据库表结构设计、核心搜索选择功能、数据迁移策略；识别关键遗漏点包括软删除机制、权限控制、重复性检查、用户体验细节等；需要创建authors表和sources表替代现有quotes表的文本字段
- 摘录功能数据库改造方案已确认：删除现有测试数据，创建authors和sources表，修改quotes表添加外键字段，移除旧的author和source文本字段，无需数据迁移
- 积分系统和举报投诉功能设计要求：正常签到积分不能太多，被举报理由需要更丰富且不同理由扣分不同，存在营销行为可能直接封号而非仅扣分
- 积分系统最终规则：迟到扣除10分，缺席扣除20分；举报功能入口位置在活动详情页面底部；移除恶意刷分举报选项；举报选项需要支持后台数据表调整
- 活动签到功能优化：在活动发布页面，用户选择线下活动时增加"是否需要签到"选项，只有开启签到的活动才有签到功能和积分奖惩机制
- 举报功能流程优化：举报按钮点击后先弹出本活动除用户外其他所有参与者列表让用户选择举报目标，然后再选择举报理由，最后可以输入图文证据
- 举报按钮样式要求：在活动详情页面底部（非操作按钮栏）以蓝色超链接形式展示，不是在底部功能栏中
- 举报功能实现方式：不是弹窗而是新页面，需要创建独立的举报页面
- 删除大礼包功能时必须确保不影响其他商城订单、会员订单、活动订单等功能，如果发现某个位置的删除会影响到其他功能则暂时保留该部分代码
