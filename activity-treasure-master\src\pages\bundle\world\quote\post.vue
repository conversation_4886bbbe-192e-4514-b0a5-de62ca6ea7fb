<script setup>
import { ref } from 'vue';
import { publishQuote, upload_img } from '@/api/index.js';
import { store } from '@/store';
import customNavbar from '@/components/customNavbar.vue';
import AuthorSelector from '@/components/AuthorSelector.vue';
import SourceSelector from '@/components/SourceSelector.vue';

// --- State Refs ---
const content = ref('');
// {{ AURA-X: Modify - 更新作者和出处为对象类型支持新的选择器. Confirmed via 寸止 }}
const selectedAuthor = ref(null); // 选中的作者对象
const selectedSource = ref(null); // 选中的出处对象
const tags = ref('');
const privacy = ref('public');
const allowOfficial = ref(false); // 是否允许官方使用
const isSubmitting = ref(false);
const images = ref([]); // 图片列表，摘录最多上传1张

// --- Event Handlers ---
const handleClose = () => {
    uni.navigateBack();
};

// 处理图片上传
const handleAfterRead = async (event) => {
    let lists = [].concat(event.file);

    // 摘录只能上传一张图片，如果已有图片则替换
    if (images.value.length > 0) {
        uni.showToast({ title: '摘录只能上传一张图片，将替换现有图片', icon: 'none' });
        images.value = []; // 清空现有图片
    }

    // 只取第一张图片
    if (lists.length > 1) {
        uni.showToast({ title: '摘录只能上传一张图片，已自动选择第一张', icon: 'none' });
        lists = [lists[0]];
    }

    let fileListLen = images.value.length;

    lists.map((item) => {
        images.value.push({
            ...item,
            status: 'uploading',
            message: '上传中'
        });
    });

    for (let i = 0; i < lists.length; i++) {
        const currentFileIndex = fileListLen + i;
        try {
            const res = await upload_img(lists[i].url);

            if (res.status === 'ok' && res.data) {
                let item = images.value[currentFileIndex];
                if (item) {
                    images.value.splice(currentFileIndex, 1, {
                        ...item,
                        status: 'success',
                        message: '',
                        url: res.data
                    });
                }
            } else {
                if (images.value[currentFileIndex]) {
                    images.value[currentFileIndex].status = 'failed';
                    images.value[currentFileIndex].message = res.msg || '上传失败';
                }
                uni.showToast({ title: res.msg || '图片上传失败', icon: 'none' });
            }
        } catch (error) {
            if (images.value[currentFileIndex]) {
                images.value[currentFileIndex].status = 'failed';
                images.value[currentFileIndex].message = '上传失败';
            }
            uni.showToast({ title: '图片上传失败，请重试', icon: 'none' });
        }
    }
};

// 删除图片
const handleDeletePic = (event) => {
    images.value.splice(event.index, 1);
};

const handleSubmit = async () => {
    // 验证必填字段
    if (!content.value.trim()) {
        uni.showToast({ title: '摘录内容不能为空', icon: 'none' });
        return;
    }

    // 验证用户登录状态
    if (!store().$state.userInfo?.uid || !store().$state.userInfo?.token) {
        uni.showToast({ title: '请先登录', icon: 'none' });
        return;
    }

    // 获取所有上传成功的图片URL
    const uploadedImageUrls = images.value
        .filter(img => img.status === 'success' && img.url)
        .map(img => img.url);

    // 验证图片上传 - 摘录必须上传一张图片
    if (uploadedImageUrls.length === 0) {
        uni.showToast({ title: '摘录必须上传一张图片', icon: 'none' });
        return;
    }

    if (isSubmitting.value) return;

    isSubmitting.value = true;

    // {{ AURA-X: Modify - 更新参数格式支持新的外键字段. Confirmed via 寸止 }}
    // 准备参数 - 与后端API保持一致
    const params = {
        uid: store().$state.userInfo.uid,
        token: store().$state.userInfo.token,
        content: content.value.trim(),
        author_id: selectedAuthor.value?.id || null, // 使用作者ID
        source_id: selectedSource.value?.id || null, // 使用出处ID
        tags: tags.value.trim(),
        privacy: privacy.value,
        allow_official: allowOfficial.value ? 1 : 0, // 添加给官方投稿字段
        images: uploadedImageUrls // 添加图片参数
    };

    try {
        const res = await publishQuote(params);
        if (res.status === 'ok') {
            uni.showToast({ title: '摘录成功', icon: 'success' });

            // {{ AURA-X: Add - 发布成功后触发摘录列表刷新. Confirmed via 寸止 }}
            // 触发摘录列表刷新
            uni.$emit('refreshQuoteList');

            setTimeout(() => {
                uni.navigateBack();
            }, 1000);
        } else if (res.status === 'relogin') {
            uni.showToast({ title: '请先登录', icon: 'none' });
        } else {
            uni.showToast({ title: res.msg || '摘录失败', icon: 'none' });
        }
    } catch (error) {
        uni.showToast({ title: '摘录失败，请重试', icon: 'none' });
    } finally {
        isSubmitting.value = false;
    }
};

</script>

<template>
    <view class="quote-post-page">
        <!-- 统一导航栏 -->
        <customNavbar
            title="摘录"
            backIcon="close"
            @back="handleClose"
        />

        <!-- Main Content Area -->
        <scroll-view scroll-y class="main-content">

            <!-- Content Textarea -->
            <view class="textarea-wrapper">
                 <u--textarea
                    v-model="content"
                    placeholder="记录书摘格言，名家语录"
                    height="300"
                    maxlength="-1"
                    border="none"
                    :customStyle="{ padding: '32rpx', lineHeight: '1.7', fontSize: '32rpx', color: '#333333', fontFamily: '-apple-system, BlinkMacSystemFont, PingFang SC, Hiragino Sans GB, sans-serif', backgroundColor: 'transparent' }"
                ></u--textarea>
            </view>

            <!-- Image Upload -->
            <view class="upload-wrapper">
                <u-upload
                    :fileList="images"
                    @afterRead="handleAfterRead"
                    @delete="handleDeletePic"
                    name="file"
                    multiple
                    :maxCount="1"
                    :previewImage="true"
                    width="200rpx"
                    height="200rpx"
                    uploadIconColor="#ccc"
                ></u-upload>
                <view class="upload-tip">
                    <text class="tip-text">摘录必须上传1张图片</text>
                </view>
            </view>

            <!-- Meta Info Section -->
            <view class="meta-section">
                <!-- {{ AURA-X: Modify - 替换为新的选择器组件. Confirmed via 寸止 }} -->
                <!-- 作者选择器 -->
                <AuthorSelector
                    v-model="selectedAuthor"
                    placeholder="选择作者 (选填)"
                />

                <!-- 出处选择器 -->
                <SourceSelector
                    v-model="selectedSource"
                    placeholder="选择出处 (选填)"
                />
                 <view class="meta-item">
                     <u-icon name="tags-fill" size="20" color="#999"></u-icon>
                      <input class="meta-input" v-model="tags" placeholder="标签 (选填, 逗号分隔)" />
                 </view>
                 <view class="meta-item">
                     <u-icon name="lock-fill" size="20" color="#999"></u-icon>
                     <text class="meta-label">谁可以看</text>
                      <view class="privacy-switch">
                         <u-radio-group v-model="privacy" placement="row">
                            <u-radio label="公开" name="public" :customStyle="{marginRight: '16rpx'}"></u-radio>
                            <u-radio label="私密" name="private"></u-radio>
                        </u-radio-group>
                     </view>
                 </view>
                 <view class="meta-item">
                     <u-icon name="checkmark-circle-fill" size="20" color="#999"></u-icon>
                     <text class="meta-label">给官方投稿</text>
                     <view class="official-switch">
                         <u-switch v-model="allowOfficial" activeColor="#6AC086" size="24"></u-switch>
                     </view>
                 </view>
            </view>
        </scroll-view>

        <!-- 发布按钮 - 固定在页面底部右侧 -->
        <view class="publish-button-container">
            <view
                class="publish-btn"
                :class="{ 'disabled': isSubmitting || !content.trim() }"
                @click="handleSubmit"
            >
                <u-icon name="checkmark" size="44rpx" color="#ffffff" v-if="!isSubmitting"></u-icon>
                <u-loading-icon v-if="isSubmitting" color="#ffffff" size="40rpx"></u-loading-icon>
                <text class="publish-text" v-if="!isSubmitting">摘录</text>
            </view>
        </view>

    </view>
</template>

<style lang="scss" scoped>
/* 统一设计变量 */
:root {
  --spacing-md: 24rpx;
  --spacing-lg: 32rpx;
  --radius-card: 20rpx;
  --radius-button: 50rpx;
  --color-bg-page: #f8f9fa;
  --color-bg-card: #ffffff;
  --color-text-title: #333333;
  --color-text-body: #666666;
  --color-text-caption: #999999;
  --shadow-card: 0 12rpx 32rpx rgba(106, 192, 134, 0.15);
}

.quote-post-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--color-bg-page);
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', sans-serif;
}

/* 发布按钮容器 */
.publish-button-container {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  z-index: 1000;
}

/* 发布按钮样式 */
.publish-btn {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #88D7A0 0%, #6AC086 100%);
  border-radius: var(--radius-button);
  box-shadow: var(--shadow-card);
  transition: all 0.3s ease;

  .publish-text {
    font-size: 24rpx;
    color: #ffffff;
    margin-top: 8rpx;
    font-weight: 500;
  }

  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  &:active {
    transform: scale(0.95);
  }
}

/* {{ AURA-X: Modify - 去除卡片样式，扩大输入区域. Confirmed via 寸止 }} */
.main-content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
    padding-bottom: 200rpx; /* 为底部发布按钮留出空间 */
    background-color: #ffffff;
    width: 100%;
    box-sizing: border-box;
}

.textarea-wrapper {
    margin-bottom: 0;
    background-color: transparent;
    border-radius: 0;
    padding: 0;
    box-shadow: none;
    min-height: 50vh;

    :deep(.u-textarea) {
        font-size: 32rpx;
        color: var(--color-text-body);
        line-height: 1.7;

        .u-textarea__field::placeholder {
            color: var(--color-text-caption);
            font-size: 32rpx;
        }
    }
}

/* {{ AURA-X: Modify - 去除图片上传卡片样式. Confirmed via 寸止 }} */
.upload-wrapper {
    margin-bottom: var(--spacing-lg);
    background-color: transparent;
    border-radius: 0;
    padding: var(--spacing-lg);
    box-shadow: none;

    :deep(.u-upload__wrap) {
        gap: var(--spacing-md);
    }

    :deep(.u-upload__button) {
        background-color: var(--color-bg-page);
        border: 2rpx dashed #e0e0e0;
        border-radius: var(--radius-card);
        transition: all 0.3s ease;
    }

    :deep(.u-upload__item) {
        border-radius: var(--radius-card);
        overflow: hidden;
        box-shadow: var(--shadow-card);
    }

    /* {{ AURA-X: Modify - 缩小提示条高度和字体. Confirmed via 寸止 }} */
    .upload-tip {
        margin-top: var(--spacing-md);
        padding: 16rpx var(--spacing-md);
        background-color: #fff3cd;
        border: 1rpx solid #ffeaa7;
        border-radius: var(--radius-card);

        .tip-text {
            font-size: 22rpx;
            color: #856404;
            line-height: 1.3;
        }
    }
}

/* {{ AURA-X: Modify - 去除元数据区域卡片样式. Confirmed via 寸止 }} */
.meta-section {
    background-color: transparent;
    border-radius: 0;
    box-shadow: none;
    overflow: hidden;
    padding: var(--spacing-lg);

    /* {{ AURA-X: Modify - 修改元数据项样式与其他页面保持一致. Confirmed via 寸止 }} */
    .meta-item {
        display: flex;
        align-items: center;
        padding: var(--spacing-lg);
        border-bottom: 1rpx solid #f0f0f0;
        transition: background-color 0.3s ease;
        background-color: #ffffff;
        border-radius: 12rpx;
        margin-bottom: 16rpx;

        &:last-child {
            border-bottom: none;
        }

        .meta-input {
             flex: 1;
             margin-left: var(--spacing-md);
             font-size: 28rpx;
             color: var(--color-text-body);
             text-align: right;
             background: transparent;
             border: none;
             outline: none;
             width: 100%;
             max-width: 300rpx;

             &::placeholder {
                 color: var(--color-text-caption);
             }
        }
        .meta-label {
            margin-left: var(--spacing-md);
            font-size: 28rpx;
            color: var(--color-text-body);
        }
        .privacy-switch {
            margin-left: auto;
            
            :deep(.u-radio) {
                font-size: 28rpx;
                
                .u-radio__label {
                    color: #666666;
                }
            }
            
            :deep(.u-radio--checked) {
                .u-radio__label {
                    color: #6AC086;
                    font-weight: 500;
                }
            }
            
            :deep(.u-radio__icon-wrap--checked) {
                background-color: #6AC086 !important;
                border-color: #6AC086 !important;
            }
        }

        .official-switch {
            margin-left: auto;
        }
    }
}
</style>