<script setup>
import { ref, watch, onUnmounted } from 'vue';
import { navto } from '@/utils';

// {{ AURA-X: Add - 创建作者选择器组件. Confirmed via 寸止 }}
const props = defineProps({
  modelValue: {
    type: Object,
    default: null
  },
  placeholder: {
    type: String,
    default: '选择作者'
  }
});

const emit = defineEmits(['update:modelValue']);

const selectedAuthor = ref(props.modelValue);

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  selectedAuthor.value = newVal;
});

// 打开作者搜索页面
const openSelector = () => {
  navto('/pages/bundle/world/author/search?type=select');
};

// 清除选择
const clearSelection = (event) => {
  event.stopPropagation();
  selectedAuthor.value = null;
  emit('update:modelValue', null);
};

// 监听全局事件，接收选择结果
uni.$on('authorSelected', (author) => {
  selectedAuthor.value = author;
  emit('update:modelValue', author);
});

// 组件销毁时移除事件监听
onUnmounted(() => {
  uni.$off('authorSelected');
});
</script>

<template>
  <view class="author-selector" @click="openSelector">
    <view class="selector-content" :class="{ 'has-value': selectedAuthor }">
      <u-icon name="account-fill" size="20" color="#999" class="icon"></u-icon>
      
      <view v-if="selectedAuthor" class="selected-item">
        <image 
          v-if="selectedAuthor.avatar" 
          :src="selectedAuthor.avatar" 
          class="avatar"
          mode="aspectFill"
        ></image>
        <view v-else class="avatar-placeholder">
          {{ selectedAuthor.name.charAt(0) }}
        </view>
        <view class="author-info">
          <text class="name">{{ selectedAuthor.name }}</text>
          <text v-if="selectedAuthor.category" class="category">{{ selectedAuthor.category }}</text>
        </view>
        <u-icon 
          name="close-circle-fill" 
          @click="clearSelection" 
          class="clear-btn"
          size="18"
          color="#ccc"
        ></u-icon>
      </view>
      
      <view v-else class="placeholder">
        <text class="placeholder-text">{{ placeholder }}</text>
        <u-icon name="arrow-right" size="16" color="#ccc" class="arrow"></u-icon>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.author-selector {
  width: 100%;
  
  .selector-content {
    display: flex;
    align-items: center;
    padding: 24rpx 0;
    min-height: 88rpx; // 最小触摸区域
    border-bottom: 1rpx solid #f0f0f0;
    
    .icon {
      margin-right: 24rpx;
      flex-shrink: 0;
    }
    
    .selected-item {
      display: flex;
      align-items: center;
      flex: 1;
      
      .avatar {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        margin-right: 24rpx;
        flex-shrink: 0;
      }
      
      .avatar-placeholder {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        background-color: #6AC086;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
        font-weight: 500;
        margin-right: 24rpx;
        flex-shrink: 0;
      }
      
      .author-info {
        flex: 1;
        
        .name {
          display: block;
          font-size: 32rpx;
          color: #333;
          font-weight: 500;
          line-height: 1.4;
        }
        
        .category {
          display: block;
          font-size: 24rpx;
          color: #999;
          margin-top: 4rpx;
        }
      }
      
      .clear-btn {
        margin-left: 16rpx;
        flex-shrink: 0;
      }
    }
    
    .placeholder {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
      
      .placeholder-text {
        font-size: 32rpx;
        color: #999;
      }
      
      .arrow {
        flex-shrink: 0;
      }
    }
    
    &.has-value {
      background-color: #f8f9fa;
      border-radius: 12rpx;
      padding: 24rpx 32rpx;
      border-bottom: none;
    }
  }
}
</style>
