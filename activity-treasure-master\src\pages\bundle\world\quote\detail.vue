<script setup>
import { ref, onMounted } from 'vue';
import { getQuoteDetail, getQuoteComments, postQuoteComment, likeQuote, favoriteQuote } from '@/api/index.js';
import { store } from '@/store';
import { navto } from '@/utils';
import { requireLogin } from '@/utils/auth';
import { onLoad } from '@dcloudio/uni-app';

// 状态管理
const quote = ref(null);
const loading = ref(true);
const showComments = ref(false);
const comments = ref([]);
const commentsLoading = ref(false);
const commentText = ref('');
const quoteId = ref(0);

// 加载摘录详情
const loadQuoteDetail = async () => {
  try {
    loading.value = true;
    
    const res = await getQuoteDetail({
      quote_id: quoteId.value,
      uid: store().$state.userInfo?.uid || 0,
      token: store().$state.userInfo?.token || ''
    });

    if (res.status === 'ok') {
      quote.value = res.data;
    } else {
      uni.showToast({ title: res.msg || '加载失败', icon: 'none' });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  } catch (error) {
    console.error('加载摘录详情失败:', error);
    uni.showToast({ title: '加载失败，请重试', icon: 'none' });
  } finally {
    loading.value = false;
  }
};

// 加载评论列表
const loadComments = async () => {
  try {
    commentsLoading.value = true;
    
    const res = await getQuoteComments({
      quote_id: quoteId.value,
      page: 1,
      page_size: 50,
      uid: store().$state.userInfo?.uid || 0,
      token: store().$state.userInfo?.token || ''
    });

    if (res.status === 'ok') {
      comments.value = res.data.list || [];
    } else if (res.status === 'empty') {
      comments.value = [];
    } else {
      uni.showToast({ title: res.msg || '加载评论失败', icon: 'none' });
    }
  } catch (error) {
    console.error('加载评论失败:', error);
    uni.showToast({ title: '加载评论失败', icon: 'none' });
  } finally {
    commentsLoading.value = false;
  }
};

// 点赞摘录
const handleLike = async () => {
  if (!requireLogin('', '请先登录后再点赞')) {
    return;
  }

  if (!quote.value) return;

  try {
    const res = await likeQuote({
      id: quote.value.id,
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token
    });

    if (res.status === 'ok') {
      quote.value.is_liked = !quote.value.is_liked;
      quote.value.like_count = quote.value.is_liked ? 
        (quote.value.like_count || 0) + 1 : 
        (quote.value.like_count || 1) - 1;
      
      uni.showToast({
        title: quote.value.is_liked ? '点赞成功' : '取消点赞',
        icon: 'success'
      });
    } else {
      uni.showToast({ title: res.msg || '操作失败', icon: 'none' });
    }
  } catch (error) {
    console.error('点赞失败:', error);
    uni.showToast({ title: '网络错误，请稍后重试', icon: 'none' });
  }
};

// 收藏摘录
const handleFavorite = async () => {
  if (!requireLogin('', '请先登录后再收藏')) {
    return;
  }

  if (!quote.value) return;

  try {
    const res = await favoriteQuote({
      id: quote.value.id,
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token
    });

    if (res.status === 'ok') {
      quote.value.is_favorited = !quote.value.is_favorited;
      quote.value.favorite_count = quote.value.is_favorited ? 
        (quote.value.favorite_count || 0) + 1 : 
        (quote.value.favorite_count || 1) - 1;
      
      uni.showToast({
        title: quote.value.is_favorited ? '收藏成功' : '取消收藏',
        icon: 'success'
      });
    } else {
      uni.showToast({ title: res.msg || '操作失败', icon: 'none' });
    }
  } catch (error) {
    console.error('收藏失败:', error);
    uni.showToast({ title: '网络错误，请稍后重试', icon: 'none' });
  }
};

// 发布评论
const submitComment = async () => {
  if (!requireLogin('', '请先登录后再评论')) {
    return;
  }

  if (!commentText.value.trim()) {
    uni.showToast({ title: '请输入评论内容', icon: 'none' });
    return;
  }

  try {
    const res = await postQuoteComment({
      quote_id: quoteId.value,
      content: commentText.value.trim(),
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token
    });

    if (res.status === 'ok') {
      commentText.value = '';
      uni.showToast({ title: '评论成功', icon: 'success' });
      // 重新加载评论列表
      loadComments();
    } else {
      uni.showToast({ title: res.msg || '评论失败', icon: 'none' });
    }
  } catch (error) {
    console.error('评论失败:', error);
    uni.showToast({ title: '网络错误，请稍后重试', icon: 'none' });
  }
};

// 切换评论区显示
const toggleComments = () => {
  showComments.value = !showComments.value;
  if (showComments.value && comments.value.length === 0) {
    loadComments();
  }
};

// 页面加载
onLoad((options) => {
  quoteId.value = parseInt(options.id);
  if (!quoteId.value) {
    uni.showToast({ title: '摘录ID无效', icon: 'none' });
    uni.navigateBack();
    return;
  }

  loadQuoteDetail();
  
  // 如果URL参数指定显示评论区
  if (options.showComments === 'true') {
    showComments.value = true;
    loadComments();
  }
});

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '';
  const date = new Date(timeStr.replace(/-/g, '/'));
  const now = new Date();
  const diff = now - date;
  
  if (diff < 60000) return '刚刚';
  if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
  if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
  if (diff < 2592000000) return Math.floor(diff / 86400000) + '天前';

  return timeStr.split(' ')[0];
};

// 关闭页面
const closePage = () => {
  uni.navigateBack();
};
</script>

<template>
  <view class="quote-detail-page">
    <!-- 顶部导航栏 -->
    <view class="quote-header">
      <view class="back-button" @click="closePage">
        <u-icon name="arrow-left" color="#333" size="20"></u-icon>
      </view>
      <text class="header-title">摘录详情</text>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <u-loading-icon mode="circle" size="30" color="#6AC086"></u-loading-icon>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 摘录详情 -->
    <view v-else-if="quote" class="quote-detail">
      <!-- 摘录内容 -->
      <view class="quote-content">
        <text class="quote-text">{{ quote.content }}</text>
        <view class="quote-meta">
          <text class="author">—— {{ quote.author }}</text>
          <text class="source" v-if="quote.source">《{{ quote.source }}》</text>
        </view>
      </view>

      <!-- 摘录图片 -->
      <view v-if="quote.images && quote.images.length > 0" class="quote-images">
        <image 
          v-for="(img, index) in quote.images" 
          :key="index"
          :src="img"
          class="quote-image"
          mode="aspectFill"
          @click="previewImage(img)"
        />
      </view>

      <!-- 用户信息 -->
      <view class="user-info">
        <image :src="quote.user?.avatar_url || '/static/default-avatar.png'" class="user-avatar" />
        <text class="user-name">{{ quote.user?.nickname || '匿名用户' }}</text>
        <text class="publish-time">{{ formatTime(quote.created_at) }}</text>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <view class="action-btn" @click="handleLike">
          <u-icon 
            :name="quote.is_liked ? 'heart-fill' : 'heart'" 
            :color="quote.is_liked ? '#ff4757' : '#999'"
            size="20"
          />
          <text class="action-text">{{ quote.like_count || 0 }}</text>
        </view>
        
        <view class="action-btn" @click="handleFavorite">
          <u-icon 
            :name="quote.is_favorited ? 'star-fill' : 'star'" 
            :color="quote.is_favorited ? '#ffa502' : '#999'"
            size="20"
          />
          <text class="action-text">{{ quote.favorite_count || 0 }}</text>
        </view>
        
        <view class="action-btn" @click="toggleComments">
          <u-icon name="chat" color="#999" size="20" />
          <text class="action-text">{{ comments.length }}</text>
        </view>
      </view>

      <!-- 评论区 -->
      <view v-if="showComments" class="comments-section">
        <view class="comments-header">
          <text class="comments-title">评论 ({{ comments.length }})</text>
        </view>

        <!-- 评论列表 -->
        <scroll-view
          class="comments-list"
          scroll-y
        >
          <view v-if="comments.length === 0 && !commentsLoading" class="empty-comments">
            <text>暂无评论，快来发表第一条评论吧</text>
          </view>

          <view v-for="(comment, index) in comments" :key="comment.id" class="comment-item">
            <image class="comment-avatar" :src="comment.user?.avatar || '/static/default-avatar.png'" mode="aspectFill"></image>
            <view class="comment-content">
              <view class="comment-header">
                <text class="comment-nickname">{{ comment.user?.nickname || '用户' }}</text>
                <text class="comment-time">{{ formatTime(comment.created_at) }}</text>
              </view>
              <view class="comment-text">
                <text>{{ comment.content }}</text>
              </view>
            </view>
          </view>

          <view v-if="commentsLoading" class="loading-more">
            <u-loading-icon mode="circle" size="24" color="#999"></u-loading-icon>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 评论输入框 -->
    <view v-if="showComments" class="comment-input-area">
      <view class="input-wrapper">
        <input
          class="comment-input"
          v-model="commentText"
          placeholder="写下你的评论..."
          confirm-type="send"
          @confirm="submitComment"
        />
        <button class="send-btn" @click="submitComment">发送</button>
      </view>
    </view>
  </view>
</template>

<style scoped>
.quote-detail-page {
  min-height: 100vh;
  background: #f8f9fa;
  padding-top: calc(88rpx + var(--status-bar-height) + 20rpx); /* 为导航栏留出空间 */
  padding-left: 20rpx;
  padding-right: 20rpx;
  padding-bottom: 20rpx;
}

/* 导航栏样式 */
.quote-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  padding-top: calc(var(--status-bar-height) + 20rpx);
  padding-bottom: 20rpx;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(88rpx + var(--status-bar-height));
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 1000;
}

.back-button {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.header-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  gap: 20rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

.quote-detail {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.quote-content {
  margin-bottom: 30rpx;
}

.quote-text {
  font-size: 32rpx;
  line-height: 1.6;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.quote-meta {
  text-align: right;
}

.author {
  font-size: 28rpx;
  color: #666;
  font-style: italic;
}

.source {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}

.quote-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-bottom: 30rpx;
}

.quote-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 10rpx;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
}

.user-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.publish-time {
  font-size: 24rpx;
  color: #999;
  margin-left: auto;
}

.action-buttons {
  display: flex;
  gap: 40rpx;
  margin-bottom: 30rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  background: #f5f5f5;
}

.action-text {
  font-size: 24rpx;
  color: #666;
}

.comments-section {
  padding: 30rpx;
  background-color: #fff;
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1px solid #f5f5f5;
}

.comments-title {
  font-weight: 600;
  font-size: 32rpx;
  color: #333;
}

.comments-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.empty-comments {
  padding: 60rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

.comment-item {
  display: flex;
  padding: 24rpx 0;
  border-bottom: 1px solid #f5f5f5;
}

.comment-avatar {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  border: 1px solid #f0f0f0;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  align-items: center;
}

.comment-nickname {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-right: 12rpx;
}

.comment-time {
  font-size: 22rpx;
  color: #999;
  flex: 1;
}

.comment-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 10rpx;
  word-break: break-all;
}

.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx;
}

.comment-input-area {
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.input-wrapper {
  display: flex;
  align-items: center;
}

.comment-input {
  flex: 1;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
}

.send-btn {
  margin-left: 20rpx;
  background-color: #576b95; /* 微信蓝色 */
  color: #fff;
  border: none;
  border-radius: 40rpx;
  padding: 0 30rpx;
  height: 80rpx;
  font-size: 28rpx;
}
</style>
