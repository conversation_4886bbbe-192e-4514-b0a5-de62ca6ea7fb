<?php
namespace core;

class Controller{

	static public $config = null;

	public function __construct(){
		self::init();
	}

	public static function init(){
		
	}

	public static function auth($uid,$token){
		if(!check($uid,"intgt0") || strlen($token) != 32) {
            return false;
        }
		dbConn();
		$uid = (int)$uid;
		// 验证token
		$check = Db()->table("user")->select("token,openid,unionid,is_dongjie")->where("uid={$uid}")->fetch();

		// 检查用户是否存在
		if(empty($check) || !isset($check['token'])) {
			return false;
		}

		// 检查token是否匹配
		if($check['token'] !== $token) {
			return false;
		}

		// 检查用户是否被冻结 - 直接返回错误并终止
		if($check['is_dongjie'] != 0) {
			responseType("json");
			echo json_encode(["status" => "error", "msg" => "账户已冻结，请联系管理员"]);
			exit;
		}

		// 更新用户最后活动时间，延长会话有效期
		// 每次验证通过都会更新last_active_time字段为当前时间
		$last_active_time = date('Y-m-d H:i:s');
		Db()->table("user")->where("uid={$uid}")->update(["last_active_time" => $last_active_time]);

		return true;
	}
	
	public static function user_log($uid,$msg){
		dbConn();
		$dirName = basename(BASE_PATH);
		$sql = "INSERT INTO `user_log` (`uid`,`msg`,`ip`) VALUES ({$uid},:msg,'".IP."')";
		$msg = "{$dirName} : [ ".Route::$privileges." ] : " . htmlspecialchars($msg);
		$msg = mb_substr($msg,0,150);
		$params = [':msg'=>$msg];
		if(\core\Db::_exec($sql,$params)){
			return true;
		}
		return false;
	}
	
	public static function exception_log($msg){
		dbConn();
		$dirName = basename(BASE_PATH);
		$msg = "{$dirName} : [ ".Route::$privileges." ] : " .htmlspecialchars($msg);
		$msg = mb_substr($msg,0,600);
		$sql = "INSERT INTO `exception_log` (`msg`,`ip`) VALUES (:msg,'".IP."')";
		$params = [':msg'=>$msg];
		if(\core\Db::_exec($sql,$params)){
			return true;
		}
		return false;
	}
	
	public static function get_config($name){
		dbConn();
		if(is_string($name)){
			$res = Db()->table("config")->where("name=:name")->prepareParam([":name"=>$name])->getColumn("val",false);
			return $res;
		}else if(is_array($name)){
			$data = [];
			foreach($name as $v){
				$res = Db()->table("config")->where("name=:name")->prepareParam([":name"=>$v])->getColumn("val",false);
				if($res !== false){
					$data[$v] = $res;
				}
			}
			return $data;
		}
		return false;
	}
	
	public function __destruct(){

	}
}
