/**
 * 默认数据模块 - 提供API请求失败时的兜底数据
 */

// 默认日卡数据
export const defaultCards = [
  {
    id: 'default-1',
    title: '晨光微露',
    description: '清晨的第一缕阳光总是最温柔的，它轻抚着大地，唤醒沉睡的万物。每一个新的开始，都值得我们用心去感受。',
    author: '张晓风',
    source: '春之怀古',
    image: '/static/images/cards/default-1.jpg',
    tags: ['自然', '感悟'],
    created_at: new Date().toISOString(),
    liked: false
  },
  {
    id: 'default-2',
    title: '城市漫步',
    description: '在熙攘的街头慢慢走着，看着来来往往的人群，每个人都有自己的故事。这座城市的温度，藏在每一个不经意的瞬间里。',
    author: '余华',
    source: '活着',
    image: '/static/images/cards/default-2.jpg',
    tags: ['城市', '生活'],
    created_at: new Date().toISOString(),
    liked: false
  },
  {
    id: 'default-3',
    title: '咖啡时光',
    description: '午后的咖啡馆里，阳光斜斜地洒在桌案上。一杯拿铁，一本好书，时间在这里变得格外缓慢而美好。',
    author: '村上春树',
    source: '挪威的森林',
    image: '/static/images/cards/default-3.jpg',
    tags: ['咖啡', '阅读'],
    created_at: new Date().toISOString(),
    liked: false
  },
  {
    id: 'default-4',
    title: '雨夜思绪',
    description: '雨滴敲打着窗棂，夜色如墨。在这样的夜晚，思绪总是特别清晰，仿佛能听见内心最真实的声音。',
    author: '三毛',
    source: '雨季不再来',
    image: '/static/images/cards/default-4.jpg',
    tags: ['雨夜', '思考'],
    created_at: new Date().toISOString(),
    liked: false
  },
  {
    id: 'default-5',
    title: '山间小径',
    description: '沿着蜿蜒的山路向上攀登，每一步都是对自己的超越。山顶的风景固然美丽，但路上的风景同样珍贵。',
    author: '汪曾祺',
    source: '人间草木',
    image: '/static/images/cards/default-5.jpg',
    tags: ['登山', '坚持'],
    created_at: new Date().toISOString(),
    liked: false
  },
  {
    id: 'default-6',
    title: '夜空繁星',
    description: '抬头仰望星空，那些闪烁的星辰仿佛在诉说着宇宙的秘密。在浩瀚的星海面前，我们都是渺小而珍贵的存在。',
    author: '刘慈欣',
    source: '三体',
    image: '/static/images/cards/default-6.jpg',
    tags: ['星空', '宇宙'],
    created_at: new Date().toISOString(),
    liked: false
  }
];

// 情感标签
export const emotionTags = [
  '励志', '治愈', '伤感', '喜悦', '思考', '平静'
];

// 主题标签
export const themeTags = [
  '爱情', '成长', '友情', '亲情', '旅行', '生活', '工作', '阅读', '音乐', '电影'
];

// 默认背景颜色（与情感标签对应）
export const tagColors = {
  '励志': '#FFF3E0',
  '治愈': '#E8F5E9',
  '伤感': '#F5F5F5',
  '喜悦': '#FFF8E1',
  '思考': '#E0F7FA',
  '平静': '#F3E5F5',
  '爱情': '#FFE0E6',
  '成长': '#E8F5E9',
  '友情': '#E3F2FD',
  '亲情': '#FFEBEE',
  '旅行': '#E0F2F1',
  '生活': '#F9FBE7',
  '工作': '#E8EAF6',
  '阅读': '#FFF8E1',
  '音乐': '#F3E5F5',
  '电影': '#E0F7FA'
};

// 提供一个获取随机卡片的方法
export function getRandomCards(count = 3) {
  const shuffled = [...defaultCards].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

// 提供一个根据标签筛选卡片的方法
export function filterCardsByTag(tag) {
  return defaultCards.filter(card => card.tags.includes(tag));
} 