<script setup>
import { ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app'; // 引入 onLoad
import CardIndex from '@/pages/bundle/world/card/index.vue';
import FeedIndex from '@/pages/bundle/world/feed/index.vue';
import DiaryIndex from '@/pages/bundle/world/diary/index.vue';
import QuoteIndex from '@/pages/bundle/world/quote/index.vue';
import { getDailyCards } from '@/api/index.js'; // 引入 API
import { store } from '@/store'; // 引入 store
import { navto } from '@/utils';
import { requireLogin } from '@/utils/auth'; // 引入 navto
import CustomTabBar from '../components/CustomTabBar.vue';

// 显式注册组件以避免微信小程序的组件加载问题
const components = {
  CardIndex,
  FeedIndex,
  DiaryIndex,
  QuoteIndex,
  CustomTabBar
};

// {{ AURA-X: Add - 添加子组件ref引用，用于按需调用数据加载方法. Confirmed via 寸止. }}
// 子组件引用
const feedIndexRef = ref(null);
const diaryIndexRef = ref(null);
const quoteIndexRef = ref(null);

// 调试：检查组件是否正确导入
console.log('world.vue: 组件导入检查', {
  CardIndex: !!CardIndex,
  FeedIndex: !!FeedIndex,
  DiaryIndex: !!DiaryIndex,
  QuoteIndex: !!QuoteIndex
});

// 当前选中的 Tab 索引 (0: 日卡, 1: 动态)
const currentTab = ref(0);
// Tab 列表数据
const tabs = ref([
  { name: '日卡' },
  { name: '动态' },
  { name: '日记' },
  { name: '摘录' }
]);

// --- Card Data State (Moved from CardIndex) ---
const dailyCardsData = ref([]);
const isCardLoading = ref(true);
const cardError = ref('');

// FAB State
const showFabOptions = ref(false);
const isFabRotated = ref(false);

// --- Utility Functions (Moved from CardIndex - formatYYYYMMDD needed here) ---
/**
 * 格式化日期为 YYYY-MM-DD
 * @param {Date} date - Date 对象
 * @returns {string} 格式化后的日期字符串
 */
const formatYYYYMMDD = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

/**
 * 加载日卡数据
 */
const loadCardData = async () => {
    // Optional: Prevent re-fetch if data exists and not explicitly refreshing
    // if (dailyCardsData.value.length > 0 && !forceRefresh) return;
    console.log("world.vue: loadCardData triggered"); // 添加日志
    isCardLoading.value = true;
    cardError.value = '';
    try {
        // --- 修改日期范围 ---
        const endDate = new Date(); // 结束日期为今天
        const startDate = new Date();
        startDate.setDate(endDate.getDate() - 15); // 开始日期为今天往前15天
        // --- 结束修改 ---

        const params = {
            startDate: formatYYYYMMDD(startDate),
            endDate: formatYYYYMMDD(endDate),
            uid: store().$state.userInfo?.uid || 0,
            token: store().$state.token || ''
        };

        console.log("world.vue: Calling getDailyCards with params:", params); // 添加日志

        // 添加重试逻辑
        let retryCount = 0;
        const maxRetries = 3;
        let res;

        while (retryCount < maxRetries) {
            try {
                res = await getDailyCards(params);
                console.log("world.vue: API response:", res); // 添加日志
                break; // 如果成功获取数据，跳出循环
            } catch (retryError) {
                retryCount++;
                console.error(`world.vue: Retry ${retryCount}/${maxRetries} failed:`, retryError);
                if (retryCount >= maxRetries) {
                    throw retryError; // 重试次数用完，抛出错误
                }
                // 等待一段时间再重试
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        if (res.status === 'ok' && res.data?.cards) {
          console.log("world.vue: Successfully received card data:", res.data.cards);
          console.log("world.vue: Card data length:", res.data.cards.length);
          if (res.data.cards.length > 0) {
            console.log("world.vue: First card sample:", res.data.cards[0]);
          }
          // 按日期排序
          dailyCardsData.value = res.data.cards.sort((a, b) => new Date(a.date) - new Date(b.date));
        } else if (res.status === 'empty') {
            console.log("world.vue: API returned empty status");
            dailyCardsData.value = [];
            cardError.value = '暂无日卡数据';
        } else {
          console.warn("world.vue: API returned error status:", res.status, res.msg);
          dailyCardsData.value = [];
          cardError.value = res.msg || '获取日卡失败';
        }
    } catch (error) {
        console.error('world.vue: Error loading card data:', error);
        dailyCardsData.value = [];
        // 检查错误是否是因为 API 函数未定义
        if (error.message.includes('getDailyCards is not a function') || error.message.includes('undefined')) {
            cardError.value = 'API函数缺失，请手动修复api/index.js';
        } else {
            cardError.value = '加载失败，请稍后重试';
        }

        // 不使用模拟数据，保持空状态
        dailyCardsData.value = [];
    } finally {
        isCardLoading.value = false;
    }
}



/**
 * 处理 Tab 切换事件
 * @param {number | object} indexOrEvent - u-tabs 切换事件返回的索引或事件对象
 */
const handleTabChange = (indexOrEvent) => {
  // u-tabs 返回的可能是索引值，也可能是包含 index 的对象
  const index = typeof indexOrEvent === 'number' ? indexOrEvent : indexOrEvent.index;

  // 防止重复切换到同一个tab
  if (currentTab.value === index) {
    console.log(`Tab ${index} 已经是当前选中状态，跳过切换`);
    return;
  }

  console.log(`Tab切换: ${currentTab.value} -> ${index}`);
  currentTab.value = index;

  // 根据tab索引加载对应数据
  switch(index) {
    case 0: // 日卡
      console.log('切换到日卡tab，加载日卡数据');
      loadCardData();
      break;
    case 1: // 动态
      console.log('切换到动态tab，加载动态数据');
      loadFeedData();
      break;
    case 2: // 日记
      console.log('切换到日记tab，加载日记数据');
      loadDiaryData();
      break;
    case 3: // 摘录
      console.log('切换到摘录tab，加载摘录数据');
      loadQuoteData();
      break;
    default:
      console.warn(`未知的tab索引: ${index}`);
      break;
  }
};

// {{ AURA-X: Modify - 修改为调用子组件的数据加载方法. Confirmed via 寸止. }}
// 加载动态数据（修复实现）
const loadFeedData = async () => {
  try {
    console.log('开始加载动态数据');

    // 检查用户登录状态
    const userInfo = store().$state.userInfo;
    if (!userInfo || !userInfo.uid) {
      console.warn('用户未登录，但仍可查看动态数据');
      // 允许未登录用户查看动态，但某些功能会受限
    }

    // 调用子组件的数据加载方法
    if (feedIndexRef.value && feedIndexRef.value.loadFeedData) {
      console.log('调用FeedIndex组件的loadFeedData方法');
      feedIndexRef.value.loadFeedData();
    } else {
      console.warn('FeedIndex组件ref未准备好');
    }

  } catch (error) {
    console.error('加载动态数据失败:', error);
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    });
  }
};

// {{ AURA-X: Modify - 修改为调用子组件的数据加载方法. Confirmed via 寸止. }}
// 加载日记数据（在当前页面内切换）
const loadDiaryData = async () => {
  try {
    console.log('world.vue: 切换到日记tab，加载日记数据');

    // 检查用户登录状态
    const userInfo = store().$state.userInfo;
    if (!userInfo || !userInfo.uid) {
      console.warn('world.vue: 用户未登录，但仍可查看日记数据');
      // 允许未登录用户查看日记，但某些功能会受限
    }

    // 调用子组件的数据加载方法
    if (diaryIndexRef.value && diaryIndexRef.value.loadDiaryData) {
      console.log('调用DiaryIndex组件的loadDiaryData方法');
      diaryIndexRef.value.loadDiaryData();
    } else {
      console.warn('DiaryIndex组件ref未准备好');
    }

  } catch (error) {
    console.error('world.vue: 加载日记数据失败:', error);
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    });
  }
};

// {{ AURA-X: Modify - 修改为调用子组件的数据加载方法. Confirmed via 寸止. }}
// 加载摘录数据（在当前页面内切换）
const loadQuoteData = async () => {
  try {
    console.log('world.vue: 切换到摘录tab，加载摘录数据');

    // 检查用户登录状态
    const userInfo = store().$state.userInfo;
    if (!userInfo || !userInfo.uid) {
      console.warn('world.vue: 用户未登录，但仍可查看摘录数据');
      // 允许未登录用户查看摘录，但某些功能会受限
    }

    // 调用子组件的数据加载方法
    if (quoteIndexRef.value && quoteIndexRef.value.loadQuoteData) {
      console.log('调用QuoteIndex组件的loadQuoteData方法');
      quoteIndexRef.value.loadQuoteData();
    } else {
      console.warn('QuoteIndex组件ref未准备好');
    }

  } catch (error) {
    console.error('world.vue: 加载摘录数据失败:', error);
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    });
  }
};

// FAB Actions
const toggleFabMenu = () => {
    console.log('FAB按钮被点击，当前状态:', { isFabRotated: isFabRotated.value, showFabOptions: showFabOptions.value });
    isFabRotated.value = !isFabRotated.value;
    showFabOptions.value = !showFabOptions.value;
    console.log('FAB按钮状态更新后:', { isFabRotated: isFabRotated.value, showFabOptions: showFabOptions.value });
};

const closeFabMenu = () => {
    isFabRotated.value = false;
    showFabOptions.value = false;
};

const handleFabOptionClick = (option) => {
    console.log('FAB Option Clicked:', option);
    closeFabMenu(); // Close menu after selection

    // 使用统一的登录校验
    if (!requireLogin('', '请先登录后再发布内容')) {
        return;
    }

    switch (option) {
        case 'feed':
            try {
                navto('/pages/bundle/world/feed/post');
            } catch (navError) {
                console.error('导航到动态发布页面失败:', navError);
                uni.showToast({
                    title: '页面跳转失败',
                    icon: 'none'
                });
            }
            break;
        case 'diary':
            try {
                console.log('跳转到日记发布页面');
                navto('/pages/bundle/world/diary/post');
            } catch (navError) {
                console.error('导航到日记发布页面失败:', navError);
                uni.showToast({
                    title: '页面跳转失败',
                    icon: 'none'
                });
            }
            break;
        case 'quote':
            try {
                console.log('跳转到摘录发布页面');
                navto('/pages/bundle/world/quote/post');
            } catch (navError) {
                console.error('导航到摘录发布页面失败:', navError);
                uni.showToast({
                    title: '页面跳转失败',
                    icon: 'none'
                });
            }
            break;
        default:
            console.warn('未知的FAB选项:', option);
    }
};

// --- Lifecycle Hooks ---
onLoad(() => {
    console.log("world.vue: onLoad triggered. Current tab:", currentTab.value);
    console.log("world.vue: Store state:", {
        userInfo: store().$state.userInfo,
        token: store().$state.token,
        uid: store().$state.userInfo?.uid
    });

    // {{ AURA-X: Modify - 默认只加载日卡数据，按需加载其他数据. Confirmed via 寸止. }}
    // 默认只加载日卡数据，其他数据在切换tab时按需加载
    console.log("world.vue: Loading card data on onLoad (default)");
    loadCardData();
});

</script>

<template>
  <view class="world-page-container" @click.self="closeFabMenu">
    <!-- 页面内层包裹，用于整体添加 padding-top -->
    <view class="world-inner-wrapper">
      <!-- 顶部粘性区域，包含 Tab 导航 -->
      <!-- Removed :customStyle="{ 'margin-top': '40rpx' }" -->
      <u-sticky offset-top="0">
        <!-- Tab 导航栏 -->
        <!-- Removed style="padding-top: 10rpx;" -->
        <view class="world-navbar">
          <!-- uview-plus 的 Tab 组件 -->
          <u-tabs
            :list="tabs"
            :current="currentTab"
            @change="handleTabChange"
            lineColor="#FFD700"
            :activeStyle="{
              color: '#000000',
              fontWeight: 'bold'
            }"
            :inactiveStyle="{
              color: '#666666'
            }"
            itemStyle="padding-left: 15px; padding-right: 15px; height: 48px;"
          ></u-tabs>
        </view>
      </u-sticky>

      <!-- 内容区域，根据选中的 Tab 显示不同组件 -->
      <view class="world-content">
        <!-- 使用 v-show 保持组件实例，避免v-if导致的渲染问题 -->
        <view v-show="currentTab === 0" class="tab-content">
          <!-- 日卡页面组件 - 传递 Props -->
          <CardIndex
            :cards="dailyCardsData"
            :loading="isCardLoading"
            :error="cardError"
          />
        </view>
        <view v-show="currentTab === 1" class="tab-content">
          <!-- 动态页面组件 -->
          <FeedIndex ref="feedIndexRef" />
        </view>
        <view v-show="currentTab === 2" class="tab-content">
          <!-- 日记页面组件 -->
          <DiaryIndex ref="diaryIndexRef" />
        </view>
        <view v-show="currentTab === 3" class="tab-content">
          <!-- 摘录页面组件 -->
          <QuoteIndex ref="quoteIndexRef" />
        </view>
      </view>

      <!-- New FAB Container -->
      <view class="fab-container">
          <!-- Options (conditionally rendered) -->
          <view v-if="showFabOptions" class="fab-options">
              <view class="fab-option option-feed" :class="{ visible: showFabOptions }" @click="handleFabOptionClick('feed')">
                  <u-icon name="chat" color="#6AC086" size="20"></u-icon>
                  <text class="fab-option-text">动态</text>
              </view>
              <view class="fab-option option-diary" :class="{ visible: showFabOptions }" @click="handleFabOptionClick('diary')">
                  <u-icon name="edit-pen" color="#6AC086" size="20"></u-icon>
                  <text class="fab-option-text">日记</text>
              </view>
              <view class="fab-option option-quote" :class="{ visible: showFabOptions }" @click="handleFabOptionClick('quote')">
                  <u-icon name="bookmark" color="#6AC086" size="20"></u-icon>
                  <text class="fab-option-text">摘录</text>
              </view>
          </view>

          <!-- Main FAB Button -->
          <view class="fab-main" :class="{ rotated: isFabRotated }" @click.stop="toggleFabMenu">
              <u-icon name="edit-pen" color="#ffffff" size="28"></u-icon>
          </view>
      </view>

    </view>

    <!-- 自定义底部导航栏 -->
    <CustomTabBar :current="1" />
  </view>
</template>

<style lang="scss" scoped>
.world-page-container {
  display: flex;
  flex-direction: column;
  height: 100vh; // Ensure full height
  background-color: #F8F9FA; // Use light gray background from spec
  // 为自定义底部导航栏预留空间 - 优化：考虑发布按钮额外高度
  padding-bottom: calc(140rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(140rpx + env(safe-area-inset-bottom));
  // 确保页面可以完整滚动
  box-sizing: border-box;
}

.world-inner-wrapper {
    padding-top: 50rpx; // 整体内容下移 50rpx
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden; // Prevent potential double scrollbars
}

.world-navbar {
  background-color: #FFFFFF; // White background for navbar
  // Add border bottom if needed, u-tabs might have its own
   border-bottom: 1px solid #f0f0f0;
}

.world-content {
  flex: 1; // Takes remaining height
  overflow-y: auto; // Allows scrolling within content area if needed
}

.tab-content {
  height: 100%;
  width: 100%;
}

.fab-container {
  position: fixed;
  right: 40rpx;
  // 修复：上移50rpx并提高z-index确保在底部导航栏之上
  bottom: calc(170rpx + env(safe-area-inset-bottom)); // 原120rpx + 50rpx上移
  z-index: 1100; // 提高层级，确保在底部导航栏(z-index: 1000)之上
  display: flex;
  flex-direction: column;
  align-items: center;
}

.fab-main {
  width: 112rpx;
  height: 112rpx;
  background: linear-gradient(135deg, #84fab0, #8fd3f4); // Green/Blue gradient
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1); // Smooth rotation with overshoot
  z-index: 1101; // 提高层级，确保按钮可点击
  // 添加点击区域保护
  pointer-events: auto;

  &.rotated {
    transform: rotate(135deg);
  }
}

.fab-options {
    position: absolute;
    bottom: 130rpx; // Position above the main FAB
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 25rpx; // Space between option buttons
    z-index: 1100; // 确保选项菜单也在底部导航栏之上
}

.fab-option {
    width: 88rpx;
    height: 88rpx;
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    opacity: 0;
    transform: scale(0.5) translateY(20px);
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    // 确保选项按钮可点击
    pointer-events: auto;
    z-index: 1101;

    // Staggered animation delay
    &.option-feed { transition-delay: 0.05s; }
    &.option-diary { transition-delay: 0.1s; }
    &.option-quote { transition-delay: 0.15s; }

    &.visible {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.fab-option-text {
    font-size: 20rpx;
    color: #6AC086;
    margin-top: 4rpx;
    font-weight: 500;
}

/* Remove or comment out the old .fab-post style if it exists */
// .fab-post { ... }
</style>
