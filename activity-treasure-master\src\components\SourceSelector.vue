<script setup>
import { ref, watch, onUnmounted } from 'vue';
import { navto } from '@/utils';

// {{ AURA-X: Add - 创建出处选择器组件. Confirmed via 寸止 }}
const props = defineProps({
  modelValue: {
    type: Object,
    default: null
  },
  placeholder: {
    type: String,
    default: '选择出处'
  }
});

const emit = defineEmits(['update:modelValue']);

const selectedSource = ref(props.modelValue);

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  selectedSource.value = newVal;
});

// 打开出处搜索页面
const openSelector = () => {
  navto('/pages/bundle/world/source/search?type=select');
};

// 清除选择
const clearSelection = (event) => {
  event.stopPropagation();
  selectedSource.value = null;
  emit('update:modelValue', null);
};

// 监听全局事件，接收选择结果
uni.$on('sourceSelected', (source) => {
  selectedSource.value = source;
  emit('update:modelValue', source);
});

// 组件销毁时移除事件监听
onUnmounted(() => {
  uni.$off('sourceSelected');
});
</script>

<template>
  <view class="source-selector" @click="openSelector">
    <view class="selector-content" :class="{ 'has-value': selectedSource }">
      <u-icon name="bookmark-fill" size="20" color="#999" class="icon"></u-icon>
      
      <view v-if="selectedSource" class="selected-item">
        <image 
          v-if="selectedSource.cover_image" 
          :src="selectedSource.cover_image" 
          class="cover"
          mode="aspectFill"
        ></image>
        <view v-else class="cover-placeholder">
          <u-icon name="bookmark" size="24" color="#6AC086"></u-icon>
        </view>
        <view class="source-info">
          <text class="name">{{ selectedSource.name }}</text>
          <text v-if="selectedSource.publisher" class="publisher">{{ selectedSource.publisher }}</text>
          <text v-else-if="selectedSource.category" class="category">{{ selectedSource.category }}</text>
        </view>
        <u-icon 
          name="close-circle-fill" 
          @click="clearSelection" 
          class="clear-btn"
          size="18"
          color="#ccc"
        ></u-icon>
      </view>
      
      <view v-else class="placeholder">
        <text class="placeholder-text">{{ placeholder }}</text>
        <u-icon name="arrow-right" size="16" color="#ccc" class="arrow"></u-icon>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.source-selector {
  width: 100%;
  
  .selector-content {
    display: flex;
    align-items: center;
    padding: 24rpx 0;
    min-height: 88rpx; // 最小触摸区域
    border-bottom: 1rpx solid #f0f0f0;
    
    .icon {
      margin-right: 24rpx;
      flex-shrink: 0;
    }
    
    .selected-item {
      display: flex;
      align-items: center;
      flex: 1;
      
      .cover {
        width: 60rpx;
        height: 80rpx;
        border-radius: 8rpx;
        margin-right: 24rpx;
        flex-shrink: 0;
        background-color: #f5f5f5;
      }
      
      .cover-placeholder {
        width: 60rpx;
        height: 80rpx;
        border-radius: 8rpx;
        background-color: #f8f9fa;
        border: 2rpx solid #6AC086;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 24rpx;
        flex-shrink: 0;
      }
      
      .source-info {
        flex: 1;
        
        .name {
          display: block;
          font-size: 32rpx;
          color: #333;
          font-weight: 500;
          line-height: 1.4;
        }
        
        .publisher,
        .category {
          display: block;
          font-size: 24rpx;
          color: #999;
          margin-top: 4rpx;
        }
      }
      
      .clear-btn {
        margin-left: 16rpx;
        flex-shrink: 0;
      }
    }
    
    .placeholder {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
      
      .placeholder-text {
        font-size: 32rpx;
        color: #999;
      }
      
      .arrow {
        flex-shrink: 0;
      }
    }
    
    &.has-value {
      background-color: #f8f9fa;
      border-radius: 12rpx;
      padding: 24rpx 32rpx;
      border-bottom: none;
    }
  }
}
</style>
