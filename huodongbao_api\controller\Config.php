<?php
namespace controller;
use core\Controller;
use core\Db;
use PhpOffice\PhpWord\Exception\Exception;

/*
 * @className 系统配置
*/
class Config extends Controller{

	public function __construct(){
		parent::__construct();
	}

	/*
	* @apiName APP相关配置
	* @method app
	* @GET
	* @param 无
	* @return {"status":"ok","data":{"config":{"app_name":{"name":"app_name","val":"小聚会","shuoming":"app\u540d\u79f0"},"min_chongzhi_money":{"name":"min_chongzhi_money","val":"1","shuoming":"\u6700\u4f4e\u5145\u503c\u91d1\u989d"},"min_tixian_money":{"name":"min_tixian_money","val":"10","shuoming":"\u6700\u4f4e\u63d0\u73b0\u91d1\u989d"},"tixian_fee":{"name":"tixian_fee","val":"0","shuoming":"\u63d0\u73b0\u624b\u7eed\u8d39\u767e\u5206\u6bd4\uff0c5\u53735%(\u6700\u591a\u4e24\u4f4d\u5c0f\u6570)"},"huiyuan_price":{"name":"huiyuan_price","val":"500","shuoming":"\u8d2d\u4e70\u4f1a\u5458\u4ef7\u683c"},"huiyuan_yongjin_money":{"name":"huiyuan_yongjin_money","val":"100","shuoming":"\u8d2d\u4e70\u4f1a\u5458\u4e00\u7ea7\u4f63\u91d1\uff08\u56fa\u5b9a\u91d1\u989d\uff09"},"huiyuan_yongjin_money_2":{"name":"huiyuan_yongjin_money_2","val":"30","shuoming":"\u8d2d\u4e70\u4f1a\u5458\u4e8c\u7ea7\u4f63\u91d1\uff08\u56fa\u5b9a\u91d1\u989d\uff09"},"goods_auto_queren_days":{"name":"goods_auto_queren_days","val":"10","shuoming":"\u5546\u54c1\u53d1\u8d27\u540e\u81ea\u52a8\u786e\u8ba4\u6536\u8d27\u65f6\u95f4\uff08\u5929\uff09"},"goods_yanchi_queren_days":{"name":"goods_yanchi_queren_days","val":"10","shuoming":"\u5546\u54c1\u5ef6\u8fdf\u6536\u8d27\u589e\u52a0\u591a\u957f\u65f6\u95f4\uff08\u5929\uff09"},"goods_nopay_cancel_minute":{"name":"goods_nopay_cancel_minute","val":"30","shuoming":"\u672a\u652f\u4ed8\u8ba2\u5355\u81ea\u52a8\u53d6\u6d88\u65f6\u95f4\uff08\u5206\u949f\uff09"},"huodong_choujiang_start_time":{"name":"huodong_choujiang_start_time","val":"30","shuoming":"\u6d3b\u52a8\u5f00\u59cb\u591a\u5c11\u5206\u949f\u540e\u5f00\u59cb\u62bd\u5956"},"huodong_choujiang_end_time":{"name":"huodong_choujiang_end_time","val":"1","shuoming":"\u6d3b\u52a8\u7ed3\u675f\u591a\u5c11\u5206\u949f\u540e\u7ed3\u675f\u62bd\u5956"},"huiyuan_days":{"name":"huiyuan_days","val":"365","shuoming":"\u8d2d\u4e70\u4f1a\u5458\u6709\u6548\u65f6\u957f\uff08\u5929\uff09"},"huodong_jiesuan_days":{"name":"huodong_jiesuan_days","val":"3","shuoming":"\u6d3b\u52a8\u7ed3\u675f\u540e\u51e0\u5929\u624d\u80fd\u7533\u8bf7\u7ed3\u7b97"}},"img_config":{"app_logo":{"mark":"app_logo","name":"app\u56fe\u6807","img_url":"http:\/\/192.168.0.66:1052\/test.jpg"}},"video_config":[]}}
	*/
	public function app(){
		$data = \core\Cache::getCache("app");
		if(!empty($data))return ["status"=>"ok","data"=>$data];
		dbConn();
		$img_config = Db()->table("img_config")->select("mark,name,img_url")->where("is_admin=0")->fetchAll();
		$video_config = Db()->table("video_config")->select("mark,name,video_url")->where("is_admin=0")->fetchAll();
		$config = Db()->table("config")->select("id,name,val,shuoming")->where("is_show=1 AND is_admin=0")->fetchAll();
		
		// 获取ID=27的封面图配置（如果不在上面查询结果中）
		$hasId27 = false;
		foreach ($config as $item) {
			if ($item['id'] == 27) {
				$hasId27 = true;
				break;
			}
		}
		
		// 如果config中没有id=27的数据，专门查询添加
		if (!$hasId27) {
			$splash_image = Db()->table("config")
				->select("id,name,val,shuoming")
				->where("id=:id")
				->prepareParam([":id" => 27])
				->fetch();
			if (!empty($splash_image)) {
				// 确保config数组已初始化为数组
				if (!is_array($config)) {
					$config = [];
				}
				// 添加封面图配置到配置列表
				$config[] = $splash_image;
			}
		}
		
		// 特别处理封面图配置，确保splash_image_url配置存在
		$hasSplashConfig = false;
		foreach ($config as $item) {
			if ($item['name'] == 'splash_image_url') {
				$hasSplashConfig = true;
				break;
			}
		}
		
		// 如果没有splash_image_url命名的配置，但有id=27的配置，创建别名
		if (!$hasSplashConfig) {
			foreach ($config as $item) {
				if ($item['id'] == 27) {
					$splashConfig = $item;
					$splashConfig['name'] = 'splash_image_url';
					$config[] = $splashConfig;
					break;
				}
			}
		}
		
		$data = [
			"config"=>array_column($config,null,"name"),
			"img_config"=>array_column($img_config,null,"mark"),
			"video_config"=>array_column($video_config,null,"mark"),
		];
		//\core\Cache::setCache("app",$data,60);
		return ["status"=>"ok","data"=>$data];
	}

	/*
	* @apiName 获取开屏媒体配置
	* @method splash_media
	* @GET
	* @return {"status":"ok","data":{"media_type":"image","media_url":"https://example.com/splash.png","duration":3000}}
	*/
	public function splash_media(){
		try {
			// {{ AURA-X: Add - 开屏媒体配置API，支持从Redis缓存获取 }}
			// 尝试从Redis缓存获取
			$redis_key = "splash_media_config";
			$cached_data = null;

			try {
				$redis = \lib\Redis::getInstance();
				if($redis) {
					$cached_data = $redis->get($redis_key);
					if($cached_data) {
						$cached_data = json_decode($cached_data, true);
						if($cached_data && isset($cached_data['media_url'])) {
							return ["status" => "ok", "data" => $cached_data];
						}
					}
				}
			} catch (\Throwable $e) {
				// Redis连接失败，继续使用数据库
				error_log("Redis连接失败，使用数据库查询: " . $e->getMessage());
			}

			// {{ AURA-X: Fix - 修复表名为新的media_config表. Confirmed via 寸止 }}
			// 从数据库获取配置
			dbConn();
			$config = Db()->table("media_config")
				->select("media_type, media_url, duration, is_active")
				->where("scene = 'splash' AND is_active = 1")
				->order("id DESC")
				->fetch();

			if(empty($config)) {
				// {{ AURA-X: Fix - 修复默认配置，避免加载不存在的图片. Confirmed via 寸止 }}
				// 没有配置时返回空状态，让前端处理
				return ["status" => "empty", "msg" => "暂无开屏媒体配置"];
			}

			$result = [
				"media_type" => $config['media_type'],
				"media_url" => $config['media_url'],
				"duration" => intval($config['duration'])
			];

			// 缓存到Redis（缓存1小时）
			try {
				if($redis) {
					$redis->setex($redis_key, 3600, json_encode($result));
				}
			} catch (\Throwable $e) {
				// 缓存失败不影响返回结果
				error_log("Redis缓存失败: " . $e->getMessage());
			}

			return ["status" => "ok", "data" => $result];

		} catch (\Throwable $e) {
			return ["status" => "error", "msg" => "获取开屏媒体配置失败: " . $e->getMessage()];
		}
	}

	/*
	* @apiName 获取个人中心背景媒体配置
	* @method profile_bg_media
	* @GET
	* @return {"status":"ok","data":{"media_type":"image","media_url":"https://example.com/bg.png","duration":0}}
	*/
	public function profile_bg_media(){
		try {
			// {{ AURA-X: Add - 个人中心背景媒体配置API. Confirmed via 寸止. }}
			// 尝试从Redis缓存获取
			$redis_key = "media_config_profile_bg";
			$cached_data = null;

			try {
				$redis = \lib\Redis::getInstance();
				if($redis) {
					$cached_data = $redis->get($redis_key);
					if($cached_data) {
						$cached_data = json_decode($cached_data, true);
						if($cached_data && isset($cached_data['media_url'])) {
							return ["status" => "ok", "data" => $cached_data];
						}
					}
				}
			} catch (\Throwable $e) {
				// Redis连接失败，继续使用数据库
				error_log("Redis连接失败，使用数据库查询: " . $e->getMessage());
			}

			// 从数据库获取配置
			dbConn();
			$config = Db()->table("media_config")
				->select("media_type, media_url, duration, is_active")
				->where("scene = :scene AND is_active = 1")
				->prepareParam([":scene" => "profile_bg"])
				->order("id DESC")
				->fetch();

			if(empty($config)) {
				return ["status" => "empty", "msg" => "暂无个人中心背景媒体配置"];
			}

			$result = [
				"media_type" => $config['media_type'],
				"media_url" => $config['media_url'],
				"duration" => intval($config['duration'])
			];

			// 缓存到Redis（缓存1小时）
			try {
				if($redis) {
					$redis->setex($redis_key, 3600, json_encode($result));
				}
			} catch (\Throwable $e) {
				// 缓存失败不影响返回结果
				error_log("Redis缓存失败: " . $e->getMessage());
			}

			return ["status" => "ok", "data" => $result];

		} catch (\Throwable $e) {
			return ["status" => "error", "msg" => "获取个人中心背景媒体配置失败: " . $e->getMessage()];
		}
	}

	/*
	* @apiName 首页弹出公告
	* @method pop
	* @GET
	* @param 无
	* @return
	*/
	public function pop(){
		dbConn();
		$data = Db()->table("pop")->select("id,title,msg")->where("is_show=1")->order("id DESC")->fetch();
		if(empty($data))return ["status"=>"empty"];
		return ["status"=>"ok","data"=>$data];
	}

	/*
	* @apiName 获取充值列表
	* @method chongzhi_list
	* @GET
	* @param 无
	* @return {"status":"ok","data":[{"id":1,"money":10,"daozhang_money":10,"label":""},{"id":2,"money":20,"daozhang_money":20,"label":""},{"id":3,"money":50,"daozhang_money":50,"label":""},{"id":4,"money":100,"daozhang_money":100,"label":""},{"id":5,"money":200,"daozhang_money":200,"label":""},{"id":6,"money":300,"daozhang_money":300,"label":""},{"id":7,"money":500,"daozhang_money":500,"label":""},{"id":8,"money":1000,"daozhang_money":1000,"label":""},{"id":9,"money":2000,"daozhang_money":2000,"label":""}]}
	*/
	public function chongzhi_list(){
		$data = \core\Cache::getCache("chongzhi_list");
		if(!empty($data))return ["status"=>"ok","data"=>$data];
		dbConn();
		$data = Db()->table("user_chongzhi")->select("id,money,daozhang_money,label")->where("1")->fetchAll();
		//\core\Cache::setCache("chongzhi_list",$data,60);
		return ["status"=>"ok","data"=>$data];
	}


	/*
	* @apiName 发送短信验证码
	* @method send_sms
	* @POST
	* @param mobile string 手机号
	* @param type string 类型:1=注册或登录,2=重置密码,3=修改资料
	* @return {"status":"ok"}
	*/
	public function send_sms($mobile,$type){
		if(
			empty($type) ||
			empty($mobile) ||
			!check($mobile,"mobile") ||
			!check($type,"integt0")
		)return ["status"=>"error","msg"=>"参数错误"];
		$mobile = htmlspecialchars($mobile);
		$type = (int)$type;
		$code = makeCode(6);
		$deadline = date("Y-m-d H:i:s",_NOW_ + 300);
		dbConn();
		//
		$m_1_time = date("Y-m-d H:i:s",_NOW_ - 60);
		$check = Db()->table("sms_code")->select("id")->where("mobile=:mobile AND type=:type AND send_time>:time")->prepareParam([":mobile"=>$mobile, ":type"=>$type, ":time"=>$m_1_time])->fetch();
		if(!empty($check)){
			return ["status"=>"ok","msg"=>"发送成功"];
		}
		//
		$res = \model\Juhesms::send($mobile,$code);
		if(isset($res['status']) && $res['status'] == "ok"){
			//
			$insert = [
				"type"=>$type,
				"mobile"=>":mobile",
				"code"=>$code,
				"deadline"=>$deadline,
				"ip"=>IP
			];
			$prepareParam = [
				":mobile"=>$mobile,
			];
			if(!Db()->table("sms_code")->prepareParam($prepareParam)->insert($insert)){
				$this->exception_log("插入验证码失败");
			}
			//
			return ["status"=>"ok","msg"=>"发送成功"];
		}else{
			if(isset($res['msg'])){
				$this->exception_log($res['msg'] . " 【 {$mobile} 】 ");
			}
			return ["status"=>"error","msg"=>"系统繁忙，请稍后再试"];
		}
	}

	/*
	* @apiName 获取省市区配置（高德数据版本）
	* @method get_china
	* @GET
	* @param 无
	* @return {"status":"ok","data":[{"id":"110000","name":"北京市","children":[{"id":"110100","name":"北京城区","children":[{"id":"110101","name":"东城区"}]}]}]}
	*/
	public function get_china(){
		$data = \core\Cache::getCache("gaode_china");
		if(!empty($data))return ["status"=>"ok","data"=>$data];

		try {
			dbConn();
			// 使用高德数据表
			$res = Db()->_fetchAll("SELECT adcode as id, parent_adcode as pid, name FROM gaode_district ORDER BY adcode");
			$data = self::get_gaode_children('0',$res);
			foreach($data as &$sheng){
				$sheng['children'] = self::get_gaode_children($sheng['id'],$res);
				foreach($sheng['children'] as &$shi){
					$shi['children'] =  self::get_gaode_children($shi['id'],$res);
				}
			}
			\core\Cache::setCache("gaode_china",$data,3600); // 缓存1小时
			return ["status"=>"ok","data"=>$data];
		} catch (\Throwable $e) {
			// 如果高德数据表不存在，回退到旧版本
			$res = Db()->table("china")->select("id,pid,name")->fetchAll();
			$data = self::get_children(0,$res);
			foreach($data as &$sheng){
				$sheng['children'] = self::get_children($sheng['id'],$res);
				foreach($sheng['children'] as &$shi){
					$shi['children'] =  self::get_children($shi['id'],$res);
				}
			}
			return ["status"=>"ok","data"=>$data];
		}
	}

	/*
	* @apiName 获取城市列表（用于城市选择页面）
	* @method get_city_list
	* @GET
	* @param search string 搜索关键词（可选）
	* @param group_by_letter int 是否按首字母分组返回（1=分组，0=列表，默认0）
	* @return {"status":"ok","data":[{"adcode":"110100","name":"北京市","pinyin":"beijing","first_letter":"B"}]}
	* @return_grouped {"status":"ok","data":{"A":[...],"B":[...],"C":[...]}}
	*/
	public function get_city_list($search = '', $group_by_letter = 0) {
		try {
			dbConn();

			// {{ AURA-X: Modify - 增强城市列表接口，支持拼音排序、首字母分组和Redis缓存. Confirmed via 寸止. }}

			// 生成缓存键
			$cache_key = "city_list:" . md5($search . "_" . $group_by_letter);

			// 尝试从Redis缓存获取数据
			$redis = \lib\Redis::getInstance();
			if ($redis) {
				$cached_data = $redis->get($cache_key);
				if ($cached_data) {
					$result = json_decode($cached_data, true);
					if ($result) {
						return $result;
					}
				}
			}

			// 构建查询条件
			$where_conditions = ["level = 'city'"];
			$params = [];

			// 搜索条件 - 支持城市名称和拼音搜索
			if (!empty($search)) {
				$where_conditions[] = "(name LIKE :search OR pinyin LIKE :search_pinyin)";
				$params[":search"] = "%{$search}%";
				$params[":search_pinyin"] = "%{$search}%";
			}

			$where_clause = implode(' AND ', $where_conditions);

			// 查询城市数据，包含拼音信息，按首字母和拼音排序
			$sql = "SELECT adcode, name, pinyin, first_letter FROM gaode_district
					WHERE {$where_clause}
					ORDER BY first_letter ASC, pinyin ASC, name ASC";

			$cities = Db()->_fetchAll($sql, $params);

			// 处理返回数据格式
			if ($group_by_letter == 1) {
				// 按首字母分组返回
				$grouped_cities = [];
				foreach ($cities as $city) {
					$letter = $city['first_letter'] ?: 'Z'; // 默认分组到Z
					if (!isset($grouped_cities[$letter])) {
						$grouped_cities[$letter] = [];
					}
					$grouped_cities[$letter][] = $city;
				}

				// 确保字母顺序
				ksort($grouped_cities);
				$result = ["status" => "ok", "data" => $grouped_cities];
			} else {
				// 列表格式返回
				$result = ["status" => "ok", "data" => $cities];
			}

			// 缓存结果到Redis（24小时）- 修复参数顺序
			if ($redis) {
				$redis->setex($cache_key, 24 * 3600, json_encode($result));
			}

			return $result;

		} catch (\Throwable $e) {
			return ["status" => "error", "msg" => "获取城市列表失败：" . $e->getMessage()];
		}
	}



	static private function get_children($pid,$data){
		$res = [];
		foreach($data as $row){
			if($row['pid'] == $pid){
				$res[] = [
					"id"=>$row['id'],
					"name"=>$row['name'],
				];
			}
		}
		return $res;
	}

	/**
	 * 获取高德数据的子级数据
	 * @param string $pid 父级adcode
	 * @param array $data 所有数据
	 * @return array
	 */
	static private function get_gaode_children($pid,$data){
		$res = [];
		foreach($data as $row){
			if($row['pid'] == $pid){
				$res[] = [
					"id"=>$row['id'],
					"name"=>$row['name'],
				];
			}
		}
		return $res;
	}

	/*
	* @apiName 省市区换编号
	* @method get_shengshiqu_id
	* @POST
	* @param name string 省市区名称
	* @param type string 类型:1=省,2=市,3=县区
	* @return {"status":"ok","data":{"id":1497,"name":"\u91d1\u6c34\u533a","deep":3,"pid":1493,"shi_info":{"id":1493,"name":"\u90d1\u5dde\u5e02","deep":2,"pid":1492},"sheng_info":{"id":1492,"name":"\u6cb3\u5357\u7701","deep":1,"pid":0}}}
	*/
	public function get_shengshiqu_id($name,$type=2){
		if(
			empty($name) ||
			!in_array($type,[1,2,3])
		)return ["status"=>"error","msg"=>"参数错误"];
		$name = trim($name);
		$type = intval($type);
		dbConn();

		try {
			// 先尝试从高德数据表查询
			$level_map = [1 => 'province', 2 => 'city', 3 => 'district'];
			$level = $level_map[$type] ?? 'city';

			$data = Db()->_fetch("SELECT adcode as id, name, level, parent_adcode as pid FROM gaode_district WHERE name LIKE :name AND level = :level LIMIT 1", [
				":name" => "{$name}%",
				":level" => $level
			]);

			if (!empty($data)) {
				// 转换level为deep以保持兼容性
				$deep_map = ['province' => 1, 'city' => 2, 'district' => 3];
				$data['deep'] = $deep_map[$data['level']] ?? 2;

				// 获取父级信息
				if($type == 3 && !empty($data['pid'])){
					$shi_info = Db()->_fetch("SELECT adcode as id, name, level, parent_adcode as pid FROM gaode_district WHERE adcode = :pid", [":pid" => $data['pid']]);
					if($shi_info) {
						$shi_info['deep'] = $deep_map[$shi_info['level']] ?? 2;
						$data['shi_info'] = $shi_info;

						if(!empty($shi_info['pid'])) {
							$sheng_info = Db()->_fetch("SELECT adcode as id, name, level, parent_adcode as pid FROM gaode_district WHERE adcode = :pid", [":pid" => $shi_info['pid']]);
							if($sheng_info) {
								$sheng_info['deep'] = $deep_map[$sheng_info['level']] ?? 1;
								$data['sheng_info'] = $sheng_info;
							}
						}
					}
				}else if($type == 2 && !empty($data['pid'])){
					$sheng_info = Db()->_fetch("SELECT adcode as id, name, level, parent_adcode as pid FROM gaode_district WHERE adcode = :pid", [":pid" => $data['pid']]);
					if($sheng_info) {
						$sheng_info['deep'] = $deep_map[$sheng_info['level']] ?? 1;
						$data['sheng_info'] = $sheng_info;
					}
				}

				return ["status"=>"ok","data"=>$data];
			}
		} catch (\Throwable $e) {
			// 高德数据查询失败，继续使用旧版本
		}

		// {{ AURA-X: Remove - 移除旧china表回退逻辑. Confirmed via 寸止. }}
		// 如果高德数据查询失败，直接返回错误
		return ["status"=>"empty","msg"=>"未找到对应地址"];
		if($type == 3){
			$shi_info = Db()->table("china")->select("id,name,deep,pid")->where("id=:pid")->prepareParam([":pid"=>$data['pid']])->fetch();
			$data['shi_info'] = $shi_info;
			$sheng_info = Db()->table("china")->select("id,name,deep,pid")->where("id=:pid")->prepareParam([":pid"=>$shi_info['pid']])->fetch();
			$data['sheng_info'] = $sheng_info;
		}else if($type == 2){
			$sheng_info = Db()->table("china")->select("id,name,deep,pid")->where("id=:pid")->prepareParam([":pid"=>$data['pid']])->fetch();
			$data['sheng_info'] = $sheng_info;
		}
		return ["status"=>"ok","data"=>$data];
	}

	/*
	* @apiName 根据adcode获取地区信息
	* @method get_area_by_adcode
	* @POST
	* @param adcode 高德地区编码
	* @return {"status":"ok","data":{"adcode":"110000","name":"北京市","level":"province"}}
	*/
	public function get_area_by_adcode() {
		$adcode = $_POST['adcode'] ?? '';

		if (empty($adcode)) {
			return ["status" => "error", "msg" => "adcode不能为空"];
		}

		try {
			dbConn();
			$result = Db()->_fetch("SELECT adcode, name, level FROM gaode_district WHERE adcode=:adcode", [
				":adcode" => $adcode
			]);

			if ($result) {
				return ["status" => "ok", "data" => $result];
			} else {
				return ["status" => "error", "msg" => "未找到对应的地区"];
			}
		} catch (\Throwable $e) {
			return ["status" => "error", "msg" => "查询失败：" . $e->getMessage()];
		}
	}

	/*
	* @apiName 获取高德行政区划数据
	* @method fetch_gaode_district_data
	* @GET
	* @param key string 高德API密钥
	* @param keywords string 查询关键字，默认为空获取全国数据
	* @param subdistrict int 子级行政区级数，默认3
	* @return {"status":"ok","data":[]}
	*/
	public function fetch_gaode_district_data($key = '', $keywords = '', $subdistrict = 3) {
		if (empty($key)) {
			return ["status" => "error", "msg" => "请提供高德API密钥"];
		}

		$url = "https://restapi.amap.com/v3/config/district";
		$params = [
			'key' => $key,
			'subdistrict' => $subdistrict,
			'extensions' => 'base'
		];

		if (!empty($keywords)) {
			$params['keywords'] = $keywords;
		}

		$query_string = http_build_query($params);
		$full_url = $url . '?' . $query_string;

		// 使用curl获取数据
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $full_url);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_TIMEOUT, 30);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

		$response = curl_exec($ch);
		$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
		curl_close($ch);

		if ($http_code !== 200 || empty($response)) {
			return ["status" => "error", "msg" => "获取高德数据失败"];
		}

		$data = json_decode($response, true);
		if (empty($data) || $data['status'] !== '1') {
			return ["status" => "error", "msg" => "高德API返回错误：" . ($data['info'] ?? '未知错误')];
		}

		return ["status" => "ok", "data" => $data['districts']];
	}

	/*
	* @apiName 导入高德数据到数据库
	* @method import_gaode_data_to_db
	* @POST
	* @param key string 高德API密钥
	* @param clean boolean 是否清理现有数据，默认true
	* @return {"status":"ok","msg":"导入成功","count":3249}
	*/
	public function import_gaode_data_to_db($key = '', $clean = true) {
		if (empty($key)) {
			return ["status" => "error", "msg" => "请提供高德API密钥"];
		}

		dbConn();

		// 检查现有数据状态
		$existing_count = Db()->table("gaode_district")->count();
		if ($existing_count > 0 && $clean) {
			// 清理现有数据 - 使用TRUNCATE
			Db::_exec("TRUNCATE TABLE gaode_district");
			$this->update_migration_status('fetch_gaode_data', 'running', "已清理{$existing_count}条现有数据");
		} elseif ($existing_count > 0 && !$clean) {
			return ["status" => "error", "msg" => "表中已存在{$existing_count}条数据，请设置clean=true清理后重新导入"];
		}

		// 获取高德数据
		$result = $this->fetch_gaode_district_data($key);
		if ($result['status'] !== 'ok') {
			return $result;
		}

		Db::begin();

		try {
			$this->update_migration_status('fetch_gaode_data', 'running', "开始导入高德数据");
			$count = 0;

			// 递归处理行政区数据
			$this->process_district_recursive($result['data'], 0, $count);

			$this->update_migration_status('fetch_gaode_data', 'completed', "成功导入{$count}条高德数据");
			Db::commit();
			return ["status" => "ok", "msg" => "导入成功", "count" => $count];

		} catch (\Throwable $e) {
			$this->update_migration_status('fetch_gaode_data', 'failed', $e->getMessage());
			Db::rollback();
			return ["status" => "error", "msg" => "导入失败：" . $e->getMessage()];
		}
	}

	/*
	* 递归处理行政区数据
	*/
	private function process_district_recursive($districts, $parent_adcode, &$count) {
		foreach ($districts as $district) {
			$adcode = $district['adcode'];
			$name = $district['name'];
			$level = $district['level'];
			$center = $district['center'] ?? '';

			// 检查是否已存在该adcode，避免重复插入
			$exists = Db()->table("gaode_district")
				->select("adcode")
				->where("adcode=:adcode")
				->prepareParam([":adcode" => $adcode])
				->fetch();

			if (!$exists) {
				// 插入到gaode_district表
				Db()->table("gaode_district")->insert([
					'adcode' => $adcode,
					'parent_adcode' => $parent_adcode,
					'name' => $name,
					'level' => $level,
					'center' => $center,
					'created_at' => date('Y-m-d H:i:s')
				]);
				$count++;
			}

			// 处理子级行政区
			if (!empty($district['districts'])) {
				$this->process_district_recursive($district['districts'], $adcode, $count);
			}
		}
	}

	/*
	* @apiName 创建china表与adcode映射关系
	* @method create_china_adcode_mapping
	* @POST
	* @return {"status":"ok","msg":"映射创建成功","count":3249}
	*/
	public function create_china_adcode_mapping() {
		dbConn();
		Db::begin();

		try {
			// 更新迁移状态
			$this->update_migration_status('create_mapping', 'running');

			$count = 0;

			// 获取原china表数据
			$china_data = Db()->table("china")->select("id,name,deep,pid")->fetchAll();

			// 获取高德数据
			$gaode_data = Db()->table("gaode_district")->select("adcode,name,level")->fetchAll();

			// 创建高德数据的名称索引
			$gaode_index = [];
			foreach ($gaode_data as $item) {
				$gaode_index[$item['name']] = $item['adcode'];
			}

			// 遍历china表数据，寻找匹配的adcode
			foreach ($china_data as $china_item) {
				$name = $china_item['name'];
				$deep = $china_item['deep'];
				$adcode = null;

				// 直接匹配名称
				if (isset($gaode_index[$name])) {
					$adcode = $gaode_index[$name];
				} else {
					// 模糊匹配（去掉"省"、"市"、"区"等后缀）
					$clean_name = $this->clean_area_name($name);
					foreach ($gaode_index as $gaode_name => $gaode_adcode) {
						if ($this->clean_area_name($gaode_name) === $clean_name) {
							$adcode = $gaode_adcode;
							break;
						}
					}
				}

				if ($adcode) {
					// 检查是否已存在该映射关系
					$exists = Db()->table("china_adcode_mapping")
						->select("old_china_id")
						->where("old_china_id=:id")
						->prepareParam([":id" => $china_item['id']])
						->fetch();

					if (!$exists) {
						// 插入映射关系
						Db()->table("china_adcode_mapping")->insert([
							'old_china_id' => $china_item['id'],
							'adcode' => $adcode,
							'name' => $name,
							'level' => $deep,
							'created_at' => date('Y-m-d H:i:s')
						]);
						$count++;
					}
				}
			}

			$this->update_migration_status('create_mapping', 'completed', "成功创建{$count}条映射关系");
			Db::commit();

			return ["status" => "ok", "msg" => "映射创建成功", "count" => $count];

		} catch (\Throwable $e) {
			$this->update_migration_status('create_mapping', 'failed', $e->getMessage());
			Db::rollback();
			return ["status" => "error", "msg" => "映射创建失败：" . $e->getMessage()];
		}
	}

	/*
	* 清理地区名称，去掉常见后缀
	*/
	private function clean_area_name($name) {
		$suffixes = ['省', '市', '区', '县', '自治区', '自治州', '自治县', '特别行政区'];
		foreach ($suffixes as $suffix) {
			if (mb_substr($name, -mb_strlen($suffix)) === $suffix) {
				return mb_substr($name, 0, -mb_strlen($suffix));
			}
		}
		return $name;
	}

	/*
	* 更新迁移状态
	*/
	private function update_migration_status($step, $status, $message = null) {
		$data = ['status' => $status];

		if ($status === 'running') {
			$data['started_at'] = date('Y-m-d H:i:s');
		} elseif (in_array($status, ['completed', 'failed'])) {
			$data['completed_at'] = date('Y-m-d H:i:s');
		}

		if ($message) {
			$data['message'] = $message;
		}

		Db()->table("migration_status")
			->where("step = :step")
			->prepareParam([":step" => $step])
			->update($data);
	}

	/*
	* @apiName 迁移huodong表地区字段
	* @method migrate_huodong_table
	* @POST
	* @return {"status":"ok","msg":"迁移成功","count":100}
	*/
	public function migrate_huodong_table() {
		dbConn();
		Db::begin();

		try {
			$this->update_migration_status('migrate_huodong_table', 'running');

			$count = 0;
			$page = 1;
			$page_size = 100;

			while (true) {
				// 分页获取huodong数据
				$huodong_list = Db()->table("huodong")
					->select("id,sheng_id,shi_id,qu_id")
					->where("sheng_id > 0 OR shi_id > 0 OR qu_id > 0")
					->page($page, $page_size);

				if (empty($huodong_list)) {
					break;
				}

				foreach ($huodong_list as $huodong) {
					$new_sheng_id = $this->get_adcode_by_china_id($huodong['sheng_id']);
					$new_shi_id = $this->get_adcode_by_china_id($huodong['shi_id']);
					$new_qu_id = $this->get_adcode_by_china_id($huodong['qu_id']);

					// 更新huodong记录
					Db()->table("huodong")
						->where("id = :id")
						->prepareParam([":id" => $huodong['id']])
						->update([
							'sheng_id' => $new_sheng_id ?: $huodong['sheng_id'],
							'shi_id' => $new_shi_id ?: $huodong['shi_id'],
							'qu_id' => $new_qu_id ?: $huodong['qu_id']
						]);

					$count++;
				}

				$page++;
			}

			$this->update_migration_status('migrate_huodong_table', 'completed', "成功迁移{$count}条记录");
			Db::commit();

			return ["status" => "ok", "msg" => "迁移成功", "count" => $count];

		} catch (\Throwable $e) {
			$this->update_migration_status('migrate_huodong_table', 'failed', $e->getMessage());
			Db::rollback();
			return ["status" => "error", "msg" => "迁移失败：" . $e->getMessage()];
		}
	}

	/*
	* @apiName 迁移user_addr表地区字段
	* @method migrate_user_addr_table
	* @POST
	* @return {"status":"ok","msg":"迁移成功","count":50}
	*/
	public function migrate_user_addr_table() {
		dbConn();
		Db::begin();

		try {
			$this->update_migration_status('migrate_user_addr_table', 'running');

			$count = 0;
			$page = 1;
			$page_size = 100;

			while (true) {
				// 分页获取user_addr数据
				$addr_list = Db()->table("user_addr")
					->select("id,sheng_id,shi_id,qu_id")
					->where("sheng_id > 0 OR shi_id > 0 OR qu_id > 0")
					->page($page, $page_size);

				if (empty($addr_list)) {
					break;
				}

				foreach ($addr_list as $addr) {
					$new_sheng_id = $this->get_adcode_by_china_id($addr['sheng_id']);
					$new_shi_id = $this->get_adcode_by_china_id($addr['shi_id']);
					$new_qu_id = $this->get_adcode_by_china_id($addr['qu_id']);

					// 更新user_addr记录
					Db()->table("user_addr")
						->where("id = :id")
						->prepareParam([":id" => $addr['id']])
						->update([
							'sheng_id' => $new_sheng_id ?: $addr['sheng_id'],
							'shi_id' => $new_shi_id ?: $addr['shi_id'],
							'qu_id' => $new_qu_id ?: $addr['qu_id']
						]);

					$count++;
				}

				$page++;
			}

			$this->update_migration_status('migrate_user_addr_table', 'completed', "成功迁移{$count}条记录");
			Db::commit();

			return ["status" => "ok", "msg" => "迁移成功", "count" => $count];

		} catch (\Throwable $e) {
			$this->update_migration_status('migrate_user_addr_table', 'failed', $e->getMessage());
			Db::rollback();
			return ["status" => "error", "msg" => "迁移失败：" . $e->getMessage()];
		}
	}

	/*
	* 根据china表ID获取对应的adcode
	*/
	private function get_adcode_by_china_id($china_id) {
		if (empty($china_id)) {
			return null;
		}

		$mapping = Db()->table("china_adcode_mapping")
			->select("adcode")
			->where("old_china_id = :china_id")
			->prepareParam([":china_id" => $china_id])
			->fetch();

		return $mapping ? $mapping['adcode'] : null;
	}

	/*
	* @apiName 查看迁移状态
	* @method get_migration_status
	* @GET
	* @return {"status":"ok","data":[],"summary":{}}
	*/
	public function get_migration_status() {
		dbConn();
		$data = Db()->table("migration_status")
			->select("step,status,message,started_at,completed_at")
			->order("id ASC")
			->fetchAll();

		// 添加数据统计信息
		$summary = [
			'gaode_district_count' => Db()->table("gaode_district")->count(),
			'mapping_count' => Db()->table("china_adcode_mapping")->count(),
			'china_backup_count' => 0
		];

		// 检查备份表是否存在
		try {
			$summary['china_backup_count'] = Db()->table("china_backup")->count();
		} catch (\Throwable $e) {
			$summary['china_backup_count'] = 0;
		}

		return ["status" => "ok", "data" => $data, "summary" => $summary];
	}

	/*
	* @apiName 检查数据表状态
	* @method check_table_status
	* @GET
	* @return {"status":"ok","data":{}}
	*/
	public function check_table_status() {
		dbConn();

		$status = [
			'gaode_district' => [
				'exists' => false,
				'count' => 0,
				'sample' => null
			],
			'china_adcode_mapping' => [
				'exists' => false,
				'count' => 0
			],
			'china_backup' => [
				'exists' => false,
				'count' => 0
			],
			'original_china' => [
				'count' => 0,
				'sample' => null
			]
		];

		try {
			// 检查gaode_district表
			$status['gaode_district']['count'] = Db()->table("gaode_district")->count();
			$status['gaode_district']['exists'] = true;
			if ($status['gaode_district']['count'] > 0) {
				$status['gaode_district']['sample'] = Db()->table("gaode_district")->limit(3)->fetchAll();
			}
		} catch (\Throwable $e) {
			$status['gaode_district']['error'] = $e->getMessage();
		}

		try {
			// 检查映射表
			$status['china_adcode_mapping']['count'] = Db()->table("china_adcode_mapping")->count();
			$status['china_adcode_mapping']['exists'] = true;
		} catch (\Throwable $e) {
			$status['china_adcode_mapping']['error'] = $e->getMessage();
		}

		try {
			// 检查备份表
			$status['china_backup']['count'] = Db()->table("china_backup")->count();
			$status['china_backup']['exists'] = true;
		} catch (\Throwable $e) {
			$status['china_backup']['error'] = $e->getMessage();
		}

		try {
			// 检查原china表
			$status['original_china']['count'] = Db()->table("china")->count();
			if ($status['original_china']['count'] > 0) {
				$status['original_china']['sample'] = Db()->table("china")->limit(3)->fetchAll();
			}
		} catch (\Throwable $e) {
			$status['original_china']['error'] = $e->getMessage();
		}

		return ["status" => "ok", "data" => $status];
	}

	/*
	* @apiName 生成高德版本的china.js文件
	* @method generate_gaode_china_js
	* @GET
	* @return {"status":"ok","js_content":"..."}
	*/
	public function generate_gaode_china_js() {
		dbConn();

		try {
			// 获取省级数据
			$provinces = Db()->table("gaode_district")
				->select("adcode,name")
				->where("level='province'")
				->order("adcode ASC")
				->fetchAll();

			$china_area = [];

			foreach ($provinces as $province) {
				$province_data = [
					"id" => $province['adcode'],
					"name" => $province['name'],
					"children" => []
				];

				// 获取该省的市级数据
				$cities = Db()->table("gaode_district")
					->select("adcode,name")
					->where("parent_adcode=:parent AND level='city'")
					->prepareParam([":parent" => $province['adcode']])
					->order("adcode ASC")
					->fetchAll();

				foreach ($cities as $city) {
					$city_data = [
						"id" => $city['adcode'],
						"name" => $city['name'],
						"children" => []
					];

					// 获取该市的区级数据
					$districts = Db()->table("gaode_district")
						->select("adcode,name")
						->where("parent_adcode=:parent AND level='district'")
						->prepareParam([":parent" => $city['adcode']])
						->order("adcode ASC")
						->fetchAll();

					foreach ($districts as $district) {
						$city_data['children'][] = [
							"id" => $district['adcode'],
							"name" => $district['name']
						];
					}

					$province_data['children'][] = $city_data;
				}

				$china_area[] = $province_data;
			}

			// 生成JavaScript内容
			$js_content = "/*\n";
			$js_content .= "高德地图行政区划数据 - 使用adcode作为ID\n";
			$js_content .= "标签固定id，自动联动\n";
			$js_content .= "<select id=\"area_sheng\"></select>\n";
			$js_content .= "<select id=\"area_shi\"></select>\n";
			$js_content .= "<select id=\"area_qu\"></select>\n";
			$js_content .= "自动填充中文\n";
			$js_content .= "<input type=\"hidden\" name=\"sheng_name\" id=\"sheng_name\" value=\"\" />\n";
			$js_content .= "<input type=\"hidden\" name=\"shi_name\" id=\"shi_name\" value=\"\" />\n";
			$js_content .= "<input type=\"hidden\" name=\"qu_name\" id=\"qu_name\" value=\"\" />\n";
			$js_content .= "*/\n";
			$js_content .= "var china_area = " . json_encode($china_area, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . ";\n\n";

			// 添加工具函数
			$js_content .= $this->generate_gaode_helper_functions();

			return ["status" => "ok", "js_content" => $js_content, "count" => count($china_area)];

		} catch (\Throwable $e) {
			return ["status" => "error", "msg" => "生成失败：" . $e->getMessage()];
		}
	}

	/*
	* @apiName 直接输出高德版本的china.js文件内容
	* @method download_gaode_china_js
	* @GET
	* @return JavaScript文件内容
	*/
	public function download_gaode_china_js() {
		$result = $this->generate_gaode_china_js();

		if ($result['status'] === 'ok') {
			// 设置响应头为JavaScript文件
			header('Content-Type: application/javascript; charset=utf-8');
			header('Content-Disposition: attachment; filename="gaode_china.js"');

			// 直接输出JavaScript内容
			echo $result['js_content'];
			exit;
		} else {
			header('Content-Type: application/json; charset=utf-8');
			echo json_encode($result, JSON_UNESCAPED_UNICODE);
			exit;
		}
	}

	/*
	* 生成高德版本的JavaScript工具函数
	*/
	private function generate_gaode_helper_functions() {
		return '
// 高德版本的工具函数
function gaode_china_init() {
	var sheng_select = document.getElementById("area_sheng");
	var shi_select = document.getElementById("area_shi");
	var qu_select = document.getElementById("area_qu");

	if (!sheng_select) return;

	// 清空并添加默认选项
	sheng_select.innerHTML = "<option value=\'\'>请选择省份</option>";
	shi_select.innerHTML = "<option value=\'\'>请选择城市</option>";
	qu_select.innerHTML = "<option value=\'\'>请选择区县</option>";

	// 填充省份数据
	china_area.forEach(function(province) {
		var option = document.createElement("option");
		option.value = province.id;
		option.textContent = province.name;
		sheng_select.appendChild(option);
	});

	// 省份变化事件
	sheng_select.addEventListener("change", function() {
		var sheng_id = this.value;
		var sheng_name = this.options[this.selectedIndex].text;

		// 更新隐藏字段
		var sheng_name_input = document.getElementById("sheng_name");
		if (sheng_name_input) sheng_name_input.value = sheng_id ? sheng_name : "";

		// 清空市和区
		shi_select.innerHTML = "<option value=\'\'>请选择城市</option>";
		qu_select.innerHTML = "<option value=\'\'>请选择区县</option>";

		if (sheng_id) {
			var province = china_area.find(p => p.id === sheng_id);
			if (province && province.children) {
				province.children.forEach(function(city) {
					var option = document.createElement("option");
					option.value = city.id;
					option.textContent = city.name;
					shi_select.appendChild(option);
				});
			}
		}
	});

	// 城市变化事件
	shi_select.addEventListener("change", function() {
		var shi_id = this.value;
		var shi_name = this.options[this.selectedIndex].text;

		// 更新隐藏字段
		var shi_name_input = document.getElementById("shi_name");
		if (shi_name_input) shi_name_input.value = shi_id ? shi_name : "";

		// 清空区
		qu_select.innerHTML = "<option value=\'\'>请选择区县</option>";

		if (shi_id) {
			var sheng_id = sheng_select.value;
			var province = china_area.find(p => p.id === sheng_id);
			if (province) {
				var city = province.children.find(c => c.id === shi_id);
				if (city && city.children) {
					city.children.forEach(function(district) {
						var option = document.createElement("option");
						option.value = district.id;
						option.textContent = district.name;
						qu_select.appendChild(option);
					});
				}
			}
		}
	});

	// 区县变化事件
	qu_select.addEventListener("change", function() {
		var qu_id = this.value;
		var qu_name = this.options[this.selectedIndex].text;

		// 更新隐藏字段
		var qu_name_input = document.getElementById("qu_name");
		if (qu_name_input) qu_name_input.value = qu_id ? qu_name : "";
	});
}

// 根据adcode获取地区名称
function getAreaNameByAdcode(adcode) {
	if (!adcode) return "";

	for (var province of china_area) {
		if (province.id === adcode) return province.name;

		for (var city of province.children) {
			if (city.id === adcode) return city.name;

			for (var district of city.children) {
				if (district.id === adcode) return district.name;
			}
		}
	}
	return "";
}

// 根据名称获取adcode
function getAdcodeByName(name, level) {
	if (!name) return "";

	for (var province of china_area) {
		if (province.name === name && (!level || level === "province")) {
			return province.id;
		}

		for (var city of province.children) {
			if (city.name === name && (!level || level === "city")) {
				return city.id;
			}

			for (var district of city.children) {
				if (district.name === name && (!level || level === "district")) {
					return district.id;
				}
			}
		}
	}
	return "";
}

// 设置选中值
function setGaodeAreaValue(sheng_id, shi_id, qu_id) {
	var sheng_select = document.getElementById("area_sheng");
	var shi_select = document.getElementById("area_shi");
	var qu_select = document.getElementById("area_qu");

	if (sheng_select && sheng_id) {
		sheng_select.value = sheng_id;
		sheng_select.dispatchEvent(new Event("change"));

		setTimeout(function() {
			if (shi_select && shi_id) {
				shi_select.value = shi_id;
				shi_select.dispatchEvent(new Event("change"));

				setTimeout(function() {
					if (qu_select && qu_id) {
						qu_select.value = qu_id;
						qu_select.dispatchEvent(new Event("change"));
					}
				}, 100);
			}
		}, 100);
	}
}

// 页面加载完成后自动初始化
if (typeof document !== "undefined") {
	if (document.readyState === "loading") {
		document.addEventListener("DOMContentLoaded", gaode_china_init);
	} else {
		gaode_china_init();
	}
}
';
	}

	/*
	* @apiName 清理迁移数据重新开始
	* @method reset_migration_data
	* @POST
	* @return {"status":"ok","msg":"清理完成"}
	*/
	public function reset_migration_data() {
		dbConn();
		Db::begin();

		try {
			$cleaned = [];

			// 清理gaode_district表 - 使用TRUNCATE更高效
			$count1 = Db()->table("gaode_district")->count();
			if ($count1 > 0) {
				Db::_exec("TRUNCATE TABLE gaode_district");
				$cleaned[] = "gaode_district: {$count1}条";
			}

			// 清理映射表
			$count2 = Db()->table("china_adcode_mapping")->count();
			if ($count2 > 0) {
				Db::_exec("TRUNCATE TABLE china_adcode_mapping");
				$cleaned[] = "china_adcode_mapping: {$count2}条";
			}

			// 重置迁移状态 - 使用原生SQL处理NULL值
			Db::_exec("UPDATE migration_status SET status='pending', message=NULL, started_at=NULL, completed_at=NULL");
			$cleaned[] = "migration_status: 已重置";

			Db::commit();

			$message = empty($cleaned) ? "无需清理" : "已清理: " . implode(", ", $cleaned);
			return ["status" => "ok", "msg" => $message];

		} catch (\Throwable $e) {
			Db::rollback();
			return ["status" => "error", "msg" => "清理失败：" . $e->getMessage()];
		}
	}

	/*
	* @apiName 执行完整的高德数据迁移流程
	* @method execute_full_migration
	* @POST
	* @param key string 高德API密钥
	* @return {"status":"ok","msg":"迁移完成"}
	*/
	public function execute_full_migration($key = '') {
		if (empty($key)) {
			return ["status" => "error", "msg" => "请提供高德API密钥"];
		}

		$results = [];

		// 步骤1：获取高德数据
		$result1 = $this->import_gaode_data_to_db($key);
		$results['fetch_gaode_data'] = $result1;
		if ($result1['status'] !== 'ok') {
			return ["status" => "error", "msg" => "获取高德数据失败", "details" => $results];
		}

		// 步骤2：创建映射关系
		$result2 = $this->create_china_adcode_mapping();
		$results['create_mapping'] = $result2;
		if ($result2['status'] !== 'ok') {
			return ["status" => "error", "msg" => "创建映射关系失败", "details" => $results];
		}

		// 步骤3：迁移huodong表
		$result3 = $this->migrate_huodong_table();
		$results['migrate_huodong_table'] = $result3;
		if ($result3['status'] !== 'ok') {
			return ["status" => "error", "msg" => "迁移huodong表失败", "details" => $results];
		}

		// 步骤4：迁移user_addr表
		$result4 = $this->migrate_user_addr_table();
		$results['migrate_user_addr_table'] = $result4;
		if ($result4['status'] !== 'ok') {
			return ["status" => "error", "msg" => "迁移user_addr表失败", "details" => $results];
		}

		return ["status" => "ok", "msg" => "数据迁移完成", "details" => $results];
	}

	/*
	* @apiName 替换china表为高德数据
	* @method replace_china_with_gaode
	* @POST
	* @return {"status":"ok","msg":"替换完成"}
	*/
	public function replace_china_with_gaode() {
		dbConn();
		Db::begin();

		try {
			// 1. 删除原china表
			Db()->_execute("DROP TABLE IF EXISTS `huodong`.`china_old`");
			Db()->_execute("RENAME TABLE `huodong`.`china` TO `huodong`.`china_old`");

			// 2. 创建新的china表结构（兼容原有字段）
			$create_sql = "
				CREATE TABLE `huodong`.`china` (
				  `id` varchar(20) NOT NULL COMMENT '高德adcode作为主键',
				  `pid` varchar(20) NOT NULL DEFAULT '0' COMMENT '父级adcode',
				  `deep` tinyint(4) NOT NULL COMMENT '层级：1=省，2=市，3=区',
				  `name` varchar(100) NOT NULL COMMENT '名称',
				  `pinyin` varchar(100) DEFAULT NULL COMMENT '拼音',
				  `pinyin_prefix` varchar(10) DEFAULT NULL COMMENT '拼音首字母',
				  `pinyin_prefix2` varchar(20) DEFAULT NULL COMMENT '拼音缩写',
				  `center` varchar(50) DEFAULT NULL COMMENT '中心点坐标',
				  PRIMARY KEY (`id`),
				  KEY `pid` (`pid`),
				  KEY `deep` (`deep`)
				) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='高德行政区划数据表'
			";
			Db()->_execute($create_sql);

			// 3. 从gaode_district表导入数据到新china表
			$insert_sql = "
				INSERT INTO `huodong`.`china` (id, pid, deep, name, center)
				SELECT
					adcode as id,
					parent_adcode as pid,
					CASE
						WHEN level = 'province' THEN 1
						WHEN level = 'city' THEN 2
						WHEN level = 'district' THEN 3
						ELSE 4
					END as deep,
					name,
					center
				FROM `huodong`.`gaode_district`
				WHERE level IN ('province', 'city', 'district')
			";
			Db()->_execute($insert_sql);

			Db::commit();
			return ["status" => "ok", "msg" => "china表替换完成"];

		} catch (\Throwable $e) {
			Db::rollback();
			return ["status" => "error", "msg" => "替换失败：" . $e->getMessage()];
		}
	}

	/*
	* @apiName 获取小程序深度链接
	* @method get_deep_link
	* @POST
	* @param path string 小程序path
	* @param query string 小程序query
	* @return {"status":"ok","data":"http://test.com/test"}
	*/
	public function get_deep_link($path,$query){
		$url_link = \model\Wechat::get_url_link($path,$query);
		if(empty($url_link)){
			return ["status"=>"error","msg"=>"获取连接失败"];
		}
		return ["status"=>"ok","data"=>$url_link];
	}

	/*
	* @apiName 图片上传接口
	* @method upload_img
	* @POST
	* @param img string 图片二进制
	* @return {"status":"ok","data":"http://test.com/test.png"}
	*/
	public function upload_img(){
		if(
			empty($_FILES['img']) ||
			$_FILES['img']['error'] > 0
		)return ["status"=>"error","msg"=>"参数错误"];

        $source = "";
        if (isset($_POST['source'])) {
            $source = $_POST['source'];
        }

        $img_url = Upload()->move($_FILES['img'],"api", $source);

        if(empty($img_url)){
			return ["status"=>"error","msg"=>"上传失败"];
		}

        return ["status"=>"ok","data"=>$img_url];
	}

    public function qrcode($contents, $base64_decode = 0, $urldecode = 0) {
        Config::qrcodePureImage($contents,$base64_decode, $urldecode);
        //Config::qrcodeWithText($contents, $base64_decode, $urldecode);
    }

    public function qrcodeText($contents, $base64_decode = 0, $urldecode = 0) {
        //Config::qrcodePureImage($contents,$base64_decode, $urldecode);
        Config::qrcodeWithText($contents, $base64_decode, $urldecode);
    }

    public function phpinfo()
    {
        phpinfo();
        if (extension_loaded('gd')) {
            echo 'GD is installed';
        } else {
            echo 'GD is not installed';
        }
    }

    /*
	* @apiName 获取二维码图片二进制
	* @method qrcode
	* @POST
	* @param contents string 二维码内容
	* @param base64_decode string 是否base64编码,1=是,0=否（默认0）
	* @param urldecode string 是否url编码,1=是,0=否（默认0）
	* @return 二进制
	*/
    public static function qrcodePureImage($contents,$base64_decode=0,$urldecode=0){
        header("Content-Type: image/png");
        if(!empty($base64_decode)){
            $contents = base64_decode($contents);
        }
        if(!empty($urldecode)){
            $contents = urldecode($contents);
        }
        echo \lib\Qrcode::png($contents);
    }

    public static function qrcodeWithText($contents, $base64_decode = 0, $urldecode = 0) {
        if (!empty($base64_decode)) {
            $contents = base64_decode($contents);
        }
        if (!empty($urldecode)) {
            $contents = urldecode($contents);
        }

        // 获取二维码图片资源
        $qrImage = \lib\Qrcode::pngWithText($contents, true);

        // 添加文字到二维码图片
        $line1 = 'MeetUP';
        $line2 = 'MeetUP，小聚会';
        $finalImage = self::addTextToQRCode($qrImage, $line1, $line2);

        // 输出新的图片
        header("Content-Type: image/png");
        imagepng($finalImage);
        imagedestroy($finalImage);
    }

    private static function addTextToQRCode($qrImage, $line1, $line2) {
        // 固定二维码大小为 125px x 125px
        $qrWidth = 125;
        $qrHeight = 125;

        // 字体大小和颜色
        $fontSize = 14; // 字体大小设置为 14px
        $fontColorHex = "#919191";
        list($r, $g, $b) = sscanf($fontColorHex, "#%02x%02x%02x");

        // 字体路径，确保服务器上有该字体文件
        $fontPath = "../access/font/simhei.ttf"; // 替换为实际的字体文件路径

        // 计算每行文字的边界框
        $bbox1 = imagettfbbox($fontSize, 0, $fontPath, $line1);
        $line1Width = $bbox1[2] - $bbox1[0];
        $bbox2 = imagettfbbox($fontSize, 0, $fontPath, $line2);
        $line2Width = $bbox2[2] - $bbox2[0];
        $lineHeight = max(abs($bbox1[7] - $bbox1[1]), abs($bbox2[7] - $bbox2[1]));

        // 计算整体高度（二维码 + 两行文字 + 间距）
        $textHeight = 2 * $lineHeight + 20; // 两行文字的高度加上两行间距
        $contentHeight = $qrHeight + $textHeight + 10; // 二维码底部到第一行文字10px的间距
        $contentWidth = max($qrWidth, $line1Width, $line2Width);

        // 计算画布宽高，添加 10px 边距
        $canvasWidth = $contentWidth + 20;
        $canvasHeight = $contentHeight + 20;

        // 创建画布
        $canvas = imagecreatetruecolor($canvasWidth, $canvasHeight);

        // 设置背景色为白色
        $white = imagecolorallocate($canvas, 255, 255, 255);
        imagefill($canvas, 0, 0, $white);

        // 将二维码图片复制到画布，并居中
        $qrX = intval(($canvasWidth - $qrWidth) / 2);
        $qrY = 10; // 距离顶部10px
        imagecopyresampled($canvas, $qrImage, $qrX, $qrY, 0, 0, $qrWidth, $qrHeight, imagesx($qrImage), imagesy($qrImage));

        // 设置文字颜色
        $fontColor = imagecolorallocate($canvas, $r, $g, $b);

        // 计算文字的位置，居中显示
        $textX1 = intval(($canvasWidth - $line1Width) / 2);
        $textY1 = $qrY + $qrHeight + 10 + $lineHeight; // 二维码底部到第一行文字的间距10px
        $textX2 = intval(($canvasWidth - $line2Width) / 2);
        $textY2 = $textY1 + $lineHeight + 10; // 第一行文字到第二行文字的间距10px

        // 在画布上绘制文字
        imagettftext($canvas, $fontSize, 0, $textX1, $textY1, $fontColor, $fontPath, $line1);
        imagettftext($canvas, $fontSize, 0, $textX2, $textY2, $fontColor, $fontPath, $line2);

        return $canvas;
    }

	/*
	* @apiName 获取微信小程序码
	* @method wxacode
	* @GET
	* @param path string 小程序页面路径 (需要 urlencode)
	* @param query string 携带参数 (需要 urlencode)
	* @return 图片二进制流
	*/
	public function wxacode($path = '', $query = ''){
		// --- 恢复原有代码 ---
		// 基本验证
		if(empty($path)){
			header("HTTP/1.1 400 Bad Request");
			echo json_encode(["status"=>"error", "msg"=>"缺少 path 参数"]);
			exit;
		}

		// 对参数进行 urldecode
		$decoded_path = urldecode($path);
		$decoded_query = urldecode($query);

		// 添加日志：记录接收到的参数 (保留)
		$this->exception_log("wxacode: Decoded Path='{$decoded_path}', Decoded Query='{$decoded_query}'");

		// 调用模型获取小程序码
		try {
			// 添加日志：准备调用模型方法 (保留)
			$this->exception_log("wxacode: Attempting to call \model\Wechat::get_xiaochengxuma with Scene='{$decoded_query}', Page='{$decoded_path}'");

			$imageData = \model\Wechat::get_xiaochengxuma($decoded_query, $decoded_path);

			if ($imageData) {
				// 成功获取图片数据
				header("Content-Type: image/png"); // 或者根据实际返回类型设置
				echo $imageData;
				exit;
			} else {
				// 模型层返回失败
				$this->exception_log("调用 get_xiaochengxuma 失败或未返回数据。Path: {$decoded_path}, Query: {$decoded_query}");
				header("HTTP/1.1 500 Internal Server Error");
				echo json_encode(["status"=>"error", "msg"=>"生成小程序码失败"]);
				exit;
			}
		} catch (\Throwable $e) {
			// 捕获模型层可能抛出的异常
			$this->exception_log("调用 get_xiaochengxuma 异常: " . $e->getMessage() . " Path: {$decoded_path}, Query: {$decoded_query}");
			header("HTTP/1.1 500 Internal Server Error");
			echo json_encode(["status"=>"error", "msg"=>"生成小程序码时发生内部错误"]);
			exit;
		}
		// --- 原有代码结束 ---
	}

	/*
	* @apiName 清空字段内容
	* @method clear
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param column string 字段
	* @return
	*/
	public function clear($uid,$token,$column){
		if(
			empty($uid) ||
			!check($uid,"intgt0") ||
			empty($token) ||
			strlen($token) != 32 ||
			!in_array($column,["mobile","avatar","nickname","token"])
		)return ["status"=>"error","msg"=>"参数错误"];
		if(!$this->auth($uid,$token))return ["status"=>"relogin","msg"=>"登录信息验证失败"];
		$uid = (int)$uid;
		dbConn();
		try{
			$val = '';
			if($column == "mobile"){
				$val = "1570000" . makeCode(4);
			}
			$sql = "UPDATE `user` SET `{$column}`=:val WHERE uid=:uid";
			Db()->_exec($sql, [":val" => $val, ":uid" => $uid]);
			return ["status"=>"ok"];
		}catch(\Throwable $e){
			return ["status"=>"error","msg"=>$e->getMessage()];
		}
	}

	/*
	* @apiName 获取小程序封面页配置
	* @method get_splash_config
	* @GET
	* @param 无
	* @return {"status":"ok","data":{"splash_image_url":"..."}} | {"status":"error","msg":"..."}
	*/
	public function get_splash_config() {
		dbConn();
		try {
			$splash_image_url = Db()->table("config")
				->select("splash_image_url")
				->where("1")
				->limit(1)
				->getColumn("splash_image_url");

			if (empty($splash_image_url)) {
				// 如果没有配置封面图，返回默认图片或空
				$splash_image_url = "";
			}

			return ["status" => "ok", "data" => [
				"splash_image_url" => $splash_image_url
			]];

		} catch (\Throwable $e) {
			$this->exception_log($e->getMessage());
			return ["status" => "error", "msg" => "获取封面配置失败"];
		}
	}

	public function _empty(){
		return ["status"=>"error","msg"=>"URL error"];
	}

	function __destruct(){

	}
}
