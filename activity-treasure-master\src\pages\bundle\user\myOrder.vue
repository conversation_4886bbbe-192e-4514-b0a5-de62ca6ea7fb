<script setup>
import { defineComponent, watch, ref, reactive } from "vue";
import {
  goodsget_order_list,
  goodscancel_order,
  goodsqueren_shouhuo,
  payyue_pay,
  payweixin_pay,
  payget_weixinpay_sign,
} from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import { store } from "@/store";
import useMescroll from "@/uni_modules/mescroll-uni/hooks/useMescroll.js";
import { setListHeight, navto, getItem, pay } from "@/utils";

const tabList = ref([
  {
    name: "全部",
  },
  {
    name: "待付款",
  },
  {
    name: "待发货",
  },
  {
    name: "待收货",
  },
  {
    name: "已完成",
  },
  {
    name: "退款/售后",
  },
  {
    name: "待评价",
  },
]);
const form = ref({
  status: "all",
});
const goods = ref([]);
// 调用mescroll的hook (注: mescroll-uni不用传onPageScroll,onReachBottom, 而mescroll-body必传)
const { mescrollInit, downCallback, getMescroll } = useMescroll(
  onPageScroll,
  onReachBottom
);
const height = ref("");
const actionsList = ref([
  {
    name: "余额支付",
    color: "#ffaa7f",
    fontSize: "18",
  },
  {
    name: "微信支付",
    color: "#ffaa7f",
    fontSize: "18",
  },
]);
const show = ref(false);
const payQuery = ref({
  order_id: null,
  money: null,
});
const vip = ref(false);

onLoad((e) => {
  // 大礼包功能已删除
});
onShow(() => {
  getMescroll().resetUpScroll(true);
});
onReady(async () => {
  height.value = (await setListHeight()) + "px";
});

// 上拉加载的回调: 其中num:当前页 从1开始, size:每页数据条数,默认10
const upCallback = async (mescroll) => {
  const api = goodsget_order_list;
  api({
    page: mescroll.num,
    page_size: mescroll.size,
    ...form.value,
  })
    .then((res) => {
      const curPageData = res.data || [];
      if (mescroll.num == 1) goods.value = [];
      goods.value = goods.value.concat(curPageData);
      mescroll.endBySize(curPageData.length, res.count);
    })
    .catch(() => {
      mescroll.endErr();
    });
};
const quxiao = async (order_id) => {
  const api = goodscancel_order;
  const res = await api({ order_id });
  if (res.status === "ok") {
    uni.$u.toast("取消成功");
    getMescroll().resetUpScroll(true);
  } else uni.$u.toast(res.msg);
};
const sure = async (order_id) => {
  const api = vip.value ? dalibaoqueren_shouhuo : goodsqueren_shouhuo;
  const res = await api({ order_id });
  if (res.status === "ok") {
    uni.$u.toast("确认成功");
    getMescroll().resetUpScroll(true);
  } else uni.$u.toast(res.msg);
};
// 支付
const selectClick = async (e) => {
  let payRes;
  if (e.name === "余额支付")
    payRes = await payyue_pay({ ...payQuery.value, type: vip.value ? 4 : 1 });
  else {
    const wxRes = await payweixin_pay({ ...payQuery.value, type: vip.value ? 5 : 1 });
    if (wxRes.status === "ok") {
      const signRes = await payget_weixinpay_sign({ prepay_id: wxRes.prepay_id });
      payRes = await pay(signRes);
      if (payRes.errMsg === "requestPayment:ok") {
        payRes.status = "ok";
        payRes.msg = "支付成功";
      } else payRes.msg = "支付失败";
    }
  }
  if (payRes?.status === "ok") {
    uni.$u.toast("支付成功");
    getMescroll().resetUpScroll(true);
  } else uni.$u.toast(payRes?.msg);
};
// 切换tab栏
const changeTabs = (e) => {
  switch (Number(e.index)) {
    case 0:
      form.value.status = "all";
      break;
    case 1:
    case 2:
    case 3:
    case 4:
      form.value.status = e.index - 1 + "";
      break;
    case 5:
      form.value.status = "5,6,7";
      break;
    case 6:
      form.value.status = "8,9,10";
      break;
  }
  goods.value = [];
  getMescroll().resetUpScroll(true);
};
</script>
<template>
  <view class="page">
    <u-tabs
      :list="tabList"
      :active-style="{
        borderRadius: '26rpx',
        textAlign: 'center',
        lineHeight: '52rpx',
        fontSize: '28rpx',
        color: '#333333',
      }"
      :inactiveStyle="{
        fontSize: '24rpx',
        color: '#999999',
      }"
      :itemStyle="{
        padding: '20rpx 30rpx',
      }"
      lineWidth="0"
      @click="changeTabs"
    ></u-tabs>
    <view class="px30">
      <mescroll-uni
        class="list"
        :down="{
          auto: false,
        }"
        :height="height"
        @init="mescrollInit"
        @down="downCallback"
        @up="upCallback"
        @topclick="$event.scrollTo(0)"
      >
        <view class="mb10 py20 pl30 pr30 b6f r20" v-for="(val, i) in goods" :key="i">
          <view class="df aic jcsb pb10" @click="store().setGoodInfo(val, false, vip)">
            <u-text size="24rpx" color="#aaa" :text="val.create_time"></u-text>
            <u-text
              align="rigth"
              :text="
                getItem(
                  [
                    '等待买家付款',
                    '等待卖家发货',
                    '卖家已发货',
                    '交易成功',
                    '交易关闭',
                    '退款中',
                    '退款成功',
                    '退款失败',
                  ],
                  val.status
                )
              "
            ></u-text>
          </view>
          <!-- <view> -->
          <view v-if="vip" @click="store().setGoodInfo(val, false, true)">
            <view class="df aic">
              <view>
                <u-text size="26rpx" text="商品名称："></u-text>
              </view>
              <u-text size="26rpx" :text="val.goods_name"></u-text>
            </view>
            <view class="df aic">
              <u-text size="26rpx" :text="val.order_id"></u-text>
              <view class="df aic">
                <u-text
                  mode="price"
                  color="#EF6227"
                  size="26rpx"
                  align="right"
                  :text="val.money"
                ></u-text>
                <u-text size="18rpx" color="#AAAAAA" align="right" text="x1"></u-text>
              </view>
            </view>
          </view>
          <template v-else>
            <view
              class="df pb10 mb10 borderBottom"
              @click="store().setGoodInfo(val)"
              v-for="(value, index) in val.goods_info"
              :key="index"
            >
              <u-image width="188rpx" height="188rpx" :src="value.img_url"></u-image>
              <view class="ml20 df fdc jcsb f1">
                <u-text size="28rpx" :text="value.goods_name"></u-text>
                <view class="df aic jcsb">
                  <view class="df aic">
                    <view>
                      <u-text size="26rpx" text="规格："></u-text>
                    </view>
                    <view v-for="(item, indexs) in value.guige_info" :key="indexs">
                      <u-text size="26rpx" :text="`${indexs}:${item},`"></u-text>
                    </view>
                  </view>
                  <u-text
                    color="#AAAAAA"
                    align="right"
                    size="18rpx"
                    :text="`x${value.num}`"
                  ></u-text>
                </view>
                <u-text
                  mode="price"
                  size="18rpx"
                  color="#EF6227"
                  :text="value.money"
                ></u-text>
              </view>
            </view>
          </template>
          <!-- </view> -->
          <view class="df aic jcr">
            <u-button
              v-if="val.status === 0"
              shape="circle"
              color="#aaa"
              plain
              text="取消订单"
              :customStyle="{
                margin: '10rpx 10rpx 0 0',
                width: '140rpx',
                height: '48rpx',
                fontSize: '22rpx',
              }"
              @click.stop="quxiao(val.order_id)"
            ></u-button>
            <u-button
              v-if="val.status === 0"
              shape="circle"
              color="#FA8700"
              plain
              text="继续付款"
              :customStyle="{
                margin: '10rpx 0 0',
                width: '140rpx',
                height: '48rpx',
                fontSize: '22rpx',
              }"
              @click.stop="
                show = true;
                payQuery.type = 1;
                payQuery.order_id = val.order_id;
                payQuery.money = val.money;
              "
            ></u-button>
            <u-button
              v-if="(val.status === 1 && !vip) || (val.status === 2 && !vip)"
              shape="circle"
              color="#aaa"
              plain
              :text="val.is_shenqing_tuikuan ? '退款中' : '申请退款'"
              :disabled="val.is_shenqing_tuikuan ? true : false"
              :customStyle="{
                margin: '10rpx 10rpx 0',
                width: '140rpx',
                height: '48rpx',
                fontSize: '22rpx',
              }"
              @click.stop="
                navto(`/pages/bundle/user/returnGood?order_id=${val.order_id}`)
              "
            ></u-button>
            <u-button
              v-if="val.status === 2"
              shape="circle"
              color="#FA8700"
              plain
              text="确认收货"
              :customStyle="{
                margin: '10rpx 0 0',
                width: '140rpx',
                height: '48rpx',
                fontSize: '22rpx',
              }"
              @click.stop="sure(val.order_id)"
            ></u-button>
            <u-button
              v-if="val.is_pingjia == 0 && val.status === 3 && !vip"
              shape="circle"
              color="#FA8700"
              plain
              text="去评价"
              :customStyle="{
                margin: '10rpx 0 0',
                width: '140rpx',
                height: '48rpx',
                fontSize: '22rpx',
              }"
              @click.stop="store().setGoodInfo(val, true)"
            ></u-button>
          </view>
        </view>
      </mescroll-uni>
    </view>
    <u-action-sheet
      title="支付方式选择"
      :close-on-click-action="true"
      :actions="actionsList"
      :show="show"
      @close="show = false"
      @select="selectClick"
    ></u-action-sheet>
  </view>
</template>

<style scoped lang="less"></style>
