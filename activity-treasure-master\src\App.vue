<script setup>
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";
import { configapp, configpop } from "@/api";
import { store } from "@/store";
import { restoreLoginState } from "@/utils/auth";

// {{ AURA-X: Add - 修复uView组件库使用已废弃API的警告. Confirmed via 寸止. }}
// 全局替换已废弃的getSystemInfoSync API
import { getSystemInfo } from "@/utils/systemInfo";

// 在应用启动前替换已废弃的API
const originalGetSystemInfoSync = uni.getSystemInfoSync;
uni.getSystemInfoSync = () => {
  try {
    // 使用新的API替换方案
    return getSystemInfo();
  } catch (error) {
    console.warn('新API获取系统信息失败，使用原始API:', error);
    // 降级到原始API但不显示警告
    return originalGetSystemInfoSync();
  }
};

// 同时替换uView可能使用的$u.sys()方法
if (uni.$u && uni.$u.sys) {
  const originalSys = uni.$u.sys;
  uni.$u.sys = () => {
    try {
      return getSystemInfo();
    } catch (error) {
      console.warn('$u.sys()新API失败，使用原始方法:', error);
      return originalSys();
    }
  };
}

onLaunch(async () => {
  try {
    // 应用启动时首先恢复登录状态
    console.log('应用启动，尝试恢复登录状态...');
    const restored = restoreLoginState();
    if (restored) {
      console.log('登录状态恢复成功');
    } else {
      console.log('无有效的登录状态需要恢复');
    }

    const res = await configapp();

    // 检查配置获取是否成功
    if (res?.status === "ok" && res?.data) {
      // 保存配置数据到store
      store().changeConfig(res.data);
    }

    // 获取弹出公告
    const popRes = await configpop();
    if (popRes?.status == "ok") {
      store().setPopContent(popRes.data);
    }
  } catch (error) {
    console.error('获取App配置出错:', error);
  }
});

onShow(() => {
  // App显示时的处理
});

onHide(() => {
  // App隐藏时的处理
});


</script>

<template>
  <!-- 主应用内容 -->
  <view>
    <slot></slot>
  </view>
</template>

<style lang="scss">
@import "uview-plus/index.scss";
@import "./style/judu-theme.scss";

/* {{ AURA-X: Modify - 修复小程序不支持标签选择器的问题，改为class选择器. Confirmed via 寸止. }} */
/* 修复小程序不支持标签选择器的问题 */
.ql-editor {
  /* 覆盖 ul 标签选择器样式 - 改为class选择器 */
  .ul-style,
  .ql-list-ul {
    margin: 0;
    padding: 0;
    counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
  }

  /* 覆盖 ul > li 标签选择器样式 - 改为class选择器 */
  .ul-li-style,
  .ql-list-ul .ql-list-item {
    list-style-type: none;
  }

  .ul-li-style:before,
  .ql-list-ul .ql-list-item:before {
    content: "•";
  }
}
</style>
<style lang="less">
@import url("style/base.less");
/*每个页面公共css */

/* {{ AURA-X: Modify - 修复小程序不支持标签选择器问题，改为class选择器. Confirmed via 寸止. }} */
/* 全局字体栈声明 - 微信小程序兼容版本 */
.page-font-family {
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  line-height: 1.6;
}

/* 具体组件字体声明 - 修复小程序不支持标签选择器问题 */
.font-inherit {
  font-family: inherit;
}

/* 为常用组件添加字体继承类 */
.uni-view, .uni-text, .uni-button, .uni-input, .uni-textarea, .uni-picker, .uni-navigator {
  font-family: inherit;
}

/* 确保所有文本元素继承字体 */
.uni-input-input, .uni-textarea-textarea {
  font-family: inherit;
}







/* 应用基础样式 */

/* 全局色彩系统 - 基于新配色方案 */
:root {
  /* 主色调系统 */
  --primary-color: #245D3C;        /* 深绿色 - 主色 */
  --primary-light: #3AEE55;        /* 亮绿色 - 强调色 */
  --primary-medium: #73C088;       /* 中绿色 - 辅助色 */
  --primary-soft: #A5E1B8;         /* 浅绿色 - 柔和色 */
  --primary-bg: #C8E6D1;           /* 极浅绿色 - 背景色 */

  /* 中性色系统 */
  --neutral-white: #FFFFFF;
  --neutral-light: #F8F9FA;
  --neutral-gray: #E5E5E5;
  --neutral-dark: #333333;
  --neutral-text: #666666;

  /* 功能色系统 */
  --success-color: #3AEE55;
  --warning-color: #FF9500;
  --error-color: #FF4757;
  --info-color: #245D3C;

  /* 阴影系统 */
  --shadow-light: 0 4px 10px rgba(36, 93, 60, 0.05);
  --shadow-medium: 0 8px 15px rgba(36, 93, 60, 0.1);
  --shadow-heavy: 0 12px 24px rgba(36, 93, 60, 0.15);

  /* 渐变系统 */
  --gradient-primary: linear-gradient(135deg, #3AEE55 0%, #73C088 100%);
  --gradient-soft: linear-gradient(135deg, #A5E1B8 0%, #C8E6D1 100%);
  --gradient-bg: linear-gradient(180deg, #C8E6D1 0%, #F8F9FA 50%);
}

/* 全局按钮呼吸感阴影效果 */
.btn-enhanced,
.u-button,
.publish-btn,
.submit-btn,
.action-btn,
.primary-btn {
  box-shadow: var(--shadow-light);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-enhanced:hover,
.u-button:hover,
.publish-btn:hover,
.submit-btn:hover,
.action-btn:hover,
.primary-btn:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-medium);
}

/* 玻璃拟态效果 */
.glass-effect,
.navbar-glass,
.card-glass,
.popup-glass {
  background: rgba(255,255,255,0.6);
  border: 1px solid rgba(255,255,255,0.8);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* 关键卡片玻璃效果 */
.activity-card,
.user-card,
.world-card,
.feed-card {
  background: rgba(255,255,255,0.6);
  border: 1px solid rgba(255,255,255,0.8);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  box-shadow: var(--shadow-light);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.activity-card:hover,
.user-card:hover,
.world-card:hover,
.feed-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

/* 主题色彩应用 */
.primary-color { color: var(--primary-color) !important; }
.primary-bg { background-color: var(--primary-color) !important; }
.primary-light-color { color: var(--primary-light) !important; }
.primary-light-bg { background-color: var(--primary-light) !important; }
.primary-gradient { background: var(--gradient-primary) !important; }
.primary-soft-bg { background-color: var(--primary-soft) !important; }
</style>
