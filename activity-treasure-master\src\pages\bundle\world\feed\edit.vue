<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { getFeedDetail, editFeed, upload_img } from '@/api/index.js';
import { store } from '@/store';
import { onLoad } from '@dcloudio/uni-app';
import { requireLogin } from '@/utils/auth';
import customNavbar from '@/components/customNavbar.vue';

// {{ AURA-X: Add - 实现动态编辑页面. Confirmed via 寸止. }}

// 状态管理
const content = ref(''); // 动态内容
const images = ref([]); // 图片列表
const location = ref(null); // 位置信息
const tags = ref(''); // 标签
const privacy = ref('public'); // 隐私设置
const isPrivate = ref(false); // 是否设为私密
const isSubmitting = ref(false); // 提交状态
const isLoading = ref(true); // 加载状态
const feedId = ref(null); // 动态ID

// 隐私选项
const privacyOptions = [
  { label: '公开', value: 'public' },
  { label: '私密', value: 'private' }
];

// 计算最大上传图片数量（会员4张，非会员1张）
const maxImageCount = computed(() => {
    const userInfo = store().$state.userInfo;
    const isVip = userInfo?.role_type === 1 || userInfo?.role_type === 2; // 1是会员，2是超级会员
    return isVip ? 4 : 1;
});

// 位置显示文本
const locationDisplay = computed(() => {
    return location.value ? (location.value.name || location.value.address) : '添加位置';
});

// 页面加载时获取动态数据
onLoad(async (options) => {
    if (!options.id) {
        uni.showToast({ title: '参数错误', icon: 'none' });
        uni.navigateBack();
        return;
    }
    
    feedId.value = options.id;
    await loadFeedData();
});

// 加载动态数据
const loadFeedData = async () => {
    try {
        isLoading.value = true;
        const userInfo = store().$state.userInfo;
        
        const response = await getFeedDetail({
            id: feedId.value,
            uid: userInfo.uid,
            token: userInfo.token
        });
        
        if (response.status === 'ok' && response.data) {
            const feed = response.data;
            
            // 检查权限
            if (feed.user_id !== userInfo.uid) {
                uni.showToast({ title: '您无权编辑此动态', icon: 'none' });
                uni.navigateBack();
                return;
            }
            
            // 填充数据
            content.value = feed.content || '';
            tags.value = feed.tags || '';
            privacy.value = feed.privacy || 'public';
            isPrivate.value = feed.privacy === 'private';
            
            // 处理位置信息
            if (feed.location) {
                location.value = { name: feed.location };
            }
            
            // 处理图片
            if (feed.images && feed.images.length > 0) {
                images.value = feed.images.map((img, index) => ({
                    url: img,
                    status: 'success',
                    message: '',
                    uid: Date.now() + index
                }));
            }
        } else {
            uni.showToast({ title: response.msg || '加载失败', icon: 'none' });
            uni.navigateBack();
        }
    } catch (error) {
        console.error('加载动态数据失败:', error);
        uni.showToast({ title: '加载失败', icon: 'none' });
        uni.navigateBack();
    } finally {
        isLoading.value = false;
    }
};

// 处理图片上传
const handleAfterRead = async (event) => {
    let lists = [].concat(event.file);
    let fileListLen = images.value.length;

    lists.map((item) => {
        images.value.push({
            ...item,
            status: 'uploading',
            message: '上传中'
        });
    });

    for (let i = 0; i < lists.length; i++) {
        const result = await uploadFilePromise(lists[i].file);
        let item = images.value[fileListLen];
        images.value.splice(fileListLen, 1, Object.assign(item, {
            status: result.success ? 'success' : 'failed',
            message: result.success ? '' : result.message,
            url: result.success ? result.data : ''
        }));
        fileListLen++;
    }
};

// 上传文件Promise
const uploadFilePromise = (file) => {
    return new Promise((resolve, reject) => {
        let formData = new FormData();
        formData.append('file', file);
        
        upload_img(formData).then(res => {
            if (res.status === 'ok') {
                resolve({ success: true, data: res.data.url });
            } else {
                resolve({ success: false, message: res.msg || '上传失败' });
            }
        }).catch(err => {
            resolve({ success: false, message: '上传失败' });
        });
    });
};

// 删除图片
const handleDelete = (event) => {
    images.value.splice(event.index, 1);
};

// 隐私设置切换
const handlePrivacyChange = (value) => {
    privacy.value = value ? 'private' : 'public';
};

// 选择位置
const chooseLocation = () => {
    uni.chooseLocation({
        success: (res) => {
            location.value = {
                name: res.name,
                address: res.address,
                latitude: res.latitude,
                longitude: res.longitude
            };
        },
        fail: (err) => {
            console.log('选择位置失败:', err);
        }
    });
};

// 提交编辑
const handleSubmit = async () => {
    if (!requireLogin()) return;
    
    if (!content.value.trim()) {
        uni.showToast({ title: '请输入动态内容', icon: 'none' });
        return;
    }
    
    if (isSubmitting.value) return;
    
    try {
        isSubmitting.value = true;
        uni.showLoading({ title: '保存中...' });
        
        const userInfo = store().$state.userInfo;
        const imageUrls = images.value
            .filter(img => img.status === 'success' && img.url)
            .map(img => img.url);
        
        const params = {
            uid: userInfo.uid,
            token: userInfo.token,
            feed_id: feedId.value,
            content: content.value.trim(),
            images: imageUrls,
            location: location.value ? location.value.name : '',
            tags: tags.value.trim(),
            privacy: privacy.value
        };
        
        const response = await editFeed(params);
        
        if (response.status === 'ok') {
            uni.hideLoading();
            uni.showToast({ title: '保存成功', icon: 'success' });

            // {{ AURA-X: Add - 通知详情页刷新数据. Confirmed via 寸止. }}
            // 通知详情页刷新数据
            uni.$emit('feed-updated', { id: feedId.value });

            // 延迟返回，让用户看到成功提示
            setTimeout(() => {
                uni.navigateBack();
            }, 1500);
        } else {
            uni.hideLoading();
            uni.showToast({ title: response.msg || '保存失败', icon: 'none' });
        }
    } catch (error) {
        console.error('保存动态失败:', error);
        uni.hideLoading();
        uni.showToast({ title: '保存失败，请重试', icon: 'none' });
    } finally {
        isSubmitting.value = false;
    }
};

// 返回
const handleClose = () => {
    uni.navigateBack();
};
</script>

<template>
  <view class="feed-edit-page">
    <!-- 自定义导航栏 -->
    <customNavbar 
      title="编辑动态" 
      :showBack="true" 
      @back="handleClose"
    />
    
    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-container">
      <u-loading-icon mode="spinner" color="#6AC086" size="40"></u-loading-icon>
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 编辑表单 -->
    <view v-else class="edit-form">
      <!-- 内容输入 -->
      <view class="content-section">
        <u-textarea
          v-model="content"
          placeholder="分享你的动态..."
          :maxlength="5000"
          :showWordLimit="true"
          :autoHeight="true"
          :minHeight="200"
          class="content-textarea"
        />
      </view>
      
      <!-- 图片上传 -->
      <view class="image-section">
        <view class="section-title">添加图片</view>
        <u-upload
          :fileList="images"
          @afterRead="handleAfterRead"
          @delete="handleDelete"
          :maxCount="maxImageCount"
          :multiple="true"
          :previewFullImage="true"
          accept="image"
          :maxSize="400 * 1024"
          @oversize="() => uni.showToast({ title: '图片大小不能超过400KB', icon: 'none' })"
        />
        <view class="upload-tip">
          最多上传{{ maxImageCount }}张图片，单张不超过400KB
        </view>
      </view>
      
      <!-- 位置信息 -->
      <view class="location-section">
        <view class="section-title">位置</view>
        <view class="location-selector" @click="chooseLocation">
          <u-icon name="map" color="#6AC086" size="20"></u-icon>
          <text class="location-text">{{ locationDisplay }}</text>
          <u-icon name="arrow-right" color="#999" size="16"></u-icon>
        </view>
      </view>
      
      <!-- 标签输入 -->
      <view class="tags-section">
        <view class="section-title">标签</view>
        <u-input
          v-model="tags"
          placeholder="添加标签，用逗号分隔"
          :maxlength="100"
          class="tags-input"
        />
      </view>
      
      <!-- 隐私设置 -->
      <view class="privacy-section">
        <view class="section-title">隐私设置</view>
        <view class="privacy-toggle">
          <text class="privacy-label">设为私密</text>
          <u-switch 
            v-model="isPrivate" 
            @change="handlePrivacyChange"
            activeColor="#6AC086"
          />
        </view>
        <view class="privacy-tip">私密动态仅自己可见</view>
      </view>
    </view>
    
    <!-- 提交按钮 -->
    <view class="submit-section">
      <u-button
        type="primary"
        :loading="isSubmitting"
        :disabled="!content.trim() || isSubmitting"
        @click="handleSubmit"
        class="submit-btn"
        color="#6AC086"
      >
        {{ isSubmitting ? '保存中...' : '保存' }}
      </u-button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.feed-edit-page {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: 120rpx;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}

.edit-form {
  padding: 32rpx;
}

.content-section {
  margin-bottom: 40rpx;
}

.content-textarea {
  background: white;
  border-radius: 20rpx;
  padding: 24rpx;
}

.image-section,
.location-section,
.tags-section,
.privacy-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.upload-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
}

.location-selector {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 20rpx;
  padding: 24rpx;
}

.location-text {
  flex: 1;
  margin-left: 16rpx;
  font-size: 28rpx;
  color: #333;
}

.tags-input {
  background: white;
  border-radius: 20rpx;
  padding: 24rpx;
}

.privacy-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: white;
  border-radius: 20rpx;
  padding: 24rpx;
}

.privacy-label {
  font-size: 28rpx;
  color: #333;
}

.privacy-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #eee;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 600;
}
</style>
