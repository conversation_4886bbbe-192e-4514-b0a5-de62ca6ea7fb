<?php
namespace controller;
use core\Controller;
use core\Db;
/*
 * @className 轮播图
*/
class Lunbotu extends Controller{

	public function __construct(){
		parent::__construct();
	}

	/*
	* @apiName 获取轮播图
	* @method index
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @return {"status":"ok","data":[{"img_url":"http:\/\/127.0.0.1\/test.jpg","title":"\u6d4b\u8bd5","url_type":2,"url_params":"1"}]}
	*/
	public function index($uid=0,$token="",$position=1){
		if(!check($position,"intgt0")){
			return ["status"=>"error","msg"=>"参数错误"];
		}
		$uid = (int)$uid;
		$is_huiyuan = 0;

		// 修复：轮播图支持公开访问，认证失败不阻止访问
		if(!empty($uid) && !empty($token)){
			// 尝试认证，但认证失败不返回错误，继续按非会员处理
			if($this->auth($uid,$token)){
				// 认证成功，获取会员状态
				$is_huiyuan = Db()->table("user")->where("uid={$uid}")->getColumn("is_huiyuan",0);
			}
			// 认证失败时，$is_huiyuan保持为0（非会员），继续执行
		}

		$position = (int)$position;
		if($is_huiyuan && $is_huiyuan == 1){
			$where = "is_show=1 AND position={$position} AND type IN (0,1)";
		}else{
			$where = "is_show=1 AND position={$position} AND type IN (0,2)";
		}
		$data = \core\Cache::getCache("lunbotu_{$position}_{$is_huiyuan}");
		if(!empty($data))return ["status"=>"ok","data"=>$data];
		dbConn();
		$data = Db()->table("lunbotu")->select("img_url,title,url_type,url_params")->where($where)->order("sort ASC,id DESC")->fetchAll();

        if(empty($data)){
			return ["status"=>"empty"];
		}else{
			//\core\Cache::getCache("lunbotu_{$position}_{$is_huiyuan}",$data,60);
			return ["status"=>"ok","data"=>$data];
		}
	}

	public function _empty(){
		return ["status"=>"error","msg"=>"URL error"];
	}

	function __destruct(){

	}
}
