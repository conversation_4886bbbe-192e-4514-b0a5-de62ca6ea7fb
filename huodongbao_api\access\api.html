<!DOCTYPE html>
<html lang="zh-cn">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="description" content="">
        <title>api</title>
		<link rel="stylesheet" href="/asset/css/bootstrap.min.3.0.3.css">
		<link href="/asset/css/font-awesome.min.4.6.2.css" rel="stylesheet">
        <style type="text/css">
            body {
                padding-top: 70px; margin-bottom: 15px;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
                font-family: "Roboto", "SF Pro SC", "SF Pro Display", "SF Pro Icons", "PingFang SC", BlinkMacSystemFont, -apple-system, "Segoe UI", "Microsoft Yahei", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
                font-weight: 400;
            }
            h2        { font-size: 1.6em; }
            hr        { margin-top: 10px; }
            .tab-pane { padding-top: 10px; }
            .mt0      { margin-top: 0px; }
            .footer   { font-size: 12px; color: #666; }
            .label    { display: inline-block; min-width: 65px; padding: 0.3em 0.6em 0.3em; }
            .string   { color: green; }
            .number   { color: darkorange; }
            .boolean  { color: blue; }
            .null     { color: magenta; }
            .key      { color: red; }
            .popover  { max-width: 400px; max-height: 400px; overflow-y: auto;}
            .list-group.panel > .list-group-item {
            }
            .list-group-item:last-child {
                border-radius:0;
            }
            h4.panel-title a {
                font-weight:normal;
                font-size:14px;
            }
            h4.panel-title a .text-muted {
                font-size:12px;
                font-weight:normal;
                font-family: 'Verdana';
            }
            #sidebar {
                width: 220px;
                position: fixed;
                margin-left: -240px;
                overflow-y:auto;
            }
            #sidebar > .list-group {
                margin-bottom:0;
            }
            #sidebar > .list-group > a{
                text-indent:0;
            }
            #sidebar .child {
                border:1px solid #ddd;
                border-bottom:none;
            }
            #sidebar .child > a {
                border:0;
            }
            #sidebar .list-group a.current {
                background:#f5f5f5;
            }
			.container{
				width:800px;
			}
            @media (max-width: 1300px){
                #sidebar {
                    display: none;
                }
				.container{
					width:700px;
				}
                #accordion {
                    padding-left:0px;
                }
            }

        </style>
    </head>
    <body>
        <!-- Fixed navbar -->
<div class="navbar navbar-default navbar-fixed-top" role="navigation">
	<div class="container">
		<div class="navbar-collapse collapse">
			<form class="navbar-form navbar-right">
				<div class="form-group">
					域名 : <strong id="baseUrl">http://127.0.0.1:1082</strong>
				</div>
				<div class="form-group">
					<strong id="baseUrl"><a target="_blank" href="/errorcode.php">错误码地址</a></strong>
				</div>
				<div class="form-group">
					生成时间 : <strong>2024-01-08 13:02:40</strong>
				</div>
				<div class="form-group">
					接口总数:<strong id="api_length"></strong>
				</div>
			</form>
		</div><!--/.nav-collapse -->
	</div>
</div>

<div class="container"><div id="sidebar">
	<div class="list-group panel"><a href="#系统配置" class="list-group-item" data-toggle="collapse" data-parent="#sidebar">系统配置<i class="fa fa-caret-down"></i></a>				<div class="child collapse" id="系统配置">
					<a href="javascript:;" data-id="1" class="list-group-item">APP相关配置</a>
				</div>				<div class="child collapse" id="系统配置">
					<a href="javascript:;" data-id="2" class="list-group-item">获取充值列表</a>
				</div>				<div class="child collapse" id="系统配置">
					<a href="javascript:;" data-id="3" class="list-group-item">发送短信验证码</a>
				</div>				<div class="child collapse" id="系统配置">
					<a href="javascript:;" data-id="4" class="list-group-item">获取省市区配置</a>
				</div>				<div class="child collapse" id="系统配置">
					<a href="javascript:;" data-id="5" class="list-group-item">省市区换编号</a>
				</div>				<div class="child collapse" id="系统配置">
					<a href="javascript:;" data-id="6" class="list-group-item">获取小程序深度链接</a>
				</div>				<div class="child collapse" id="系统配置">
					<a href="javascript:;" data-id="7" class="list-group-item">图片上传接口</a>
				</div><a href="#商品" class="list-group-item" data-toggle="collapse" data-parent="#sidebar">商品<i class="fa fa-caret-down"></i></a>				<div class="child collapse" id="商品">
					<a href="javascript:;" data-id="13" class="list-group-item">获取商品类型</a>
				</div>				<div class="child collapse" id="商品">
					<a href="javascript:;" data-id="14" class="list-group-item">获取商品列表</a>
				</div>				<div class="child collapse" id="商品">
					<a href="javascript:;" data-id="15" class="list-group-item">获取商品详情</a>
				</div>				<div class="child collapse" id="商品">
					<a href="javascript:;" data-id="16" class="list-group-item">获取库存数量</a>
				</div>				<div class="child collapse" id="商品">
					<a href="javascript:;" data-id="17" class="list-group-item">添加购物车</a>
				</div>				<div class="child collapse" id="商品">
					<a href="javascript:;" data-id="18" class="list-group-item">删除购物车</a>
				</div>				<div class="child collapse" id="商品">
					<a href="javascript:;" data-id="19" class="list-group-item">获取购物车列表</a>
				</div>				<div class="child collapse" id="商品">
					<a href="javascript:;" data-id="20" class="list-group-item">商品下单</a>
				</div>				<div class="child collapse" id="商品">
					<a href="javascript:;" data-id="21" class="list-group-item">获取我的订单列表</a>
				</div>				<div class="child collapse" id="商品">
					<a href="javascript:;" data-id="22" class="list-group-item">添加评价</a>
				</div>				<div class="child collapse" id="商品">
					<a href="javascript:;" data-id="23" class="list-group-item">获取商品评价</a>
				</div>				<div class="child collapse" id="商品">
					<a href="javascript:;" data-id="24" class="list-group-item">获取我的评价</a>
				</div>				<div class="child collapse" id="商品">
					<a href="javascript:;" data-id="25" class="list-group-item">取消订单</a>
				</div>				<div class="child collapse" id="商品">
					<a href="javascript:;" data-id="26" class="list-group-item">确认收货</a>
				</div>				<div class="child collapse" id="商品">
					<a href="javascript:;" data-id="27" class="list-group-item">延迟收货</a>
				</div>				<div class="child collapse" id="商品">
					<a href="javascript:;" data-id="28" class="list-group-item">申请退款</a>
				</div><a href="#其他" class="list-group-item" data-toggle="collapse" data-parent="#sidebar">其他<i class="fa fa-caret-down"></i></a>				<div class="child collapse" id="其他">
					<a href="javascript:;" data-id="29" class="list-group-item">获取富文本</a>
				</div><a href="#活动" class="list-group-item" data-toggle="collapse" data-parent="#sidebar">活动<i class="fa fa-caret-down"></i></a>				<div class="child collapse" id="活动">
					<a href="javascript:;" data-id="30" class="list-group-item">活动分类</a>
				</div>				<div class="child collapse" id="活动">
					<a href="javascript:;" data-id="31" class="list-group-item">添加活动信息</a>
				</div>				<div class="child collapse" id="活动">
					<a href="javascript:;" data-id="32" class="list-group-item">修改活动信息</a>
				</div>				<div class="child collapse" id="活动">
					<a href="javascript:;" data-id="33" class="list-group-item">取消活动</a>
				</div>				<div class="child collapse" id="活动">
					<a href="javascript:;" data-id="34" class="list-group-item">获取活动列表</a>
				</div>				<div class="child collapse" id="活动">
					<a href="javascript:;" data-id="35" class="list-group-item">获取和我相关的活动列表</a>
				</div>				<div class="child collapse" id="活动">
					<a href="javascript:;" data-id="36" class="list-group-item">获取活动详情</a>
				</div>				<div class="child collapse" id="活动">
					<a href="javascript:;" data-id="37" class="list-group-item">报名下单</a>
				</div>				<div class="child collapse" id="活动">
					<a href="javascript:;" data-id="38" class="list-group-item">取消报名</a>
				</div>				<div class="child collapse" id="活动">
					<a href="javascript:;" data-id="39" class="list-group-item">获取活动报名列表</a>
				</div>				<div class="child collapse" id="活动">
					<a href="javascript:;" data-id="40" class="list-group-item">获取活动报名列表公开</a>
				</div>				<div class="child collapse" id="活动">
					<a href="javascript:;" data-id="41" class="list-group-item">添加收藏</a>
				</div>				<div class="child collapse" id="活动">
					<a href="javascript:;" data-id="42" class="list-group-item">删除收藏</a>
				</div>				<div class="child collapse" id="活动">
					<a href="javascript:;" data-id="43" class="list-group-item">点赞</a>
				</div>				<div class="child collapse" id="活动">
					<a href="javascript:;" data-id="44" class="list-group-item">取消点赞</a>
				</div>				<div class="child collapse" id="活动">
					<a href="javascript:;" data-id="45" class="list-group-item">添加评价</a>
				</div>				<div class="child collapse" id="活动">
					<a href="javascript:;" data-id="46" class="list-group-item">获取评价</a>
				</div><a href="#调试" class="list-group-item" data-toggle="collapse" data-parent="#sidebar">调试<i class="fa fa-caret-down"></i></a>				<div class="child collapse" id="调试">
					<a href="javascript:;" data-id="47" class="list-group-item">共用 - 参数输出</a>
				</div><a href="#轮播图" class="list-group-item" data-toggle="collapse" data-parent="#sidebar">轮播图<i class="fa fa-caret-down"></i></a>				<div class="child collapse" id="轮播图">
					<a href="javascript:;" data-id="48" class="list-group-item">获取轮播图</a>
				</div><a href="#支付" class="list-group-item" data-toggle="collapse" data-parent="#sidebar">支付<i class="fa fa-caret-down"></i></a>				<div class="child collapse" id="支付">
					<a href="javascript:;" data-id="49" class="list-group-item">余额支付</a>
				</div>				<div class="child collapse" id="支付">
					<a href="javascript:;" data-id="50" class="list-group-item">微信支付下单</a>
				</div>				<div class="child collapse" id="支付">
					<a href="javascript:;" data-id="51" class="list-group-item">获取微信支付调起支付签名V3</a>
				</div><a href="#用户管理" class="list-group-item" data-toggle="collapse" data-parent="#sidebar">用户管理<i class="fa fa-caret-down"></i></a>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="52" class="list-group-item">用户注册/登录</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="53" class="list-group-item">获取用户信息</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="54" class="list-group-item">更新手机号</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="55" class="list-group-item">修改资料</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="56" class="list-group-item">添加标签</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="57" class="list-group-item">删除标签</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="58" class="list-group-item">添加资料照片</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="59" class="list-group-item">删除资料照片</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="60" class="list-group-item">添加收货地址</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="61" class="list-group-item">删除收货地址</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="62" class="list-group-item">设置默认收货地址</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="63" class="list-group-item">获取收货地址列表</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="64" class="list-group-item">添加收款账号信息</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="65" class="list-group-item">删除收款账号信息</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="66" class="list-group-item">获取收款账号信息</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="67" class="list-group-item">设置默认收款地址</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="68" class="list-group-item">提现</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="69" class="list-group-item">获取提现记录</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="70" class="list-group-item">获取佣金</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="71" class="list-group-item">获取充值记录</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="72" class="list-group-item">获取分享记录</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="73" class="list-group-item">分享事件上传</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="74" class="list-group-item">获取对账单</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="75" class="list-group-item">充值下单</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="76" class="list-group-item">购买会员下单</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="77" class="list-group-item">添加关注</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="78" class="list-group-item">取消关注</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="79" class="list-group-item">检测关注</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="80" class="list-group-item">获取关注列表</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="81" class="list-group-item">获取社交用户列表</a>
				</div>				<div class="child collapse" id="用户管理">
					<a href="javascript:;" data-id="82" class="list-group-item">获取他人信息资料</a>
				</div>	</div>
</div>					<h2>系统配置</h2>
					<hr>
					<div class="panel panel-default">			<div class="panel-heading" id="headingheading-1">
				<h4 class="panel-title">
					<span class="label label-success">GET</span>
					<a data-toggle="collapse" data-parent="#accordion1" href="#collapseOne1"> APP相关配置 <span class="text-muted">/config/app</span></a>
				</h4>
			</div>
			<div id="collapseOne1" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab1"><li class="active"><a href="#info1" data-toggle="tab">基础信息</a></li><li><a href="#sample1" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info1">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>无</td><td></td><td></td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample1">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response1">{
    "status": "ok",
    "data": {
        "config": {
            "app_name": {
                "name": "app_name",
                "val": "\u6d3b\u52a8\u5b9d",
                "shuoming": "app\u540d\u79f0"
            },
            "min_chongzhi_money": {
                "name": "min_chongzhi_money",
                "val": "1",
                "shuoming": "\u6700\u4f4e\u5145\u503c\u91d1\u989d"
            },
            "min_tixian_money": {
                "name": "min_tixian_money",
                "val": "10",
                "shuoming": "\u6700\u4f4e\u63d0\u73b0\u91d1\u989d"
            },
            "tixian_fee": {
                "name": "tixian_fee",
                "val": "0",
                "shuoming": "\u63d0\u73b0\u624b\u7eed\u8d39\u767e\u5206\u6bd4\uff0c5\u53735%(\u6700\u591a\u4e24\u4f4d\u5c0f\u6570)"
            },
            "huiyuan_price": {
                "name": "huiyuan_price",
                "val": "500",
                "shuoming": "\u8d2d\u4e70\u4f1a\u5458\u4ef7\u683c"
            },
            "huiyuan_yongjin_money": {
                "name": "huiyuan_yongjin_money",
                "val": "100",
                "shuoming": "\u8d2d\u4e70\u4f1a\u5458\u4e00\u7ea7\u4f63\u91d1\uff08\u56fa\u5b9a\u91d1\u989d\uff09"
            },
            "huiyuan_yongjin_money_2": {
                "name": "huiyuan_yongjin_money_2",
                "val": "30",
                "shuoming": "\u8d2d\u4e70\u4f1a\u5458\u4e8c\u7ea7\u4f63\u91d1\uff08\u56fa\u5b9a\u91d1\u989d\uff09"
            },
            "goods_auto_queren_days": {
                "name": "goods_auto_queren_days",
                "val": "10",
                "shuoming": "\u5546\u54c1\u53d1\u8d27\u540e\u81ea\u52a8\u786e\u8ba4\u6536\u8d27\u65f6\u95f4\uff08\u5929\uff09"
            },
            "goods_yanchi_queren_days": {
                "name": "goods_yanchi_queren_days",
                "val": "10",
                "shuoming": "\u5546\u54c1\u5ef6\u8fdf\u6536\u8d27\u589e\u52a0\u591a\u957f\u65f6\u95f4\uff08\u5929\uff09"
            },
            "goods_nopay_cancel_minute": {
                "name": "goods_nopay_cancel_minute",
                "val": "30",
                "shuoming": "\u672a\u652f\u4ed8\u8ba2\u5355\u81ea\u52a8\u53d6\u6d88\u65f6\u95f4\uff08\u5206\u949f\uff09"
            },
            "huodong_choujiang_start_time": {
                "name": "huodong_choujiang_start_time",
                "val": "30",
                "shuoming": "\u6d3b\u52a8\u5f00\u59cb\u591a\u5c11\u5206\u949f\u540e\u5f00\u59cb\u62bd\u5956"
            },
            "huodong_choujiang_end_time": {
                "name": "huodong_choujiang_end_time",
                "val": "1",
                "shuoming": "\u6d3b\u52a8\u7ed3\u675f\u591a\u5c11\u5206\u949f\u540e\u7ed3\u675f\u62bd\u5956"
            },
            "huiyuan_days": {
                "name": "huiyuan_days",
                "val": "365",
                "shuoming": "\u8d2d\u4e70\u4f1a\u5458\u6709\u6548\u65f6\u957f\uff08\u5929\uff09"
            },
            "huodong_jiesuan_days": {
                "name": "huodong_jiesuan_days",
                "val": "3",
                "shuoming": "\u6d3b\u52a8\u7ed3\u675f\u540e\u51e0\u5929\u624d\u80fd\u7533\u8bf7\u7ed3\u7b97"
            }
        },
        "img_config": {
            "app_logo": {
                "mark": "app_logo",
                "name": "app\u56fe\u6807",
                "img_url": "http:\/\/192.168.0.66:1052\/test.jpg"
            }
        },
        "video_config": []
    }
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-2">
				<h4 class="panel-title">
					<span class="label label-success">GET</span>
					<a data-toggle="collapse" data-parent="#accordion2" href="#collapseOne2"> 获取充值列表 <span class="text-muted">/config/chongzhi_list</span></a>
				</h4>
			</div>
			<div id="collapseOne2" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab2"><li class="active"><a href="#info2" data-toggle="tab">基础信息</a></li><li><a href="#sample2" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info2">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>无</td><td></td><td></td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample2">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response2">{
    "status": "ok",
    "data": [
        {
            "id": 1,
            "money": 10,
            "daozhang_money": 10,
            "label": ""
        },
        {
            "id": 2,
            "money": 20,
            "daozhang_money": 20,
            "label": ""
        },
        {
            "id": 3,
            "money": 50,
            "daozhang_money": 50,
            "label": ""
        },
        {
            "id": 4,
            "money": 100,
            "daozhang_money": 100,
            "label": ""
        },
        {
            "id": 5,
            "money": 200,
            "daozhang_money": 200,
            "label": ""
        },
        {
            "id": 6,
            "money": 300,
            "daozhang_money": 300,
            "label": ""
        },
        {
            "id": 7,
            "money": 500,
            "daozhang_money": 500,
            "label": ""
        },
        {
            "id": 8,
            "money": 1000,
            "daozhang_money": 1000,
            "label": ""
        },
        {
            "id": 9,
            "money": 2000,
            "daozhang_money": 2000,
            "label": ""
        }
    ]
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-3">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion3" href="#collapseOne3"> 发送短信验证码 <span class="text-muted">/config/send_sms</span></a>
				</h4>
			</div>
			<div id="collapseOne3" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab3"><li class="active"><a href="#info3" data-toggle="tab">基础信息</a></li><li><a href="#sample3" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info3">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>mobile</td><td>string</td><td>手机号</td></tr><tr><td>type</td><td>string</td><td>类型:1=注册或登录,2=重置密码,3=修改资料</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample3">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response3">{
    "status": "ok"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-4">
				<h4 class="panel-title">
					<span class="label label-success">GET</span>
					<a data-toggle="collapse" data-parent="#accordion4" href="#collapseOne4"> 获取省市区配置 <span class="text-muted">/config/get_china</span></a>
				</h4>
			</div>
			<div id="collapseOne4" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab4"><li class="active"><a href="#info4" data-toggle="tab">基础信息</a></li><li><a href="#sample4" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info4">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>无</td><td></td><td></td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample4">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response4">{
    "status": "ok",
    "data": [
        {
            "id": 37,
            "name": "\u6cb3\u5317\u7701",
            "children": [
                {
                    "id": 38,
                    "name": "\u77f3\u5bb6\u5e84\u5e02",
                    "children": [
                        {
                            "id": 39,
                            "name": "\u957f\u5b89\u533a"
                        }
                    ]
                }
            ]
        }
    ]
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-5">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion5" href="#collapseOne5"> 省市区换编号 <span class="text-muted">/config/get_shengshiqu_id</span></a>
				</h4>
			</div>
			<div id="collapseOne5" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab5"><li class="active"><a href="#info5" data-toggle="tab">基础信息</a></li><li><a href="#sample5" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info5">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>name</td><td>string</td><td>省市区名称</td></tr><tr><td>type</td><td>string</td><td>类型:1=省,2=市,3=县区</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample5">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response5">{
    "status": "ok",
    "data": {
        "id": 1497,
        "name": "\u91d1\u6c34\u533a",
        "deep": 3,
        "pid": 1493,
        "shi_info": {
            "id": 1493,
            "name": "\u90d1\u5dde\u5e02",
            "deep": 2,
            "pid": 1492
        },
        "sheng_info": {
            "id": 1492,
            "name": "\u6cb3\u5357\u7701",
            "deep": 1,
            "pid": 0
        }
    }
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-6">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion6" href="#collapseOne6"> 获取小程序深度链接 <span class="text-muted">/config/get_deep_link</span></a>
				</h4>
			</div>
			<div id="collapseOne6" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab6"><li class="active"><a href="#info6" data-toggle="tab">基础信息</a></li><li><a href="#sample6" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info6">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>path</td><td>string</td><td>小程序path</td></tr><tr><td>query</td><td>string</td><td>小程序query</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample6">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response6">{
    "status": "ok",
    "data": "http:\/\/test.com\/test"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-7">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion7" href="#collapseOne7"> 图片上传接口 <span class="text-muted">/config/upload_img</span></a>
				</h4>
			</div>
			<div id="collapseOne7" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab7"><li class="active"><a href="#info7" data-toggle="tab">基础信息</a></li><li><a href="#sample7" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info7">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>img</td><td>string</td><td>图片二进制</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample7">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response7">{
    "status": "ok",
    "data": "http:\/\/test.com\/test.png"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>	</div>					<h2>商品</h2>
					<hr>
					<h2>商品</h2>
					<hr>
					<div class="panel panel-default">			<div class="panel-heading" id="headingheading-13">
				<h4 class="panel-title">
					<span class="label label-success">GET</span>
					<a data-toggle="collapse" data-parent="#accordion13" href="#collapseOne13"> 获取商品类型 <span class="text-muted">/goods/get_types</span></a>
				</h4>
			</div>
			<div id="collapseOne13" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab13"><li class="active"><a href="#info13" data-toggle="tab">基础信息</a></li><li><a href="#sample13" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info13">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>无</td><td></td><td></td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample13">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response13">{
    "status": "ok",
    "data": [
        {
            "id": 1,
            "name": "\u5206\u7c7b1"
        },
        {
            "id": 2,
            "name": "\u5206\u7c7b2"
        }
    ]
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-14">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion14" href="#collapseOne14"> 获取商品列表 <span class="text-muted">/goods/get_list</span></a>
				</h4>
			</div>
			<div id="collapseOne14" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab14"><li class="active"><a href="#info14" data-toggle="tab">基础信息</a></li><li><a href="#sample14" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info14">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>page</td><td>string</td><td>页码,默认1</td></tr><tr><td>page_size</td><td>string</td><td>每页多少条,默认20</td></tr><tr><td>is_shiyong</td><td>string</td><td>会员试用:1=是,0=否(可选)</td></tr><tr><td>type_id</td><td>string</td><td>商品类型编号(可选)</td></tr><tr><td>is_tuijian</td><td>string</td><td>是否推荐:1=是0=否(可选)</td></tr><tr><td>keyword</td><td>string</td><td>关键词(可选)</td></tr><tr><td>sort</td><td>string</td><td>排序:1=默认,2=销量(可选)</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample14">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response14">{
    "status": "ok",
    "data": [
        {
            "id": 1,
            "name": "\u5546\u54c11",
            "type_id": 1,
            "price": "10.00",
            "img_url": "http:\/\/127.0.0.1\/test.jpg",
            "guige": {
                "\u91cd\u91cf": [
                    "100g",
                    "200g",
                    "500g"
                ],
                "\u7b49\u7ea7": [
                    "\u4e00\u7b49",
                    "\u7279\u7b49",
                    "\u666e\u901a"
                ]
            },
            "is_tuijian": 0,
            "sell_num": 0,
            "yongjin_bili": 5,
            "is_shiyong": 0
        }
    ],
    "count": 1
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-15">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion15" href="#collapseOne15"> 获取商品详情 <span class="text-muted">/goods/get_goods_info</span></a>
				</h4>
			</div>
			<div id="collapseOne15" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab15"><li class="active"><a href="#info15" data-toggle="tab">基础信息</a></li><li><a href="#sample15" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info15">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>goods_id</td><td>string</td><td>商品编号</td></tr><tr><td>guige</td><td>string</td><td>规格json:{"颜色":"红","尺码":"L"}</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample15">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response15">{
    "status": "ok",
    "data": {
        "id": 1,
        "name": "\u5546\u54c11",
        "type_id": 1,
        "price": "10.00",
        "img_url": "http:\/\/127.0.0.1\/test.jpg",
        "guige": {
            "\u91cd\u91cf": [
                "100g",
                "200g",
                "500g"
            ],
            "\u7b49\u7ea7": [
                "\u4e00\u7b49",
                "\u7279\u7b49",
                "\u666e\u901a"
            ]
        },
        "jieshao": "\u5546\u54c1\u4ecb\u7ecd",
        "sell_num": 0,
        "yongjin_bili": 5,
        "guige_info": {
            "id": 1,
            "goods_id": 1,
            "name": "100g-\u7ea2",
            "guige": {
                "\u91cd\u91cf": "100g",
                "\u7b49\u7ea7": "\u4e00\u7b49"
            },
            "canshu": [],
            "price": "10.00",
            "kucun": 100,
            "is_shiyong": 0,
            "imgs": [
                {
                    "id": 1,
                    "goods_id": 1,
                    "guige_id": 0,
                    "img_url": "http:\/\/127.0.0.1\/test.jpg",
                    "img_shuoming": "ddd"
                },
                {
                    "id": 2,
                    "goods_id": 1,
                    "guige_id": 0,
                    "img_url": "http:\/\/127.0.0.1\/test.jpg",
                    "img_shuoming": "ddd"
                }
            ]
        }
    }
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-16">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion16" href="#collapseOne16"> 获取库存数量 <span class="text-muted">/goods/get_goods_kucun</span></a>
				</h4>
			</div>
			<div id="collapseOne16" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab16"><li class="active"><a href="#info16" data-toggle="tab">基础信息</a></li><li><a href="#sample16" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info16">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>goods_id</td><td>string</td><td>商品编号</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample16">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response16">{
    "status": "ok",
    "data": [
        {
            "id": 1,
            "name": "100g-\u4e00\u7b49",
            "guige": {
                "\u91cd\u91cf": "100g",
                "\u7b49\u7ea7": "\u4e00\u7b49"
            },
            "kucun": 100,
            "is_shiyong": 0
        },
        {
            "id": 2,
            "name": "200g-\u4e00\u7b49",
            "guige": {
                "\u91cd\u91cf": "200g",
                "\u7b49\u7ea7": "\u4e00\u7b49"
            },
            "kucun": 100,
            "is_shiyong": 0
        }
    ]
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-17">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion17" href="#collapseOne17"> 添加购物车 <span class="text-muted">/goods/add_car</span></a>
				</h4>
			</div>
			<div id="collapseOne17" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab17"><li class="active"><a href="#info17" data-toggle="tab">基础信息</a></li><li><a href="#sample17" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info17">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>goods_id</td><td>string</td><td>商品编号</td></tr><tr><td>guige_id</td><td>string</td><td>规格编号</td></tr><tr><td>num</td><td>string</td><td>数量</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample17">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response17">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-18">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion18" href="#collapseOne18"> 删除购物车 <span class="text-muted">/goods/del_car</span></a>
				</h4>
			</div>
			<div id="collapseOne18" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab18"><li class="active"><a href="#info18" data-toggle="tab">基础信息</a></li><li><a href="#sample18" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info18">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>ids</td><td>string</td><td>购物车编号多个英文逗号隔开</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample18">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response18">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-19">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion19" href="#collapseOne19"> 获取购物车列表 <span class="text-muted">/goods/get_car_list</span></a>
				</h4>
			</div>
			<div id="collapseOne19" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab19"><li class="active"><a href="#info19" data-toggle="tab">基础信息</a></li><li><a href="#sample19" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info19">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample19">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response19">{
    "status": "ok",
    "data": [
        {
            "id": 3,
            "goods_id": 1,
            "guige_id": 2,
            "num": 2,
            "price": "10.00",
            "zong_price": "20.00",
            "time": "2023-11-17 17:47:10",
            "goods_info": {
                "id": 1,
                "name": "\u5546\u54c11",
                "img_url": "http:\/\/127.0.0.1\/test.jpg"
            },
            "guige_info": {
                "id": 2,
                "name": "200g-\u4e00\u7b49",
                "guige": {
                    "\u91cd\u91cf": "200g",
                    "\u7b49\u7ea7": "\u4e00\u7b49"
                },
                "price": "10.00",
                "kucun": 100,
                "is_shiyong": 0,
                "is_shangjia": 1
            }
        },
        {
            "id": 1,
            "goods_id": 1,
            "guige_id": 1,
            "num": 2,
            "price": "10.00",
            "zong_price": "20.00",
            "time": "2023-11-17 17:47:03",
            "goods_info": {
                "id": 1,
                "name": "\u5546\u54c11",
                "img_url": "http:\/\/127.0.0.1\/test.jpg"
            },
            "guige_info": {
                "id": 1,
                "name": "100g-\u4e00\u7b49",
                "guige": {
                    "\u91cd\u91cf": "100g",
                    "\u7b49\u7ea7": "\u4e00\u7b49"
                },
                "price": "10.00",
                "kucun": 100,
                "is_shiyong": 0,
                "is_shangjia": 1
            }
        }
    ]
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-20">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion20" href="#collapseOne20"> 商品下单 <span class="text-muted">/goods/add_order</span></a>
				</h4>
			</div>
			<div id="collapseOne20" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab20"><li class="active"><a href="#info20" data-toggle="tab">基础信息</a></li><li><a href="#sample20" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info20">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>goods_info</td><td>string</td><td>商品信息json[{"goods_id":1,"guige_id":1,"num":2,"money":4},{"goods_id":5,"guige_id":2,"num":1,"money":2}]</td></tr><tr><td>addr_id</td><td>string</td><td>收货地址编号</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample20">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response20">{
    "status": "ok",
    "msg": "\u4e0b\u5355\u6210\u529f",
    "order_id": "20231117175510385129",
    "pay_money": "30.00"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-21">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion21" href="#collapseOne21"> 获取我的订单列表 <span class="text-muted">/goods/get_order_list</span></a>
				</h4>
			</div>
			<div id="collapseOne21" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab21"><li class="active"><a href="#info21" data-toggle="tab">基础信息</a></li><li><a href="#sample21" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info21">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>page</td><td>string</td><td>页码,默认1</td></tr><tr><td>page_size</td><td>string</td><td>每页多少条,默认20</td></tr><tr><td>status</td><td>string</td><td>状态,多个逗号隔开(可选)：0=未支付,1=已支付,2=已发货,3=交易完成,4=取消订单,5=退款中,6=退款成功,7=退款失败</td></tr><tr><td>is_pingjia</td><td>string</td><td>是否评价:1=是,0=否(可选)</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample21">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response21">{
    "status": "ok",
    "data": [
        {
            "id": 6,
            "order_id": "20231117175733019328",
            "create_time": "2023-11-17 17:57:33",
            "cancel_time": null,
            "goods_info": [
                {
                    "goods_id": 1,
                    "guige_id": 1,
                    "num": 2,
                    "money": 20,
                    "guige_info": {
                        "\u91cd\u91cf": "100g",
                        "\u7b49\u7ea7": "\u4e00\u7b49"
                    },
                    "goods_name": "\u5546\u54c11",
                    "img_url": "http:\/\/127.0.0.1\/test.jpg"
                },
                {
                    "goods_id": 1,
                    "guige_id": 2,
                    "num": 1,
                    "money": 10,
                    "guige_info": {
                        "\u91cd\u91cf": "200g",
                        "\u7b49\u7ea7": "\u4e00\u7b49"
                    },
                    "goods_name": "\u5546\u54c11",
                    "img_url": "http:\/\/127.0.0.1\/test.jpg"
                }
            ],
            "goods_info_title": "\u5546\u54c11,\u5546\u54c11",
            "order_money": "30.00",
            "money": "30.00",
            "addr_id": 1,
            "status": 0,
            "pay_time": null,
            "fahuo_time": null,
            "wuliu_id": null,
            "wuliu_danhao": null,
            "queren_time": null,
            "tuikuan_money": "0.00",
            "tuikuan_time": null,
            "is_pingjia": 0,
            "auto_cancel_time": "2023-11-17 18:27:33",
            "auto_queren_time": null,
            "is_shenqing_tuikuan": 0,
            "shenqing_tuikuan_msg": "",
            "wuliu_info": [],
            "addr_info": {
                "sheng": "\u5b89\u5fbd\u7701",
                "shi": "\u94dc\u9675\u5e02",
                "qu": "\u94dc\u5b98\u533a",
                "addr": "\u67d0\u67d0\u8857\u9053\u67d0\u5c0f\u533a",
                "mobile": "***********",
                "username": "\u5f20\u4e09"
            }
        },
        {
            "id": 5,
            "order_id": "20231117175730337122",
            "create_time": "2023-11-17 17:57:30",
            "cancel_time": null,
            "goods_info": [
                {
                    "goods_id": 1,
                    "guige_id": 1,
                    "num": 2,
                    "money": 20,
                    "guige_info": {
                        "\u91cd\u91cf": "100g",
                        "\u7b49\u7ea7": "\u4e00\u7b49"
                    },
                    "goods_name": "\u5546\u54c11",
                    "img_url": "http:\/\/127.0.0.1\/test.jpg"
                },
                {
                    "goods_id": 1,
                    "guige_id": 2,
                    "num": 1,
                    "money": 10,
                    "guige_info": {
                        "\u91cd\u91cf": "200g",
                        "\u7b49\u7ea7": "\u4e00\u7b49"
                    },
                    "goods_name": "\u5546\u54c11",
                    "img_url": "http:\/\/127.0.0.1\/test.jpg"
                }
            ],
            "goods_info_title": "\u5546\u54c11,\u5546\u54c11",
            "order_money": "30.00",
            "money": "30.00",
            "addr_id": 1,
            "status": 0,
            "pay_time": null,
            "fahuo_time": null,
            "wuliu_id": null,
            "wuliu_danhao": null,
            "queren_time": null,
            "tuikuan_money": "0.00",
            "tuikuan_time": null,
            "is_pingjia": 0,
            "auto_cancel_time": "2023-11-17 18:27:30",
            "auto_queren_time": null,
            "is_shenqing_tuikuan": 0,
            "shenqing_tuikuan_msg": "",
            "wuliu_info": [],
            "addr_info": {
                "sheng": "\u5b89\u5fbd\u7701",
                "shi": "\u94dc\u9675\u5e02",
                "qu": "\u94dc\u5b98\u533a",
                "addr": "\u67d0\u67d0\u8857\u9053\u67d0\u5c0f\u533a",
                "mobile": "***********",
                "username": "\u5f20\u4e09"
            }
        }
    ],
    "count": 4
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-22">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion22" href="#collapseOne22"> 添加评价 <span class="text-muted">/goods/add_pingjia</span></a>
				</h4>
			</div>
			<div id="collapseOne22" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab22"><li class="active"><a href="#info22" data-toggle="tab">基础信息</a></li><li><a href="#sample22" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info22">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>order_id</td><td>string</td><td>订单编号</td></tr><tr><td>pingjia_data</td><td>string</td><td>评价内容json:[{goods_id:1,guige_id:1,star_num:3,contents:ddd,imgs_url:[u1,u2]}]</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample22">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response22">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-23">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion23" href="#collapseOne23"> 获取商品评价 <span class="text-muted">/goods/get_goods_pingjia</span></a>
				</h4>
			</div>
			<div id="collapseOne23" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab23"><li class="active"><a href="#info23" data-toggle="tab">基础信息</a></li><li><a href="#sample23" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info23">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>page</td><td>string</td><td>页码,默认1</td></tr><tr><td>page_size</td><td>string</td><td>每页多少条,默认20</td></tr><tr><td>goods_id</td><td>string</td><td>商品编号</td></tr><tr><td>guige_id</td><td>string</td><td>规格编号(可选)</td></tr><tr><td>star_nums</td><td>string</td><td>星级，多个逗号隔开(可选)</td></tr><tr><td>has_img</td><td>string</td><td>是否包含图片:1=是,0=否(可选)</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample23">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response23">{
    "status": "ok",
    "data": [
        {
            "id": 20,
            "uid": 2,
            "goods_id": 1,
            "guige_id": 2,
            "imgs_url": [
                "http:\/\/127.0.0.1\/test.jpg",
                "http:\/\/127.0.0.1\/test.jpg"
            ],
            "contents": "\u8bc4\u4ef7",
            "star_num": 3,
            "time": "2023-11-17 18:15:20",
            "user": {
                "nickname": "test",
                "avatar": "http:\/\/127.0.0.1\/test.jpg",
                "is_huiyuan": 0
            }
        }
    ],
    "count": 1
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-24">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion24" href="#collapseOne24"> 获取我的评价 <span class="text-muted">/goods/get_my_goods_pingjia</span></a>
				</h4>
			</div>
			<div id="collapseOne24" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab24"><li class="active"><a href="#info24" data-toggle="tab">基础信息</a></li><li><a href="#sample24" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info24">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>page</td><td>string</td><td>页码,默认1</td></tr><tr><td>page_size</td><td>string</td><td>每页多少条,默认20</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample24">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response24">{
    "status": "ok",
    "data": [
        {
            "id": 20,
            "uid": 2,
            "goods_id": 1,
            "guige_id": 2,
            "order_id": "20231117175653517677",
            "imgs_url": [
                "http:\/\/127.0.0.1\/test.jpg",
                "http:\/\/127.0.0.1\/test.jpg"
            ],
            "contents": "\u8bc4\u4ef7",
            "star_num": 3,
            "time": "2023-11-17 18:15:20",
            "guige_info": {
                "\u91cd\u91cf": "200g",
                "\u7b49\u7ea7": "\u4e00\u7b49"
            },
            "goods_name": "\u5546\u54c11"
        },
        {
            "id": 19,
            "uid": 2,
            "goods_id": 1,
            "guige_id": 1,
            "order_id": "20231117175653517677",
            "imgs_url": [
                "http:\/\/127.0.0.1\/test.jpg",
                "http:\/\/127.0.0.1\/test.jpg"
            ],
            "contents": "\u8bc4\u4ef7",
            "star_num": 3,
            "time": "2023-11-17 18:15:20",
            "guige_info": {
                "\u91cd\u91cf": "100g",
                "\u7b49\u7ea7": "\u4e00\u7b49"
            },
            "goods_name": "\u5546\u54c11"
        }
    ],
    "count": 4
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-25">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion25" href="#collapseOne25"> 取消订单 <span class="text-muted">/goods/cancel_order</span></a>
				</h4>
			</div>
			<div id="collapseOne25" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab25"><li class="active"><a href="#info25" data-toggle="tab">基础信息</a></li><li><a href="#sample25" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info25">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>order_id</td><td>string</td><td>订单编号</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample25">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response25">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-26">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion26" href="#collapseOne26"> 确认收货 <span class="text-muted">/goods/queren_shouhuo</span></a>
				</h4>
			</div>
			<div id="collapseOne26" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab26"><li class="active"><a href="#info26" data-toggle="tab">基础信息</a></li><li><a href="#sample26" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info26">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>order_id</td><td>string</td><td>订单编号</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample26">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response26">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-27">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion27" href="#collapseOne27"> 延迟收货 <span class="text-muted">/goods/yanchi_shouhuo</span></a>
				</h4>
			</div>
			<div id="collapseOne27" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab27"><li class="active"><a href="#info27" data-toggle="tab">基础信息</a></li><li><a href="#sample27" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info27">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>order_id</td><td>string</td><td>订单编号</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample27">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response27">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-28">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion28" href="#collapseOne28"> 申请退款 <span class="text-muted">/goods/shenqing_tuikuan</span></a>
				</h4>
			</div>
			<div id="collapseOne28" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab28"><li class="active"><a href="#info28" data-toggle="tab">基础信息</a></li><li><a href="#sample28" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info28">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>order_id</td><td>string</td><td>订单编号</td></tr><tr><td>msg</td><td>string</td><td>退款原因</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample28">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response28">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>	</div>					<h2>其他</h2>
					<hr>
					<div class="panel panel-default">			<div class="panel-heading" id="headingheading-29">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion29" href="#collapseOne29"> 获取富文本 <span class="text-muted">/html/index</span></a>
				</h4>
			</div>
			<div id="collapseOne29" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab29"><li class="active"><a href="#info29" data-toggle="tab">基础信息</a></li><li><a href="#sample29" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info29">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>type</td><td>string</td><td>类型：1=用户注册协议,2=报名须知,3=用户规则,4=隐私协议</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample29">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response29">{
    "status": "ok",
    "data": {
        "name": "\u7528\u6237\u6ce8\u518c\u534f\u8bae",
        "contents": "\u6b64\u5904\u662f\u5bcc\u6587\u672c",
        "time": "2023-11-07 18:08:09"
    }
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>	</div>					<h2>活动</h2>
					<hr>
					<div class="panel panel-default">			<div class="panel-heading" id="headingheading-30">
				<h4 class="panel-title">
					<span class="label label-success">GET</span>
					<a data-toggle="collapse" data-parent="#accordion30" href="#collapseOne30"> 活动分类 <span class="text-muted">/huodong/get_type</span></a>
				</h4>
			</div>
			<div id="collapseOne30" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab30"><li class="active"><a href="#info30" data-toggle="tab">基础信息</a></li><li><a href="#sample30" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info30">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample30">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response30">{
    "status": "ok",
    "data": [
        {
            "id": 1,
            "name": "\u5206\u7c7b1",
            "icon": "http:\/\/192.168.0.66\/test.jpg"
        },
        {
            "id": 2,
            "name": "\u5206\u7c7b2",
            "icon": "http:\/\/192.168.0.66\/test.jpg"
        },
        {
            "id": 3,
            "name": "\u5206\u7c7b3",
            "icon": "http:\/\/192.168.0.66\/test.jpg"
        }
    ]
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-31">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion31" href="#collapseOne31"> 添加活动信息 <span class="text-muted">/huodong/add_huodong</span></a>
				</h4>
			</div>
			<div id="collapseOne31" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab31"><li class="active"><a href="#info31" data-toggle="tab">基础信息</a></li><li><a href="#sample31" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info31">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>name</td><td>string</td><td>名称</td></tr><tr><td>title</td><td>string</td><td>标题</td></tr><tr><td>type_id</td><td>string</td><td>分类编号</td></tr><tr><td>img_url</td><td>string</td><td>列表图</td></tr><tr><td>money</td><td>string</td><td>费用</td></tr><tr><td>pay_type</td><td>string</td><td>收费方式:1=线上,2=线下</td></tr><tr><td>contents</td><td>string</td><td>介绍</td></tr><tr><td>liucheng</td><td>json</td><td>流程json['111','222']</td></tr><tr><td>guige</td><td>string</td><td>规格</td></tr><tr><td>num</td><td>string</td><td>人数</td></tr><tr><td>baoming_start_time</td><td>string</td><td>报名开始时间</td></tr><tr><td>baoming_end_time</td><td>string</td><td>报名结束时间</td></tr><tr><td>start_time</td><td>string</td><td>活动开始时间</td></tr><tr><td>end_time</td><td>string</td><td>活动结束时间</td></tr><tr><td>sheng_id</td><td>string</td><td>省编号</td></tr><tr><td>shi_id</td><td>string</td><td>市编号</td></tr><tr><td>qu_id</td><td>string</td><td>县区编号</td></tr><tr><td>addr</td><td>string</td><td>具体位置</td></tr><tr><td>lng</td><td>string</td><td>地图坐标经度</td></tr><tr><td>lat</td><td>string</td><td>地图坐标纬度</td></tr><tr><td>yongjin_bili</td><td>string</td><td>邀请佣金比例，5即5%</td></tr><tr><td>imgs_url</td><td>string</td><td>图片集逗号隔开</td></tr><tr><td>is_choujiang</td><td>string</td><td>是否抽奖:1=是,0=否</td></tr><tr><td>choujiang_huiyuan_num</td><td>string</td><td>抽奖会员需达到数量</td></tr><tr><td>choujiang_zhongjiang_num</td><td>string</td><td>抽奖中奖人数</td></tr><tr><td>choujiang_zhekou</td><td>string</td><td>中奖折扣:0-99,0=免单,5=0.5折,60=6折</td></tr><tr><td>lianxi_name</td><td>string</td><td>联系人姓名</td></tr><tr><td>lianxi_mobile</td><td>string</td><td>联系人电话</td></tr><tr><td>lianxi_qrcode</td><td>string</td><td>联系客服微信二维码地址</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample31">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response31">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-32">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion32" href="#collapseOne32"> 修改活动信息 <span class="text-muted">/huodong/update_huodong</span></a>
				</h4>
			</div>
			<div id="collapseOne32" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab32"><li class="active"><a href="#info32" data-toggle="tab">基础信息</a></li><li><a href="#sample32" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info32">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>huodong_id</td><td>string</td><td>活动编号</td></tr><tr><td>name</td><td>string</td><td>名称</td></tr><tr><td>title</td><td>string</td><td>标题</td></tr><tr><td>type_id</td><td>string</td><td>分类编号</td></tr><tr><td>img_url</td><td>string</td><td>列表图</td></tr><tr><td>money</td><td>string</td><td>费用</td></tr><tr><td>pay_type</td><td>string</td><td>收费方式:1=线上,2=线下</td></tr><tr><td>contents</td><td>string</td><td>介绍</td></tr><tr><td>liucheng</td><td>json</td><td>流程json</td></tr><tr><td>guige</td><td>string</td><td>规格</td></tr><tr><td>num</td><td>string</td><td>人数</td></tr><tr><td>baoming_start_time</td><td>string</td><td>报名开始时间</td></tr><tr><td>baoming_end_time</td><td>string</td><td>报名结束时间</td></tr><tr><td>start_time</td><td>string</td><td>活动开始时间</td></tr><tr><td>end_time</td><td>string</td><td>活动结束时间</td></tr><tr><td>sheng_id</td><td>string</td><td>省编号</td></tr><tr><td>shi_id</td><td>string</td><td>市编号</td></tr><tr><td>qu_id</td><td>string</td><td>县区编号</td></tr><tr><td>addr</td><td>string</td><td>具体位置</td></tr><tr><td>lng</td><td>string</td><td>地图坐标经度</td></tr><tr><td>lat</td><td>string</td><td>地图坐标纬度</td></tr><tr><td>yongjin_bili</td><td>string</td><td>邀请佣金比例，5即5%</td></tr><tr><td>imgs_url</td><td>string</td><td>图片集逗号隔开</td></tr><tr><td>is_choujiang</td><td>string</td><td>是否抽奖:1=是,0=否</td></tr><tr><td>choujiang_huiyuan_num</td><td>string</td><td>抽奖会员需达到数量</td></tr><tr><td>choujiang_zhongjiang_num</td><td>string</td><td>抽奖中奖人数</td></tr><tr><td>choujiang_zhekou</td><td>string</td><td>中奖折扣:0-99,0=免单,5=0.5折,60=6折</td></tr><tr><td>lianxi_name</td><td>string</td><td>联系人姓名</td></tr><tr><td>lianxi_mobile</td><td>string</td><td>联系人电话</td></tr><tr><td>lianxi_qrcode</td><td>string</td><td>联系客服微信二维码地址</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample32">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response32">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-33">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion33" href="#collapseOne33"> 取消活动 <span class="text-muted">/huodong/cancel_huodong</span></a>
				</h4>
			</div>
			<div id="collapseOne33" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab33"><li class="active"><a href="#info33" data-toggle="tab">基础信息</a></li><li><a href="#sample33" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info33">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>huodong_id</td><td>string</td><td>活动编号</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample33">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response33">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-34">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion34" href="#collapseOne34"> 获取活动列表 <span class="text-muted">/huodong/get_list</span></a>
				</h4>
			</div>
			<div id="collapseOne34" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab34"><li class="active"><a href="#info34" data-toggle="tab">基础信息</a></li><li><a href="#sample34" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info34">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>type_id</td><td>string</td><td>分类(0=全部)</td></tr><tr><td>page</td><td>string</td><td>页码,默认1</td></tr><tr><td>page_size</td><td>string</td><td>每页多少条,默认20</td></tr><tr><td>is_tuijian</td><td>string</td><td>是否推荐:1=是,0=否(可选)</td></tr><tr><td>keyword</td><td>string</td><td>关键词(可选)</td></tr><tr><td>shi_id</td><td>string</td><td>市级编号</td></tr><tr><td>qu_id</td><td>string</td><td>区级编号(可选)</td></tr><tr><td>sort</td><td>string</td><td>排序:1=默认,2=距离,3=人气(可选)</td></tr><tr><td>lng</td><td>string</td><td>用户经度坐标(可选)</td></tr><tr><td>lat</td><td>string</td><td>用户纬度坐标(可选)</td></tr><tr><td>baoming_status</td><td>string</td><td>报名状态:1=未开始,2=报名中,3=报名已结束(可选)</td></tr><tr><td>huodong_status</td><td>string</td><td>活动状态:1=未开始,2=进行中,3=已结束(可选)</td></tr><tr><td>baoming_date</td><td>string</td><td>可报名日期(可选)</td></tr><tr><td>huodong_date</td><td>string</td><td>活动日期(可选)</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample34">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response34">{
    "status": "ok",
    "data": [
        {
            "id": 2,
            "uid": 2,
            "name": "\u6d3b\u52a83",
            "title": "\u6d3b\u52a8\u6807\u9898",
            "img_url": "http:\/\/192.168.0.66\/test.jpg",
            "money": "100.00",
            "guige": "\u89c4\u683c1111122",
            "num": 80,
            "baoming_start_time": "2023-11-18 00:00:00",
            "baoming_end_time": "2023-11-18 11:30:00",
            "baoming_num": 0,
            "date": "2023-11-18",
            "start_time": "12:00:00",
            "end_time": "14:00:00",
            "sheng": "\u5b89\u5fbd\u7701",
            "shi": "\u94dc\u9675\u5e02",
            "qu": "\u94dc\u5b98\u533a",
            "addr": "\u67d0\u67d0\u8857\u9053\u67d0\u516c\u56ed11",
            "yongjin_bili": 10,
            "distance": "0\u7c73",
            "user": {
                "uid": 2,
                "nickname": "test",
                "avatar": "http:\/\/127.0.0.1\/test.jpg",
                "sex": 2,
                "is_huiyuan": 0,
                "mobile": "15800000000"
            }
        }
    ],
    "count": 1
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-35">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion35" href="#collapseOne35"> 获取和我相关的活动列表 <span class="text-muted">/huodong/get_my_list</span></a>
				</h4>
			</div>
			<div id="collapseOne35" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab35"><li class="active"><a href="#info35" data-toggle="tab">基础信息</a></li><li><a href="#sample35" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info35">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>type</td><td>string</td><td>类型:1=发布的,2=报名的,3=收藏的,4=评价的,5=分享的</td></tr><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>page</td><td>string</td><td>页码,默认1</td></tr><tr><td>page_size</td><td>string</td><td>每页多少条,默认20</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample35">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response35">{
    "status": "ok",
    "data": [
        {
            "id": 2,
            "huodong_id": 2,
            "contents": "\u8bc4\u4ef7\u5185\u5bb9",
            "imgs_url": [
                "http:\/\/192.168.0.66\/test.jpg",
                "http:\/\/192.168.0.66\/test.jpg",
                "http:\/\/192.168.0.66\/test.jpg"
            ],
            "time": "2023-11-18 13:21:00",
            "huodong_info": {
                "id": 2,
                "uid": 2,
                "name": "\u6d3b\u52a83",
                "title": "\u6d3b\u52a8\u6807\u9898",
                "img_url": "http:\/\/192.168.0.66\/test.jpg",
                "guige": "\u89c4\u683c1111122",
                "num": 80,
                "baoming_start_time": "2023-11-18 00:00:00",
                "baoming_end_time": "2023-11-19 11:30:00",
                "date": "2023-11-19",
                "start_time": "12:00:00",
                "end_time": "14:00:00",
                "sheng": "\u5b89\u5fbd\u7701",
                "shi": "\u94dc\u9675\u5e02",
                "qu": "\u94dc\u5b98\u533a",
                "addr": "\u67d0\u67d0\u8857\u9053\u67d0\u516c\u56ed11",
                "yongjin_bili": 10,
                "status": 1,
                "user": {
                    "uid": 2,
                    "avatar": "http:\/\/127.0.0.1\/test.jpg",
                    "nickname": "test",
                    "mobile": "15800000000"
                }
            },
            "user": {
                "uid": 2,
                "nickname": "test",
                "avatar": "http:\/\/127.0.0.1\/test.jpg",
                "sex": 2,
                "is_huiyuan": 0,
                "mobile": "15800000000"
            }
        }
    ]
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-36">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion36" href="#collapseOne36"> 获取活动详情 <span class="text-muted">/huodong/get_info</span></a>
				</h4>
			</div>
			<div id="collapseOne36" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab36"><li class="active"><a href="#info36" data-toggle="tab">基础信息</a></li><li><a href="#sample36" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info36">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>huodong_id</td><td>string</td><td>活动编号</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample36">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response36">{
    "status": "ok",
    "data": {
        "id": 3,
        "uid": 2,
        "name": "\u6d3b\u52a82",
        "title": "\u6d3b\u52a8\u6807\u9898",
        "img_url": "http:\/\/192.168.0.66\/test.jpg",
        "money": "100.00",
        "contents": "\u6d3b\u52a8\u4ecb\u7ecd",
        "liucheng": [
            "\u6d41\u7a0b1",
            "\u6d41\u7a0b2"
        ],
        "guige": "\u89c4\u683c1111",
        "num": 80,
        "baoming_start_time": "2023-11-18 00:00:00",
        "baoming_end_time": "2023-11-18 11:00:00",
        "baoming_num": 0,
        "date": "2023-11-18",
        "start_time": "12:00:00",
        "end_time": "14:00:00",
        "sheng": "\u5b89\u5fbd\u7701",
        "shi": "\u94dc\u9675\u5e02",
        "qu": "\u94dc\u5b98\u533a",
        "addr": "\u67d0\u67d0\u8857\u9053\u67d0\u516c\u56ed",
        "yongjin_bili": 5,
        "time": "2023-11-18 09:49:52",
        "pingjia_times": 0,
        "user": {
            "uid": 2,
            "nickname": "test",
            "avatar": "http:\/\/127.0.0.1\/test.jpg",
            "sex": 2,
            "is_huiyuan": 0,
            "mobile": "15800000000"
        },
        "imgs": [
            "http:\/\/192.168.0.66\/test.jpg",
            "http:\/\/192.168.0.66\/test.jpg",
            "http:\/\/192.168.0.66\/test.jpg"
        ],
        "is_shoucang": 0,
        "is_zan": 0
    }
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-37">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion37" href="#collapseOne37"> 报名下单 <span class="text-muted">/huodong/add_baoming</span></a>
				</h4>
			</div>
			<div id="collapseOne37" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab37"><li class="active"><a href="#info37" data-toggle="tab">基础信息</a></li><li><a href="#sample37" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info37">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>huodong_id</td><td>string</td><td>活动编号</td></tr><tr><td>is_choujiang</td><td>string</td><td>是否参与抽奖:1=是,0=否</td></tr><tr><td>lianxi_name</td><td>string</td><td>联系人姓名</td></tr><tr><td>lianxi_mobile</td><td>string</td><td>联系人手机号</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample37">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response37">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f",
    "order_id": "20231118113306655667",
    "money": "100.00"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-38">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion38" href="#collapseOne38"> 取消报名 <span class="text-muted">/huodong/cancel_baoming</span></a>
				</h4>
			</div>
			<div id="collapseOne38" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab38"><li class="active"><a href="#info38" data-toggle="tab">基础信息</a></li><li><a href="#sample38" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info38">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>order_id</td><td>string</td><td>报名订单编号</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample38">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response38">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-39">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion39" href="#collapseOne39"> 获取活动报名列表 <span class="text-muted">/huodong/get_baoming_list</span></a>
				</h4>
			</div>
			<div id="collapseOne39" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab39"><li class="active"><a href="#info39" data-toggle="tab">基础信息</a></li><li><a href="#sample39" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info39">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>huodong_id</td><td>string</td><td>活动编号</td></tr><tr><td>page</td><td>string</td><td>页码,默认1</td></tr><tr><td>page_size</td><td>string</td><td>每页多少条,默认20</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample39">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response39">{
    "status": "ok",
    "data": [
        {
            "id": 4,
            "order_id": "20231118130603127893",
            "uid": 2,
            "money": "100.00",
            "yongjin_money": "10.00",
            "time": "2023-11-18 13:06:03",
            "pay_time": null,
            "status": 1,
            "is_jiesuan": 0,
            "user": {
                "uid": 2,
                "avatar": "http:\/\/127.0.0.1\/test.jpg",
                "nickname": "test",
                "mobile": "15800000000"
            }
        },
        {
            "id": 3,
            "order_id": "20231118130602506823",
            "uid": 2,
            "money": "100.00",
            "yongjin_money": "10.00",
            "time": "2023-11-18 13:06:02",
            "pay_time": null,
            "status": 1,
            "is_jiesuan": 0,
            "user": {
                "uid": 2,
                "avatar": "http:\/\/127.0.0.1\/test.jpg",
                "nickname": "test",
                "mobile": "15800000000"
            }
        }
    ]
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-40">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion40" href="#collapseOne40"> 获取活动报名列表公开 <span class="text-muted">/huodong/get_baoming_list_public</span></a>
				</h4>
			</div>
			<div id="collapseOne40" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab40"><li class="active"><a href="#info40" data-toggle="tab">基础信息</a></li><li><a href="#sample40" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info40">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>huodong_id</td><td>string</td><td>活动编号</td></tr><tr><td>page</td><td>string</td><td>页码,默认1</td></tr><tr><td>page_size</td><td>string</td><td>每页多少条,默认20</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample40">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response40">{
    "status": "ok",
    "data": [
        {
            "id": 4,
            "uid": 2,
            "user": {
                "uid": 2,
                "avatar": "http:\/\/127.0.0.1\/test.jpg",
                "nickname": "test"
            }
        }
    ]
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-41">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion41" href="#collapseOne41"> 添加收藏 <span class="text-muted">/huodong/shoucang_add</span></a>
				</h4>
			</div>
			<div id="collapseOne41" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab41"><li class="active"><a href="#info41" data-toggle="tab">基础信息</a></li><li><a href="#sample41" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info41">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>huodong_id</td><td>string</td><td>活动编号</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample41">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response41">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-42">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion42" href="#collapseOne42"> 删除收藏 <span class="text-muted">/huodong/shoucang_del</span></a>
				</h4>
			</div>
			<div id="collapseOne42" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab42"><li class="active"><a href="#info42" data-toggle="tab">基础信息</a></li><li><a href="#sample42" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info42">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>ids</td><td>string</td><td>活动编号多个逗号隔开</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample42">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response42">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-43">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion43" href="#collapseOne43"> 点赞 <span class="text-muted">/huodong/zan_add</span></a>
				</h4>
			</div>
			<div id="collapseOne43" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab43"><li class="active"><a href="#info43" data-toggle="tab">基础信息</a></li><li><a href="#sample43" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info43">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>huodong_id</td><td>string</td><td>活动编号</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample43">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response43">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-44">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion44" href="#collapseOne44"> 取消点赞 <span class="text-muted">/huodong/zan_del</span></a>
				</h4>
			</div>
			<div id="collapseOne44" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab44"><li class="active"><a href="#info44" data-toggle="tab">基础信息</a></li><li><a href="#sample44" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info44">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>huodong_ids</td><td>string</td><td>活动编号多个逗号隔开</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample44">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response44">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-45">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion45" href="#collapseOne45"> 添加评价 <span class="text-muted">/huodong/add_pingjia</span></a>
				</h4>
			</div>
			<div id="collapseOne45" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab45"><li class="active"><a href="#info45" data-toggle="tab">基础信息</a></li><li><a href="#sample45" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info45">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>huodong_id</td><td>string</td><td>活动编号</td></tr><tr><td>contents</td><td>string</td><td>内容</td></tr><tr><td>imgs_url</td><td>string</td><td>图片地址,多个英文逗号隔开(可选)</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample45">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response45">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-46">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion46" href="#collapseOne46"> 获取评价 <span class="text-muted">/huodong/get_pingjia</span></a>
				</h4>
			</div>
			<div id="collapseOne46" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab46"><li class="active"><a href="#info46" data-toggle="tab">基础信息</a></li><li><a href="#sample46" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info46">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>huodong_id</td><td>string</td><td>活动编号</td></tr><tr><td>page</td><td>string</td><td>页码,默认1</td></tr><tr><td>page_size</td><td>string</td><td>每页多少条,默认20</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample46">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response46">{
    "status": "ok",
    "data": [
        {
            "id": 2,
            "uid": 2,
            "contents": "\u8bc4\u4ef7\u5185\u5bb9",
            "imgs_url": [
                "http:\/\/192.168.0.66\/test.jpg",
                "http:\/\/192.168.0.66\/test.jpg",
                "http:\/\/192.168.0.66\/test.jpg"
            ],
            "time": "2023-11-18 13:21:00",
            "user": {
                "uid": 2,
                "avatar": "http:\/\/127.0.0.1\/test.jpg",
                "nickname": "test",
                "is_huiyuan": 0
            }
        }
    ],
    "count": 1
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>	</div>					<h2>调试</h2>
					<hr>
					<div class="panel panel-default">			<div class="panel-heading" id="headingheading-47">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion47" href="#collapseOne47"> 共用 - 参数输出 <span class="text-muted">/index/index</span></a>
				</h4>
			</div>
			<div id="collapseOne47" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab47"><li class="active"><a href="#info47" data-toggle="tab">基础信息</a></li><li><a href="#sample47" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info47">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>任意参数</td><td></td><td></td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample47">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response47">{
    "status": "ok",
    "data": {
        "get": [],
        "post": {
            "a": "aaa",
            "b": "bbb"
        },
        "file": [],
        "input": "",
        "exec_params": {
            "a": "aaa",
            "b": "bbb"
        }
    }
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>	</div>					<h2>轮播图</h2>
					<hr>
					<div class="panel panel-default">			<div class="panel-heading" id="headingheading-48">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion48" href="#collapseOne48"> 获取轮播图 <span class="text-muted">/lunbotu/index</span></a>
				</h4>
			</div>
			<div id="collapseOne48" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab48"><li class="active"><a href="#info48" data-toggle="tab">基础信息</a></li><li><a href="#sample48" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info48">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample48">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response48">{
    "status": "ok",
    "data": [
        {
            "img_url": "http:\/\/127.0.0.1\/test.jpg",
            "title": "\u6d4b\u8bd5",
            "url_type": 2,
            "url_params": "1"
        }
    ]
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>	</div>					<h2>支付</h2>
					<hr>
					<div class="panel panel-default">			<div class="panel-heading" id="headingheading-49">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion49" href="#collapseOne49"> 余额支付 <span class="text-muted">/pay/yue_pay</span></a>
				</h4>
			</div>
			<div id="collapseOne49" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab49"><li class="active"><a href="#info49" data-toggle="tab">基础信息</a></li><li><a href="#sample49" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info49">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>type</td><td>string</td><td>类型:1=商城支付,2=活动报名,3=购买会员</td></tr><tr><td>order_id</td><td>string</td><td>订单编号</td></tr><tr><td>money</td><td>string</td><td>金额</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample49">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response49">{
    "status": "ok",
    "msg": "\u4f59\u989d\u652f\u4ed8\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-50">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion50" href="#collapseOne50"> 微信支付下单 <span class="text-muted">/pay/weixin_pay</span></a>
				</h4>
			</div>
			<div id="collapseOne50" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab50"><li class="active"><a href="#info50" data-toggle="tab">基础信息</a></li><li><a href="#sample50" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info50">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>type</td><td>string</td><td>类型:1=商城支付,2=活动报名,3=购买会员,4=充值</td></tr><tr><td>order_id</td><td>string</td><td>订单编号</td></tr><tr><td>money</td><td>string</td><td>金额</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample50">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response50">{
    "status": "ok",
    "prepay_id": "wx0116525740157731b210090a822cc80000"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-51">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion51" href="#collapseOne51"> 获取微信支付调起支付签名V3 <span class="text-muted">/pay/get_weixinpay_sign</span></a>
				</h4>
			</div>
			<div id="collapseOne51" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab51"><li class="active"><a href="#info51" data-toggle="tab">基础信息</a></li><li><a href="#sample51" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info51">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>prepay_id</td><td>string</td><td>预下单编号</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample51">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response51">{
    "timeStamp": 1692752801,
    "nonceStr": "xnlfmeljzkgjsrq6tk1insgwzsv5e36l",
    "package": "prepay_id=fdsafdsaf",
    "signType": "RSA",
    "paySign": "HLcpcOiTbGSJ0kFM2cyLMxsI\/04K1iuRC8Q3pztkPxsh1mQxJH93zNz7QZfNR9NIP31PjdKucB5SCaIYShPf\/+C1hHeAVqDy9kKOX3v1\/D4tdEBndV9kMqwb\/3wiMjcgSUoS5WnQQBn0QayL6\/eT9Cg3sUpNUwYGHcM1XEAfZ9RfOdHoW0zuhTpP5RTU119f9JL5omsp0Odi0gQ1igO2P81Jo7whL1EqWvtV4xjjiRz24a5lxEdUSboehZV4bawBpmDs2Xdc547z0I\/w6GTYONXNoQrf8MgutC+8nAeAS\/QLSVgIdpAgMxOHSJwLNfE14PwVs+jqoiR4x52qrmQKNg=="
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>	</div>					<h2>用户管理</h2>
					<hr>
					<div class="panel panel-default">			<div class="panel-heading" id="headingheading-52">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion52" href="#collapseOne52"> 用户注册/登录 <span class="text-muted">/user/login</span></a>
				</h4>
			</div>
			<div id="collapseOne52" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab52"><li class="active"><a href="#info52" data-toggle="tab">基础信息</a></li><li><a href="#sample52" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info52">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>code</td><td>string</td><td>微信登录code</td></tr><tr><td>pid</td><td>string</td><td>邀请人编号</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample52">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response52">{
    "status": "ok",
    "data": {
        "uid": 2,
        "mobile": "15800000000",
        "avatar": "http:\/\/127.0.0.1\/test.jpg",
        "nickname": "test",
        "sex": 2,
        "money": "0.00",
        "token": "7723cdc68268ccb1a77cd95c6a3097ee",
        "birthday": "1960-10-10",
        "gexingqianming": "\u4e2a\u6027\u7b7e\u540d",
        "is_huiyuan": 0,
        "reg_time": "2023-11-17 16:23:53",
        "labels": [
            {
                "id": 1,
                "label": "90\u540e"
            },
            {
                "id": 2,
                "label": "\u97f3\u4e50"
            }
        ],
        "imgs": [
            {
                "id": 1,
                "img_url": "http:\/\/127.0.0.1\/test.jpg"
            },
            {
                "id": 2,
                "img_url": "http:\/\/127.0.0.1\/test.jpg"
            }
        ]
    }
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-53">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion53" href="#collapseOne53"> 获取用户信息 <span class="text-muted">/user/get_user_info</span></a>
				</h4>
			</div>
			<div id="collapseOne53" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab53"><li class="active"><a href="#info53" data-toggle="tab">基础信息</a></li><li><a href="#sample53" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info53">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample53">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response53">{
    "status": "ok",
    "data": {
        "uid": 2,
        "mobile": "15800000000",
        "avatar": "http:\/\/127.0.0.1\/test.jpg",
        "nickname": "test",
        "sex": 2,
        "money": "0.00",
        "token": "7723cdc68268ccb1a77cd95c6a3097ee",
        "birthday": "1960-10-10",
        "gexingqianming": "\u4e2a\u6027\u7b7e\u540d",
        "is_huiyuan": 0,
        "reg_time": "2023-11-17 16:23:53",
        "labels": [
            {
                "id": 1,
                "label": "90\u540e"
            },
            {
                "id": 2,
                "label": "\u97f3\u4e50"
            }
        ],
        "imgs": [
            {
                "id": 1,
                "img_url": "http:\/\/127.0.0.1\/test.jpg"
            },
            {
                "id": 2,
                "img_url": "http:\/\/127.0.0.1\/test.jpg"
            }
        ]
    }
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-54">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion54" href="#collapseOne54"> 更新手机号 <span class="text-muted">/user/update_mobile</span></a>
				</h4>
			</div>
			<div id="collapseOne54" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab54"><li class="active"><a href="#info54" data-toggle="tab">基础信息</a></li><li><a href="#sample54" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info54">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>code</td><td>string</td><td>微信手机号code</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample54">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response54">{
    "status": "ok",
    "msg": "\u4fee\u6539\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-55">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion55" href="#collapseOne55"> 修改资料 <span class="text-muted">/user/update</span></a>
				</h4>
			</div>
			<div id="collapseOne55" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab55"><li class="active"><a href="#info55" data-toggle="tab">基础信息</a></li><li><a href="#sample55" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info55">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>avatar</td><td>string</td><td>头像地址(可选)</td></tr><tr><td>nickname</td><td>string</td><td>昵称(可选)</td></tr><tr><td>sex</td><td>string</td><td>性别:0=未知,1=男,2=女(可选)</td></tr><tr><td>birthday</td><td>string</td><td>出生日期(可选)</td></tr><tr><td>gexingqianming</td><td>string</td><td>个性签名(可选)</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample55">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response55">{
    "status": "ok",
    "msg": "\u4fee\u6539\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-56">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion56" href="#collapseOne56"> 添加标签 <span class="text-muted">/user/add_label</span></a>
				</h4>
			</div>
			<div id="collapseOne56" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab56"><li class="active"><a href="#info56" data-toggle="tab">基础信息</a></li><li><a href="#sample56" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info56">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>label</td><td>string</td><td>标签</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample56">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response56">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-57">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion57" href="#collapseOne57"> 删除标签 <span class="text-muted">/user/del_label</span></a>
				</h4>
			</div>
			<div id="collapseOne57" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab57"><li class="active"><a href="#info57" data-toggle="tab">基础信息</a></li><li><a href="#sample57" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info57">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>id</td><td>string</td><td>标签编号</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample57">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response57">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-58">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion58" href="#collapseOne58"> 添加资料照片 <span class="text-muted">/user/add_img</span></a>
				</h4>
			</div>
			<div id="collapseOne58" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab58"><li class="active"><a href="#info58" data-toggle="tab">基础信息</a></li><li><a href="#sample58" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info58">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>img_url</td><td>string</td><td>图片地址</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample58">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response58">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-59">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion59" href="#collapseOne59"> 删除资料照片 <span class="text-muted">/user/del_img</span></a>
				</h4>
			</div>
			<div id="collapseOne59" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab59"><li class="active"><a href="#info59" data-toggle="tab">基础信息</a></li><li><a href="#sample59" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info59">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>ids</td><td>string</td><td>资料照片编号,多个英文逗号隔开</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample59">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response59">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-60">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion60" href="#collapseOne60"> 添加收货地址 <span class="text-muted">/user/add_addr</span></a>
				</h4>
			</div>
			<div id="collapseOne60" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab60"><li class="active"><a href="#info60" data-toggle="tab">基础信息</a></li><li><a href="#sample60" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info60">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>sheng_id</td><td>string</td><td>省编号</td></tr><tr><td>shi_id</td><td>string</td><td>市编号</td></tr><tr><td>qu_id</td><td>string</td><td>区/县编号</td></tr><tr><td>addr</td><td>string</td><td>地址</td></tr><tr><td>username</td><td>string</td><td>收货人</td></tr><tr><td>mobile</td><td>string</td><td>手机号</td></tr><tr><td>is_default</td><td>string</td><td>是否默认地址默认0</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample60">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response60">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-61">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion61" href="#collapseOne61"> 删除收货地址 <span class="text-muted">/user/del_addr</span></a>
				</h4>
			</div>
			<div id="collapseOne61" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab61"><li class="active"><a href="#info61" data-toggle="tab">基础信息</a></li><li><a href="#sample61" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info61">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>ids</td><td>string</td><td>收货地址编号,多个英文逗号隔开</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample61">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response61">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-62">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion62" href="#collapseOne62"> 设置默认收货地址 <span class="text-muted">/user/set_default_addr</span></a>
				</h4>
			</div>
			<div id="collapseOne62" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab62"><li class="active"><a href="#info62" data-toggle="tab">基础信息</a></li><li><a href="#sample62" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info62">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>id</td><td>string</td><td>收货地址编号</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample62">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response62">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-63">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion63" href="#collapseOne63"> 获取收货地址列表 <span class="text-muted">/user/get_addr_list</span></a>
				</h4>
			</div>
			<div id="collapseOne63" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab63"><li class="active"><a href="#info63" data-toggle="tab">基础信息</a></li><li><a href="#sample63" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info63">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample63">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response63">{
    "status": "ok",
    "data": [
        {
            "id": 2,
            "uid": 2,
            "sheng": "\u5b89\u5fbd\u7701",
            "shi": "\u94dc\u9675\u5e02",
            "qu": "\u94dc\u5b98\u533a",
            "sheng_id": 1012,
            "shi_id": 1059,
            "qu_id": 1060,
            "addr": "\u67d0\u67d0\u8857\u9053\u67d0\u5c0f\u533a",
            "mobile": "***********",
            "username": "\u5f20\u4e09",
            "is_default": 1
        },
        {
            "id": 1,
            "uid": 2,
            "sheng": "\u5b89\u5fbd\u7701",
            "shi": "\u94dc\u9675\u5e02",
            "qu": "\u94dc\u5b98\u533a",
            "sheng_id": 1012,
            "shi_id": 1059,
            "qu_id": 1060,
            "addr": "\u67d0\u67d0\u8857\u9053\u67d0\u5c0f\u533a",
            "mobile": "***********",
            "username": "\u5f20\u4e09",
            "is_default": 0
        }
    ]
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-64">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion64" href="#collapseOne64"> 添加收款账号信息 <span class="text-muted">/user/bank_add</span></a>
				</h4>
			</div>
			<div id="collapseOne64" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab64"><li class="active"><a href="#info64" data-toggle="tab">基础信息</a></li><li><a href="#sample64" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info64">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>bank_name</td><td>string</td><td>银行名称</td></tr><tr><td>bank_num</td><td>string</td><td>银行卡号</td></tr><tr><td>username</td><td>string</td><td>收款姓名</td></tr><tr><td>is_default</td><td>string</td><td>是否默认,默认0</td></tr><tr><td>sms_code</td><td>string</td><td>短信验证码(可选)</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample64">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response64">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-65">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion65" href="#collapseOne65"> 删除收款账号信息 <span class="text-muted">/user/bank_del</span></a>
				</h4>
			</div>
			<div id="collapseOne65" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab65"><li class="active"><a href="#info65" data-toggle="tab">基础信息</a></li><li><a href="#sample65" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info65">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>ids</td><td>string</td><td>收款账户编号多个逗号隔开</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample65">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response65">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-66">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion66" href="#collapseOne66"> 获取收款账号信息 <span class="text-muted">/user/bank_list</span></a>
				</h4>
			</div>
			<div id="collapseOne66" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab66"><li class="active"><a href="#info66" data-toggle="tab">基础信息</a></li><li><a href="#sample66" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info66">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample66">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response66">{
    "status": "ok",
    "data": [
        {
            "id": 1,
            "bank_name": "\u5de5\u5546\u94f6\u884c",
            "bank_num": "*************",
            "username": "\u5f20\u4e09",
            "is_default": 0
        },
        {
            "id": 2,
            "bank_name": "\u652f\u4ed8\u5b9d",
            "bank_num": "***********",
            "username": "\u5f20\u4e09",
            "is_default": 0
        }
    ]
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-67">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion67" href="#collapseOne67"> 设置默认收款地址 <span class="text-muted">/user/set_default_bank</span></a>
				</h4>
			</div>
			<div id="collapseOne67" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab67"><li class="active"><a href="#info67" data-toggle="tab">基础信息</a></li><li><a href="#sample67" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info67">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>id</td><td>string</td><td>收款编号</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample67">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response67">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-68">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion68" href="#collapseOne68"> 提现 <span class="text-muted">/user/tixian</span></a>
				</h4>
			</div>
			<div id="collapseOne68" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab68"><li class="active"><a href="#info68" data-toggle="tab">基础信息</a></li><li><a href="#sample68" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info68">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>bank_id</td><td>string</td><td>收款编号</td></tr><tr><td>money</td><td>string</td><td>金额</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample68">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response68">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-69">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion69" href="#collapseOne69"> 获取提现记录 <span class="text-muted">/user/get_tixian_list</span></a>
				</h4>
			</div>
			<div id="collapseOne69" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab69"><li class="active"><a href="#info69" data-toggle="tab">基础信息</a></li><li><a href="#sample69" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info69">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>page</td><td>string</td><td>页码,默认1</td></tr><tr><td>page_size</td><td>string</td><td>每页多少条,默认20</td></tr><tr><td>status</td><td>string</td><td>状态:0=未处理,1=已通过,2=已拒绝</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample69">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response69">{
    "status": "ok",
    "data": [
        {
            "id": 1,
            "uid": 2,
            "money": "10.00",
            "daozhang_money": "9.49",
            "time": "2023-11-17 16:59:30",
            "status": 0,
            "bank_info": {
                "bank_name": "\u5de5\u5546\u94f6\u884c",
                "bank_num": "*************",
                "username": "\u5f20\u4e09"
            }
        }
    ],
    "count": 1
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-70">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion70" href="#collapseOne70"> 获取佣金 <span class="text-muted">/user/get_yongjin_log</span></a>
				</h4>
			</div>
			<div id="collapseOne70" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab70"><li class="active"><a href="#info70" data-toggle="tab">基础信息</a></li><li><a href="#sample70" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info70">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>page</td><td>string</td><td>页码,默认1</td></tr><tr><td>page_size</td><td>string</td><td>每页多少条,默认20</td></tr><tr><td>type</td><td>string</td><td>类型:1=会员佣金,2=活动佣金,3=商品佣金</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample70">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response70">{
    "status": "ok",
    "data": [
        {
            "id": 1,
            "money": "10.00",
            "type": 1,
            "time": "2023-11-17 17:03:16"
        }
    ],
    "count": 1
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-71">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion71" href="#collapseOne71"> 获取充值记录 <span class="text-muted">/user/get_chongzhi_log</span></a>
				</h4>
			</div>
			<div id="collapseOne71" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab71"><li class="active"><a href="#info71" data-toggle="tab">基础信息</a></li><li><a href="#sample71" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info71">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>page</td><td>string</td><td>页码,默认1</td></tr><tr><td>page_size</td><td>string</td><td>每页多少条,默认20</td></tr><tr><td>status</td><td>string</td><td>状态:0=未支付,1=已支付,2=已取消</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample71">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response71">{
    "status": "ok",
    "data": [
        {
            "id": 1,
            "order_id": "24324324",
            "money": "10.00",
            "time": "2023-11-17 17:04:36",
            "pay_time": null,
            "status": 0
        }
    ],
    "count": 1
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-72">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion72" href="#collapseOne72"> 获取分享记录 <span class="text-muted">/user/get_fenxiang_log</span></a>
				</h4>
			</div>
			<div id="collapseOne72" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab72"><li class="active"><a href="#info72" data-toggle="tab">基础信息</a></li><li><a href="#sample72" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info72">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>page</td><td>string</td><td>页码,默认1</td></tr><tr><td>page_size</td><td>string</td><td>每页多少条,默认20</td></tr><tr><td>type</td><td>string</td><td>类型:1=其他,2=活动,2=商品</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample72">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response72">{
    "status": "ok",
    "data": [
        {
            "id": 3,
            "type": 3,
            "item_id": 1,
            "time": "2023-11-17 17:06:31",
            "item_info": {
                "id": 1,
                "name": "\u5546\u54c11",
                "price": "10.00",
                "img_url": "http:\/\/127.0.0.1\/test.jpg"
            }
        },
        {
            "id": 2,
            "type": 2,
            "item_id": 1,
            "time": "2023-11-17 17:06:31",
            "item_info": {
                "id": 1,
                "name": "\u6d3b\u52a81",
                "title": "\u6d3b\u52a8\u6807\u9898",
                "img_url": "http:\/\/127.0.0.1\/test.jpg",
                "date": "2023-11-30",
                "start_time": "08:00:00",
                "end_time": "10:00:00",
                "baoming_start_time": "2023-11-17 17:11:18",
                "baoming_end_time": "2023-11-17 17:11:18",
                "num": 10
            }
        },
        {
            "id": 1,
            "type": 1,
            "item_id": 1,
            "time": "2023-11-17 17:06:20",
            "item_info": []
        }
    ],
    "count": 3
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-73">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion73" href="#collapseOne73"> 分享事件上传 <span class="text-muted">/user/fenxiang_event</span></a>
				</h4>
			</div>
			<div id="collapseOne73" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab73"><li class="active"><a href="#info73" data-toggle="tab">基础信息</a></li><li><a href="#sample73" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info73">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>type</td><td>string</td><td>类型:1=会员,2=活动,3=商品</td></tr><tr><td>item_id</td><td>string</td><td>类型内容编号</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample73">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response73">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-74">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion74" href="#collapseOne74"> 获取对账单 <span class="text-muted">/user/get_zhangdan</span></a>
				</h4>
			</div>
			<div id="collapseOne74" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab74"><li class="active"><a href="#info74" data-toggle="tab">基础信息</a></li><li><a href="#sample74" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info74">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>page</td><td>string</td><td>页码,默认1</td></tr><tr><td>page_size</td><td>string</td><td>每页多少条,默认20</td></tr><tr><td>type</td><td>string</td><td>类型:1=充值,2=活动费用结算,3=活动佣金结算,4=商品佣金结算,5=会员佣金结算,6=提现申请,7=提现驳回,8=活动费用支付,9=活动费用退还,10=抽奖中奖,11=商品支付,12=会员支付,13=商品退款</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample74">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response74">{
    "status": "ok",
    "data": [
        {
            "id": 1,
            "money": "10.00",
            "shengyu": "9990.00",
            "type": 6,
            "time": "2023-11-17 16:59:30"
        }
    ],
    "count": 1
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-75">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion75" href="#collapseOne75"> 充值下单 <span class="text-muted">/user/add_chongzhi_order</span></a>
				</h4>
			</div>
			<div id="collapseOne75" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab75"><li class="active"><a href="#info75" data-toggle="tab">基础信息</a></li><li><a href="#sample75" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info75">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>money</td><td>string</td><td>金额</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample75">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response75">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f",
    "order_id": "20231117171914966171",
    "money": "10.00"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-76">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion76" href="#collapseOne76"> 购买会员下单 <span class="text-muted">/user/add_huiyuan_order</span></a>
				</h4>
			</div>
			<div id="collapseOne76" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab76"><li class="active"><a href="#info76" data-toggle="tab">基础信息</a></li><li><a href="#sample76" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info76">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample76">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response76">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f",
    "order_id": "20231117172120001855",
    "money": "500"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-77">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion77" href="#collapseOne77"> 添加关注 <span class="text-muted">/user/guanzhu_add</span></a>
				</h4>
			</div>
			<div id="collapseOne77" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab77"><li class="active"><a href="#info77" data-toggle="tab">基础信息</a></li><li><a href="#sample77" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info77">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>to_uid</td><td>string</td><td>被关注用户编号</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample77">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response77">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-78">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion78" href="#collapseOne78"> 取消关注 <span class="text-muted">/user/guanzhu_del</span></a>
				</h4>
			</div>
			<div id="collapseOne78" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab78"><li class="active"><a href="#info78" data-toggle="tab">基础信息</a></li><li><a href="#sample78" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info78">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>to_uid</td><td>string</td><td>被关注用户编号</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample78">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response78">{
    "status": "ok",
    "msg": "\u64cd\u4f5c\u6210\u529f"
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-79">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion79" href="#collapseOne79"> 检测关注 <span class="text-muted">/user/guanzhu_check</span></a>
				</h4>
			</div>
			<div id="collapseOne79" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab79"><li class="active"><a href="#info79" data-toggle="tab">基础信息</a></li><li><a href="#sample79" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info79">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>to_uid</td><td>string</td><td>被关注用户编号</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample79">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response79">{
    "status": "ok",
    "data": 1
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-80">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion80" href="#collapseOne80"> 获取关注列表 <span class="text-muted">/user/get_guanzhu_list</span></a>
				</h4>
			</div>
			<div id="collapseOne80" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab80"><li class="active"><a href="#info80" data-toggle="tab">基础信息</a></li><li><a href="#sample80" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info80">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>page</td><td>string</td><td>页码,默认1</td></tr><tr><td>page_size</td><td>string</td><td>每页多少条,默认20</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample80">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response80">{
    "status": "ok",
    "data": [
        {
            "id": 3,
            "uid": 2,
            "to_uid": 1,
            "time": "2023-11-17 17:23:29",
            "to_user": {
                "uid": 2,
                "nickname": "test",
                "avatar": "http:\/\/127.0.0.1\/test.jpg",
                "mobile": "15800000000",
                "is_huiyuan": 0
            }
        }
    ],
    "count": 1
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-81">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion81" href="#collapseOne81"> 获取社交用户列表 <span class="text-muted">/user/get_shejiao_list</span></a>
				</h4>
			</div>
			<div id="collapseOne81" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab81"><li class="active"><a href="#info81" data-toggle="tab">基础信息</a></li><li><a href="#sample81" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info81">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>page</td><td>string</td><td>页码,默认1</td></tr><tr><td>page_size</td><td>string</td><td>每页多少条,默认20</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample81">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response81">{
    "status": "ok",
    "data": [
        {
            "uid": 2,
            "avatar": "http:\/\/127.0.0.1\/test.jpg",
            "nickname": "test",
            "sex": 2,
            "birthday": "1960-10-10",
            "gexingqianming": "\u4e2a\u6027\u7b7e\u540d",
            "is_huiyuan": 0,
            "imgs": [
                "http:\/\/127.0.0.1\/test.jpg",
                "http:\/\/127.0.0.1\/test.jpg"
            ],
            "is_guanzhu": 0
        }
    ],
    "count": 1
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>			<div class="panel-heading" id="headingheading-82">
				<h4 class="panel-title">
					<span class="label label-success">POST</span>
					<a data-toggle="collapse" data-parent="#accordion82" href="#collapseOne82"> 获取他人信息资料 <span class="text-muted">/user/get_other_user_info</span></a>
				</h4>
			</div>
			<div id="collapseOne82" class="panel-collapse collapse">
				<div class="panel-body">
					<!-- Nav tabs -->
					<ul class="nav nav-tabs" id="doctab82"><li class="active"><a href="#info82" data-toggle="tab">基础信息</a></li><li><a href="#sample82" data-toggle="tab">返回示例</a></li></ul>
					<!-- Tab panes -->
					<div class="tab-content">
						<div class="tab-pane active" id="info82">
							<div class="panel panel-default">
								<div class="panel-heading"><strong>参数</strong></div>
								<div class="panel-body">
									<table class="table table-hover">
										<thead><tr><th>名称</th><th>类型</th><th>描述</th></tr></thead>
										<tbody><tr><td>uid</td><td>string</td><td>用户编号</td></tr><tr><td>token</td><td>string</td><td>token</td></tr><tr><td>to_uid</td><td>string</td><td>他人用户编号</td></tr>										</tbody>
									</table>
								</div>
							</div>
						</div><!-- #info -->
						<div class="tab-pane" id="sample82">
							<div class="row">
								<div class="col-md-12">
									<pre id="sample_response82">{
    "status": "ok",
    "data": {
        "uid": 2,
        "mobile": "15800000000",
        "avatar": "http:\/\/127.0.0.1\/test.jpg",
        "nickname": "test",
        "sex": 2,
        "birthday": "1960-10-10",
        "gexingqianming": "\u4e2a\u6027\u7b7e\u540d",
        "is_huiyuan": 0,
        "reg_time": "2023-11-17 16:23:53",
        "labels": [
            {
                "id": 1,
                "label": "90\u540e"
            },
            {
                "id": 2,
                "label": "\u97f3\u4e50"
            }
        ],
        "imgs": [
            "http:\/\/127.0.0.1\/test.jpg",
            "http:\/\/127.0.0.1\/test.jpg"
        ]
    }
}</pre>
								</div>
							</div>
						</div><!-- #sample -->

					</div><!-- .tab-content -->
				</div>
			</div>	</div>

<!-- -container end-------------------------------------------- -->
</div>
<script src="/asset/js/jquery.min.js"></script>
<script src="/asset/js/bootstrap.min.3.0.3.js"></script>
<script type="text/javascript">
	function syntaxHighlight(json) {
		if (typeof json != 'string') {
			json = JSON.stringify(json, undefined, 2);
		}
		json = json.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
		return json.replace(/("(\u[a-zA-Z0-9]{4}|\[^u]|[^\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, function (match) {
			var cls = 'number';
			if (/^"/.test(match)) {
				if (/:$/.test(match)) {
					cls = 'key';
				} else {
					cls = 'string';
				}
			} else if (/true|false/.test(match)) {
				cls = 'boolean';
			} else if (/null/.test(match)) {
				cls = 'null';
			}
			return '<span class="' + cls + '">' + match + '</span>';
		});
	}

	function prepareStr(str) {
		try {
			return syntaxHighlight(JSON.stringify(JSON.parse(str.replace(/'/g, '"')), null, 2));
		} catch (e) {
			return str;
		}
	}

	$.fn.serializeObject = function ()
	{
		var o = {};
		var a = this.serializeArray();
		$.each(a, function () {
			if (!this.value) {
				return;
			}
			if (o[this.name] !== undefined) {
				if (!o[this.name].push) {
					o[this.name] = [o[this.name]];
				}
				o[this.name].push(this.value || '');
			} else {
				o[this.name] = this.value || '';
			}
		});
		return o;
	};

	$(document).ready(function () {

		$('[data-toggle="tooltip"]').tooltip({
			placement: 'bottom'
		});

		$(window).on("resize", function(){
			$("#sidebar").css("max-height", $(window).height()-80);
		});

		$(window).trigger("resize");

		$(document).on("click", "#sidebar .list-group > .list-group-item", function(){
			$("#sidebar .list-group > .list-group-item").removeClass("current");
			$(this).addClass("current");
		});
		$(document).on("click", "#sidebar .child a", function(){
			var heading = $("#headingheading-"+$(this).data("id"));
			if(!heading.next().hasClass("in")){
				$("a", heading).trigger("click");
			}
			$("html,body").animate({scrollTop:heading.offset().top-70});
		});

		$('code[id^=response]').hide();

		$.each($('pre[id^=sample_response],pre[id^=sample_post_body]'), function () {
			if ($(this).html() == 'NA') {
				return;
			}
			var str = prepareStr($(this).html());
			$(this).html(str);
		});

		$("[data-toggle=popover]").popover({placement: 'right'});

		$('[data-toggle=popover]').on('shown.bs.popover', function () {
			var sample = $(this).parent().find(".popover-content"),
					str = $(this).data('content');
			if (typeof str == "undefined" || str === "") {
				return;
			}
			var str = prepareStr(str);
			sample.html('<pre>' + str + '</pre>');
		});
		$("#api_length").text($(".text-muted").length);
	});
</script>
    </body>
</html>