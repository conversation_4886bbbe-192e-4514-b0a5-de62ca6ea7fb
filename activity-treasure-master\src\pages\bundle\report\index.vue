<template>
  <view class="page">
    <view class="container">
      <!-- 页面标题 -->
      <view class="page-header">
        <u-navbar 
            title="投诉/举报" 
            :border="false"
            :background="{ backgroundColor: '#ffffff' }"
            :titleStyle="{ color: '#333333', fontSize: '32rpx', fontWeight: 'bold' }"
            leftIcon="arrow-left"
            @leftClick="goBack"
        ></u-navbar>
      </view>

      <!-- 步骤指示器 -->
      <view class="steps-container">
        <view class="step-item" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
          <view class="step-number">1</view>
          <text class="step-text">选择对象</text>
        </view>
        <view class="step-line" :class="{ active: currentStep > 1 }"></view>
        <view class="step-item" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
          <view class="step-number">2</view>
          <text class="step-text">选择理由</text>
        </view>
        <view class="step-line" :class="{ active: currentStep > 2 }"></view>
        <view class="step-item" :class="{ active: currentStep >= 3 }">
          <view class="step-number">3</view>
          <text class="step-text">提交详情</text>
        </view>
      </view>

      <!-- 步骤1：选择举报对象 -->
      <view v-if="currentStep === 1" class="step-content">
        <view class="section-title">
          <u-text text="请选择要举报的用户" size="32rpx" bold color="#333"></u-text>
        </view>
        
        <view v-if="loading" class="loading-container">
          <u-loading-icon mode="spinner" size="60rpx"></u-loading-icon>
          <text class="loading-text">加载中...</text>
        </view>
        
        <view v-else-if="participants.length === 0" class="empty-container">
          <u-empty text="暂无其他参与者" mode="data"></u-empty>
        </view>
        
        <view v-else class="participants-list">
          <view 
              v-for="participant in participants" 
              :key="participant.uid"
              class="participant-item"
              @click="selectParticipant(participant)"
          >
            <u-avatar 
                :src="participant.avatar" 
                size="80rpx"
                :customStyle="{ marginRight: '24rpx' }"
            ></u-avatar>
            <view class="participant-info">
              <text class="participant-name">{{ participant.nickname }}</text>
              <text class="participant-role">{{ getRoleText(participant.role_type) }}</text>
            </view>
            <u-icon name="arrow-right" size="20" color="#999"></u-icon>
          </view>
        </view>
      </view>

      <!-- 步骤2：选择举报理由 -->
      <view v-if="currentStep === 2" class="step-content">
        <view class="section-title">
          <u-text text="请选择举报理由" size="32rpx" bold color="#333"></u-text>
        </view>
        
        <view class="selected-target">
          <text class="target-label">举报对象：</text>
          <text class="target-name">{{ selectedParticipant?.nickname }}</text>
        </view>
        
        <view v-if="loadingOptions" class="loading-container">
          <u-loading-icon mode="spinner" size="60rpx"></u-loading-icon>
          <text class="loading-text">加载举报选项...</text>
        </view>
        
        <view v-else class="report-options-list">
          <view 
              v-for="option in reportOptions" 
              :key="option.id"
              class="report-option-item"
              @click="selectReportOption(option)"
          >
            <view class="option-content">
              <text class="option-title">{{ option.title }}</text>
              <text class="option-description">{{ option.description }}</text>
            </view>
            <u-icon name="arrow-right" size="20" color="#999"></u-icon>
          </view>
        </view>
        
        <view class="step-buttons">
          <u-button 
              text="上一步" 
              color="#f5f5f5"
              :customStyle="buttonStyles.secondary"
              @click="prevStep"
          ></u-button>
        </view>
      </view>

      <!-- 步骤3：填写举报详情 -->
      <view v-if="currentStep === 3" class="step-content">
        <view class="section-title">
          <u-text text="举报详情" size="32rpx" bold color="#333"></u-text>
        </view>
        
        <view class="report-summary">
          <view class="summary-item">
            <text class="summary-label">举报对象：</text>
            <text class="summary-value">{{ selectedParticipant?.nickname }}</text>
          </view>
          <view class="summary-item">
            <text class="summary-label">举报理由：</text>
            <text class="summary-value">{{ selectedReportOption?.title }}</text>
          </view>
        </view>
        
        <view class="detail-form">
          <view class="form-item">
            <text class="form-label">详细说明 <text class="required">*</text></text>
            <u-textarea
                v-model="reportDetail"
                placeholder="请详细描述举报内容，提供具体事实和情况说明..."
                :height="300"
                :maxlength="500"
                :show-confirm-bar="false"
                :customStyle="textareaStyles"
            ></u-textarea>
            <text class="char-count">{{ reportDetail.length }}/500</text>
          </view>
        </view>
        
        <view class="step-buttons">
          <u-button 
              text="上一步" 
              color="#f5f5f5"
              :customStyle="buttonStyles.secondary"
              @click="prevStep"
          ></u-button>
          <u-button 
              text="提交举报" 
              color="linear-gradient(135deg, #FF6B6B 0%, #FF4757 100%)"
              :customStyle="buttonStyles.primary"
              :loading="submitting"
              @click="submitReport"
          ></u-button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { getAuthParams } from '@/utils/auth';
import { store } from '@/store';

// 页面参数
const activityId = ref('');

// 步骤控制
const currentStep = ref(1);

// 数据状态
const loading = ref(false);
const loadingOptions = ref(false);
const submitting = ref(false);

// 数据
const participants = ref([]);
const reportOptions = ref([]);
const selectedParticipant = ref(null);
const selectedReportOption = ref(null);
const reportDetail = ref('');

// 样式配置
const buttonStyles = {
  primary: {
    color: '#fff',
    fontWeight: 'bold',
    borderRadius: '30rpx',
    fontSize: '28rpx',
    width: '45%'
  },
  secondary: {
    color: '#666',
    fontWeight: 'bold',
    borderRadius: '30rpx',
    fontSize: '28rpx',
    width: '45%'
  }
};

const textareaStyles = {
  borderColor: '#E5E5E5',
  borderRadius: '20rpx',
  padding: '20rpx',
  backgroundColor: '#fafafa'
};

// 页面加载
onLoad((options) => {
  if (options.activity_id) {
    activityId.value = options.activity_id;
    loadParticipants();
  } else {
    uni.$u.toast('活动ID缺失');
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }
});

// 获取角色文本
const getRoleText = (roleType) => {
  const roleMap = {
    '0': '管理员',
    '1': '分会长',
    '2': '普通用户',
    '3': '场地方',
    '4': '城市分会长',
    '5': '邀请用户'
  };
  return roleMap[roleType] || '用户';
};

// 加载参与者列表
const loadParticipants = async () => {
  loading.value = true;
  try {
    const authParams = getAuthParams();
    if (!authParams) {
      uni.$u.toast('请先登录');
      uni.navigateBack();
      return;
    }
    
    // 检查举报权限
    const permissionRes = await uni.request({
      url: `${store().$state.config.apiBaseUrl}User/check_report_permission`,
      method: 'POST',
      data: authParams
    });
    
    if (permissionRes.data.status !== 'ok') {
      uni.$u.toast(permissionRes.data.msg || '无举报权限');
      uni.navigateBack();
      return;
    }
    
    // 获取活动参与者列表
    const participantsRes = await uni.request({
      url: `${store().$state.config.apiBaseUrl}User/get_activity_participants`,
      method: 'POST',
      data: {
        ...authParams,
        activity_id: activityId.value
      }
    });
    
    if (participantsRes.data.status === 'ok') {
      participants.value = participantsRes.data.data || [];
    } else {
      uni.$u.toast(participantsRes.data.msg || '获取参与者列表失败');
    }
  } catch (error) {
    console.error('加载参与者列表失败:', error);
    uni.$u.toast('操作失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 选择参与者
const selectParticipant = (participant) => {
  selectedParticipant.value = participant;
  currentStep.value = 2;
  loadReportOptions();
};

// 加载举报选项
const loadReportOptions = async () => {
  loadingOptions.value = true;
  try {
    const res = await uni.request({
      url: `${store().$state.config.apiBaseUrl}User/get_report_options`,
      method: 'GET'
    });
    
    if (res.data.status === 'ok') {
      reportOptions.value = res.data.data || [];
    } else {
      uni.$u.toast('获取举报选项失败');
    }
  } catch (error) {
    console.error('加载举报选项失败:', error);
    uni.$u.toast('操作失败，请稍后重试');
  } finally {
    loadingOptions.value = false;
  }
};

// 选择举报理由
const selectReportOption = (option) => {
  selectedReportOption.value = option;
  currentStep.value = 3;
};

// 上一步
const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  }
};

// 提交举报
const submitReport = async () => {
  if (!reportDetail.value.trim()) {
    uni.$u.toast('请输入举报详情');
    return;
  }
  
  submitting.value = true;
  try {
    const authParams = getAuthParams();
    const res = await uni.request({
      url: `${store().$state.config.apiBaseUrl}User/submit_report`,
      method: 'POST',
      data: {
        ...authParams,
        reported_uid: selectedParticipant.value.uid,
        activity_id: activityId.value,
        report_option_id: selectedReportOption.value.id,
        report_detail: reportDetail.value
      }
    });
    
    if (res.data.status === 'ok') {
      uni.showModal({
        title: '举报成功',
        content: '您的举报已提交，我们会尽快处理。感谢您的反馈！',
        showCancel: false,
        success: () => {
          uni.navigateBack();
        }
      });
    } else {
      uni.$u.toast(res.data.msg || '举报提交失败');
    }
  } catch (error) {
    console.error('提交举报失败:', error);
    uni.$u.toast('操作失败，请稍后重试');
  } finally {
    submitting.value = false;
  }
};

// 返回
const goBack = () => {
  uni.navigateBack();
};
</script>

<style scoped>
.page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #f8f9fb 100%);
}

.container {
  padding: 0 30rpx 30rpx;
}

.page-header {
  background: #ffffff;
  margin: 0 -30rpx 30rpx;
}

/* 步骤指示器 */
.steps-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #E5E5E5;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  transition: all 0.3s ease;
}

.step-item.active .step-number {
  background: #6AC086;
  color: #ffffff;
}

.step-item.completed .step-number {
  background: #6AC086;
  color: #ffffff;
}

.step-text {
  font-size: 24rpx;
  color: #999;
  transition: all 0.3s ease;
}

.step-item.active .step-text {
  color: #6AC086;
  font-weight: bold;
}

.step-line {
  width: 100rpx;
  height: 4rpx;
  background: #E5E5E5;
  margin: 0 20rpx;
  margin-bottom: 30rpx;
  transition: all 0.3s ease;
}

.step-line.active {
  background: #6AC086;
}

/* 步骤内容 */
.step-content {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  text-align: center;
  margin-bottom: 40rpx;
}

/* 加载和空状态 */
.loading-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}

/* 参与者列表 */
.participants-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.participant-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.participant-item:last-child {
  border-bottom: none;
}

.participant-item:active {
  background-color: #f8f9fa;
}

.participant-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.participant-name {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.participant-role {
  font-size: 24rpx;
  color: #999;
}

/* 选中目标显示 */
.selected-target {
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  border-left: 6rpx solid #6AC086;
}

.target-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}

.target-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

/* 举报选项列表 */
.report-options-list {
  max-height: 500rpx;
  overflow-y: auto;
}

.report-option-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.report-option-item:last-child {
  border-bottom: none;
}

.report-option-item:active {
  background-color: #f8f9fa;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
  display: block;
}

.option-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 举报摘要 */
.report-summary {
  background: #f8f9fa;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
}

.summary-item {
  display: flex;
  margin-bottom: 15rpx;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-label {
  font-size: 28rpx;
  color: #666;
  min-width: 140rpx;
}

.summary-value {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  flex: 1;
}

/* 表单 */
.detail-form {
  margin-bottom: 50rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 15rpx;
  display: block;
}

.required {
  color: #ff4757;
}

.char-count {
  font-size: 24rpx;
  color: #999;
  text-align: right;
  margin-top: 10rpx;
  display: block;
}

/* 按钮 */
.step-buttons {
  display: flex;
  justify-content: space-between;
  gap: 30rpx;
  margin-top: 50rpx;
}
</style>
