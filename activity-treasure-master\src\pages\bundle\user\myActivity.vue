<script setup>
import { watch, ref, reactive } from "vue";
import {
  huodongget_info,
  huodongget_my_list,
  huodongcancel_huodong,
  huodongcancel_baoming,
} from "@/api";
import {
  onLoad,
  onShow,
  onReady,
  onPageScroll,
  onPullDownRefresh,
  onReachBottom,
} from "@dcloudio/uni-app";
import { store } from "@/store";
import useMescroll from "@/uni_modules/mescroll-uni/hooks/useMescroll.js";
import { setListHeight, navto, getItem } from "@/utils";
import { requireLogin, isLoggedIn, getCurrentUser } from "@/utils/auth";

// {{ AURA-X: Modify - 重新组织tab结构：删除我的点赞，我的评价改为我的提问，调整顺序. Confirmed via 寸止. }}
const tabList = ref([
  {
    name: "我的报名",
  },
  {
    name: "我的发布",
  },
  {
    name: "我的提问",
  },
  {
    name: "我的收藏",
  },
]);
const type = ref(2);
const goods = ref([]);
// 调用mescroll的hook (注: mescroll-uni不用传onPageScroll,onReachBottom, 而mescroll-body必传)
const { mescrollInit, downCallback, getMescroll } = useMescroll(
    onPageScroll,
    onReachBottom
);
const height = ref("");
const current = ref(0);

// {{ AURA-X: Add - 添加操作菜单状态管理. Confirmed via 寸止. }}
// 操作菜单状态
const showMenu = ref(false);
const selectedActivity = ref(null);
const selectedIndex = ref(-1);

// 添加一个初始化函数，确保页面首次加载时显示正确的数据
const initPage = (optionType) => {
  if (optionType) {
    // {{ AURA-X: Modify - 更新参数映射表，适配新的tab结构. Confirmed via 寸止. }}
    // 参数映射表
    const typeMap = {
      '1': 1, // 我的发布
      '2': 0, // 我的报名
      '3': 2, // 我的提问
      '4': 3  // 我的收藏
    };
    
    // 如果有合法的type参数，设置对应的选项卡
    if (typeMap[optionType] !== undefined) {
      current.value = typeMap[optionType];
      type.value = parseInt(optionType);
    } else {
      current.value = 0; // 默认显示我的报名
      type.value = 2;
    }
  } else {
    current.value = 0; // 默认显示我的报名
    type.value = 2;
  }
  
  console.log(`初始化页面 - 选项卡索引: ${current.value}, 数据类型: ${type.value}`);
};

onLoad((e) => {
  console.log('页面加载参数:', e);

  // 检查登录状态
  if (!isLoggedIn()) {
    console.log('用户未登录，跳转到登录页面');
    requireLogin('/pages/bundle/user/myActivity', '请先登录后查看我的活动');
    return;
  }

  console.log('用户已登录，用户信息:', getCurrentUser());
  initPage(e.type);
});
onReady(async () => {
  height.value = (await setListHeight()) + "px";
});
onShow(() => {
  console.log('当前选项卡索引:', current.value);
  console.log('当前数据类型:', type.value);
  getMescroll().resetUpScroll(true);
});

// 上拉加载的回调: 其中num:当前页 从1开始, size:每页数据条数,默认10
const upCallback = async (mescroll) => {
  try {
    console.log('请求我的活动列表，参数:', {
      page: mescroll.num,
      page_size: mescroll.size,
      type: type.value,
      userInfo: getCurrentUser()
    });

    const res = await huodongget_my_list({
      page: mescroll.num,
      page_size: mescroll.size,
      type: type.value,
    });

    console.log('我的活动列表API响应:', res);

    // {{ AURA-X: Modify - 修复我的收藏tab空数据处理，避免报错显示友好提示. Confirmed via 寸止. }}
    // 处理特殊返回值 'n' 的情况（通常表示无数据）
    if (res === 'n' || res === null || res === undefined) {
      if (mescroll.num == 1) goods.value = [];
      mescroll.endBySize(0, 0);

      // {{ AURA-X: Modify - 更新空数据提示信息，适配新的tab结构和type值. Confirmed via 寸止. }}
      // 根据当前type值显示不同的提示
      const emptyMessages = {
        1: '暂无发布活动',
        2: '暂无报名活动',
        3: '暂无收藏内容',
        4: '暂无提问记录'
      };

      if (mescroll.num == 1) {
        const message = emptyMessages[type.value] || '暂无数据';
        console.log(message);
        // 不显示toast，让页面自然显示空状态
      }
      return;
    }

    if (res.status === 'ok') {
      const curPageData = res.data || [];
      if (mescroll.num == 1) goods.value = [];
      goods.value = goods.value.concat(curPageData);
      mescroll.endBySize(curPageData.length, res.count);
      console.log(`加载成功，当前页数据: ${curPageData.length} 条，总数: ${res.count}`);
    } else if (res.status === 'empty') {
      // 空数据状态，显示友好提示
      console.log('暂无活动数据');
      if (mescroll.num == 1) goods.value = [];
      mescroll.endBySize(0, 0);
      // 不显示toast，让页面自然显示空状态
    } else if (res.status === 'relogin') {
      console.warn('需要重新登录:', res.msg);
      mescroll.endErr();
      requireLogin('/pages/bundle/user/myActivity', res.msg || '登录已过期，请重新登录');
    } else {
      // 真正的请求失败
      console.error('获取我的活动列表失败:', res);
      mescroll.endErr();
      if (mescroll.num == 1) {
        uni.showToast({ title: res.msg || '获取数据失败', icon: 'none' });
      }
    }
  } catch (error) {
    console.error('请求我的活动列表异常:', error);
    mescroll.endErr();
    if (mescroll.num == 1) {
      uni.showToast({ title: '网络错误，请检查网络连接', icon: 'none' });
    }
  }
};

// {{ AURA-X: Modify - 修复iOS时间格式兼容性问题. Confirmed via 寸止. }}
// 添加iOS兼容的时间格式转换函数
const formatDateForIOS = (dateStr) => {
  if (!dateStr) return null;
  // 将 "yyyy-MM-dd HH:mm:ss" 格式转换为 "yyyy/MM/dd HH:mm:ss" 以兼容iOS
  return dateStr.replace(/-/g, '/');
};

// 添加格式化日期的函数
const formatDate = (dateStr) => {
  if (!dateStr) return '';

  const date = new Date(formatDateForIOS(dateStr));
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');

  // 计算日期是否在近10天内
  const now = new Date();
  const diffTime = Math.abs(date - now);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  const weekday = weekdays[date.getDay()];

  return diffDays <= 10 ? `${month}月${day}日 ${weekday}` : `${month}月${day}日`;
};

// 修改changeTabs函数，处理顺序变化
const changeTabs = (e) => {
  // {{ AURA-X: Modify - 更新tab索引映射，适配新的tab结构. Confirmed via 寸止. }}
  // {{ AURA-X: Modify - 修正type值映射，确保与后端API一致. Confirmed via 寸止. }}
  // 重新映射tab索引到type值
  const typeMap = {
    0: 2, // 我的报名 (type=2)
    1: 1, // 我的发布 (type=1)
    2: 4, // 我的提问 (type=4, 原评价)
    3: 3  // 我的收藏 (type=3)
  };

  type.value = typeMap[e.index];
  current.value = e.index;
  goods.value = [];
  
  console.log(`切换选项卡 - 新索引: ${e.index}, 新数据类型: ${type.value}`);
  
  getMescroll().resetUpScroll(true);
};

// {{ AURA-X: Add - 添加操作菜单相关函数. Confirmed via 寸止. }}
// 显示操作菜单
const showActionMenu = (activity, index) => {
  selectedActivity.value = activity;
  selectedIndex.value = index;
  showMenu.value = true;
};

// 关闭操作菜单
const closeActionMenu = () => {
  showMenu.value = false;
  selectedActivity.value = null;
  selectedIndex.value = -1;
};

// 检查活动是否可以修改/取消（基于开始时间）
const canModifyActivity = (activity) => {
  if (!activity?.huodong_info?.start_time) return false;
  const now = new Date();
  const startTime = new Date(formatDateForIOS(activity.huodong_info.start_time));
  return now < startTime;
};

// {{ AURA-X: Modify - 修复活动结束状态判断函数，根据不同数据结构使用正确字段. Confirmed via 寸止. }}
// 检查活动是否已结束（活动开始时间已过）
const isActivityEnded = (activity) => {
  // 根据数据结构获取正确的开始时间字段
  const startTime = activity?.start_time || activity?.huodong_info?.start_time;
  if (!startTime) return false;

  const now = new Date();
  const activityStartTime = new Date(formatDateForIOS(startTime));
  return now >= activityStartTime;
};

// 取消活动
const cancelActivity = async () => {
  if (!selectedActivity.value) return;

  try {
    // {{ AURA-X: Modify - 修复活动ID字段访问路径，使用正确的字段名. Confirmed via 寸止. }}
    const activityId = selectedActivity.value.huodong_info?.id || selectedActivity.value.huodong_id;
    const res = await huodongcancel_huodong({
      huodong_id: activityId
    });

    if (res.status === "ok") {
      uni.$u.toast('活动已取消');
      getMescroll().resetUpScroll(true);
      closeActionMenu();
    } else {
      uni.$u.toast(res.msg || '取消失败');
    }
  } catch (error) {
    console.error('取消活动失败:', error);
    uni.$u.toast('取消失败，请重试');
  }
};

// 修改活动
const editActivity = () => {
  if (!selectedActivity.value) return;

  const activityId = selectedActivity.value.huodong_info?.id || selectedActivity.value.huodong_id;
  closeActionMenu();
  navto(`/pages/bundle/index/addActive?huodong_id=${activityId}`);
};

// 再办一场
const duplicateActivity = () => {
  if (!selectedActivity.value) return;

  const activityId = selectedActivity.value.huodong_info?.id || selectedActivity.value.huodong_id;
  console.log('再办一场活动，ID:', activityId);

  closeActionMenu();
  // 直接传递活动ID，让发布页面从数据库查询最新数据
  navto(`/pages/bundle/index/addActive?duplicate=1&duplicate_id=${activityId}`);
};

// 处理操作菜单选项点击
const handleMenuAction = (action) => {
  if (!selectedActivity.value) return;

  const canModify = canModifyActivity(selectedActivity.value);

  switch (action) {
    case 'cancel':
      if (!canModify) {
        uni.$u.toast('活动已开始，无法取消');
        return;
      }
      // 确认取消
      uni.showModal({
        title: '确认取消',
        content: '确定要取消这个活动吗？取消后无法恢复。',
        success: (res) => {
          if (res.confirm) {
            cancelActivity();
          }
        }
      });
      break;

    case 'edit':
      if (!canModify) {
        uni.$u.toast('活动已开始，无法修改');
        return;
      }
      editActivity();
      break;

    case 'duplicate':
      duplicateActivity();
      break;

    case 'close':
      closeActionMenu();
      break;
  }
};

// 检查活动是否可以上传相册（活动已开始即可）
const canUploadAlbum = (activityInfo) => {
  // 根据数据结构获取正确的开始时间字段
  const startTime = activityInfo?.start_time || activityInfo?.huodong_info?.start_time;
  if (!startTime) return false;

  const now = new Date();
  const activityStartTime = new Date(formatDateForIOS(startTime));

  // 活动已开始即可上传相册（包括已结束的活动）
  return now >= activityStartTime;
};

// {{ AURA-X: Add - 判断是否应该显示上传按钮，用于动态调整卡片高度. Confirmed via 寸止. }}
const shouldShowUploadButton = (val) => {
  // 只有在"我的发布"tab且活动状态不是已取消时才可能显示上传按钮
  if (current.value !== 1 || val.huodong_info?.status === 3) {
    return false;
  }
  // 检查是否可以上传相册
  return canUploadAlbum(val);
};

// 跳转到活动相册页面
const goToActivityAlbum = (activityId) => {
  navto(`/pages/bundle/activity/album?activity_id=${activityId}`);
};

// {{ AURA-X: Add - 添加活动信息调试函数. Confirmed via 寸止. }}
// 打印活动信息到控制台用于调试
const logActivityInfo = (val, index) => {
  console.log(`=== 活动信息调试 [Tab: ${current.value}, Index: ${index}] ===`);
  console.log('完整活动数据:', val);
  console.log('活动字段检查:');
  console.log('- val.huodong_id:', val.huodong_id);
  console.log('- val.start_time:', val.start_time);
  console.log('- val.time:', val.time);
  console.log('- val.status:', val.status);

  if (val.huodong_info) {
    console.log('huodong_info字段检查:');
    console.log('- val.huodong_info.huodong_id:', val.huodong_info.huodong_id);
    console.log('- val.huodong_info.id:', val.huodong_info.id);
    console.log('- val.huodong_info.start_time:', val.huodong_info.start_time);
    console.log('- val.huodong_info.status:', val.huodong_info.status);
    console.log('- val.huodong_info.name:', val.huodong_info.name);
    console.log('- val.huodong_info.baoming_start_time:', val.huodong_info.baoming_start_time);
    console.log('- val.huodong_info.baoming_end_time:', val.huodong_info.baoming_end_time);
  } else {
    console.log('huodong_info: null 或 undefined');
  }

  console.log('=== 调试结束 ===');
  return ''; // 返回空字符串，不影响模板渲染
};
// 取消报名
const cancelHuodong = async (order_id) => {
  const res = await huodongcancel_baoming({ order_id });
  if (res.status === "ok") getMescroll().resetUpScroll(true);
  else uni.$u.toast(res.msg);
};

// 图片加载错误处理
const imageError = (i) => {
  // 当图片加载失败时，设置一个默认图片
  if (goods.value[i] && goods.value[i].huodong_info) {
    goods.value[i].huodong_info.img_url_fallback = true;
    console.log(`活动图片加载失败，已切换到默认图片: 索引${i}`);
  }
};

/**
 * 处理列表项点击，根据不同类型跳转到相应的详情页
 * @param {Object} item - 列表项数据
 * @param {Number} index - 列表项索引
 */
const handleItemClick = (item, index) => {
  console.log('点击项目:', item, '类型:', type.value);
  
  // 不同选项卡对应不同类型内容
  switch (current.value) {
    case 0: // 我的报名 - 活动详情
      const activityId0 = item.huodong_info?.id || item.huodong_id;
      if (activityId0) {
        navto(`/pages/bundle/index/activeInfo?id=${activityId0}`);
      }
      break;

    case 1: // 我的发布 - 活动详情
      const activityId1 = item.huodong_info?.id || item.huodong_id;
      if (activityId1) {
        navto(`/pages/bundle/index/activeInfo?id=${activityId1}`);
      }
      break;

    case 2: // 我的提问 - 活动详情
      const activityId2 = item.huodong_info?.id || item.huodong_id;
      if (activityId2) {
        navto(`/pages/bundle/index/activeInfo?id=${activityId2}`);
      }
      break;

    case 3: // 我的收藏 - 支持多种内容类型
      const activityId3 = item.huodong_info?.id || item.huodong_id;
      if (activityId3) {
        navto(`/pages/bundle/index/activeInfo?id=${activityId3}`);
      } else if (item.feed_id) {
        navto(`/pages/bundle/world/feed/detail?id=${item.feed_id}`);
      } else if (item.card_id) {
        navto(`/pages/bundle/world/card/detail?cardId=${item.card_id}`);
      } else if (item.quote_id) {
        navto(`/pages/bundle/world/quote/detail?id=${item.quote_id}`);
      }
      break;
      
    default:
      console.log('未知选项卡类型');
      break;
  }
};
</script>
<template>
  <view class="page">
    <myTitle
        bgColor="linear-gradient(135deg, #88D7A0 0%, #6AC086 100%)"
        height="176rpx"
        title="我的活动"
        color="#ffffff"
        :blod="true"
    ></myTitle>
    <view class="tab-container">
      <u-tabs
          :current="current"
          :list="tabList"
          :active-style="{
          borderRadius: '25rpx',
          textAlign: 'center',
          lineHeight: '56rpx',
          fontSize: '28rpx',
          color: '#ffffff',
          fontWeight: '600',
          backgroundColor: '#6AC086',
          boxShadow: '0 8rpx 24rpx rgba(106, 192, 134, 0.3), 0 4rpx 12rpx rgba(106, 192, 134, 0.2)',
          transition: 'all 0.3s ease',
          transform: 'translateY(-2rpx)'
        }"
          :inactiveStyle="{
          fontSize: '26rpx',
          color: '#6c757d',
          fontWeight: '400',
          borderRadius: '20rpx',
          backgroundColor: 'transparent'
        }"
          :itemStyle="{
          padding: '20rpx 32rpx',
          margin: '0 8rpx',
          minWidth: '120rpx'
        }"
          lineWidth="0"
          @click="changeTabs"
      ></u-tabs>
    </view>
    <view class="px30">
      <mescroll-uni
          class="list"
          :height="height"
          :down="{
          auto: false,
        }"
          @init="mescrollInit"
          @down="downCallback"
          @up="upCallback"
          @topclick="$event.scrollTo(0)"
      >
        <view
          class="activity-item"
          :class="{
            'activity-item-compact': !shouldShowUploadButton(val),
            'activity-item-registration': current === 0
          }"
          v-for="(val, i) in goods"
          :key="i"
          @click="handleItemClick(val, i)"
        >
          <!-- {{ AURA-X: Add - 添加活动信息调试打印. Confirmed via 寸止. }} -->
          {{ logActivityInfo(val, i) }}

          <!-- 操作菜单图标 (仅在我的发布tab显示) -->
          <view v-if="current === 1" class="activity-menu-icon" @click.stop="showActionMenu(val, i)">
            <u-icon name="more-dot-fill" color="#6AC086" size="24"></u-icon>
          </view>

          <!-- 左侧固定宽高的缩略图 -->
          <view class="activity-thumb">
            <image
                class="activity-image"
                :src="val.huodong_info?.img_url_fallback ? `${store().$state.url}default_activity.png` : val.huodong_info?.img_url"
                mode="aspectFill"
                lazy-load
                @error="imageError(i)"
            ></image>
          </view>

          <!-- 右侧活动信息 -->
          <view class="activity-info">
            <!-- 活动状态标签 -->
            <view class="activity-status-tags">
              <view class="status-tag" v-if="current === 0 || current === 1">
                <text>{{
                    getItem(
                      ['未开始', '报名中', '已结束'],
                      Date.now() * 1 < new Date(formatDateForIOS(val.huodong_info?.baoming_start_time || val.baoming_start_time)) * 1
                        ? 0
                        : Date.now() * 1 >= new Date(formatDateForIOS(val.huodong_info?.start_time || val.start_time)) * 1
                        ? 2
                        : 1
                    )
                  }}</text>
              </view>

              <view class="status-tag secondary" v-if="current === 0">
                <text>{{
                    getItem(
                      ['未支付', '已报名', '已取消', '退款中', '退款成功', '退款失败', '支付失败已删除'],
                      val.status
                    )
                  }}</text>
              </view>

              <view class="status-tag secondary" v-if="current === 1">
                <text>{{
                    getItem(
                      ['审核中', '审核通过', '审核未通过', '活动已取消'],
                      val.huodong_info?.status
                    )
                  }}</text>
              </view>
            </view>

            <!-- 活动标题 -->
            <view class="activity-title">
              <u-text
                  size="30rpx"
                  :bold="true"
                  lines="1"
                  :text="val.huodong_info?.name"
              ></u-text>
            </view>

            <!-- 活动日期 -->
            <view class="activity-date">
              <view class="date-line">
                <u-icon
                    :name="`${store().$state.url}time.png`"
                    size="24rpx"
                    :label="formatDate(val.huodong_info?.start_time)"
                    space="8rpx"
                    color="#666666"
                    label-size="24rpx"
                    label-color="#666666"
                ></u-icon>
                <view class="date-line-hr"></view>
              </view>
            </view>

            <!-- 活动位置 -->
            <view class="activity-location">
              <u-text
                  :prefix-icon="`${store().$state.url}place.png`"
                  icon-style="margin-right:8rpx;width:24rpx;height:24rpx;"
                  :text="
                  val.huodong_info?.sheng +
                  val.huodong_info?.shi +
                  val.huodong_info?.qu +
                  val.huodong_info?.addr
                "
                  lines="1"
                  color="#666666"
                  size="24rpx"
              ></u-text>
            </view>

            <!-- 报名人数 -->
            <view class="activity-participants">
              <u-icon
                  :name="`${store().$state.url}man.png`"
                  size="24rpx"
                  :label="`${val.huodong_info?.baoming_num}人报名`"
                  label-color="#666666"
                  space="8rpx"
                  label-size="24rpx"
              ></u-icon>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="activity-actions" v-if="current == 1 && val.huodong_info?.status != 3">
            <u-button
              v-if="canUploadAlbum(val)"
              text="上传活动相册"
              shape="circle"
              color="linear-gradient(135deg, #fff8dc 0%, #f0f8e8 50%, #e8f5e8 100%)"
              :customStyle="{
                margin: '15rpx auto 0',
                width: '80%',
                height: '70rpx',
                fontSize: '28rpx',
                color: '#333',
                fontWeight: '600',
                boxShadow: '0 4rpx 12rpx rgba(106, 192, 134, 0.2)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                textAlign: 'center'
              }"
              @click.stop="goToActivityAlbum(val.huodong_info?.id || val.huodong_id)"
            ></u-button>
          </view>

          <view class="activity-actions" v-if="current === 0">
            <u-button
                v-if="val.status === 1 && !isActivityEnded(val.huodong_info)"
                text="取消报名"
                shape="circle"
                color="linear-gradient(135deg, #dc3545 0%, #c82333 100%)"
                :customStyle="{
                margin: '10rpx 10rpx 0',
                width: '150rpx',
                height: '50rpx',
                fontSize: '24rpx',
                color: '#fff',
                fontWeight: '500',
                boxShadow: '0 2rpx 8rpx rgba(220, 53, 69, 0.25)'
              }"
                @click.stop="cancelHuodong(val.order_id)"
            ></u-button>

            <!-- 活动相册按钮（仅在活动已开始显示） -->
            <u-button
              v-if="canUploadAlbum(val)"
              text="上传活动相册"
              shape="circle"
              color="linear-gradient(135deg, #fff8dc 0%, #f0f8e8 50%, #e8f5e8 100%)"
              :customStyle="{
                margin: '15rpx auto 0',
                width: '80%',
                height: '70rpx',
                fontSize: '28rpx',
                color: '#333',
                fontWeight: '600',
                boxShadow: '0 4rpx 12rpx rgba(106, 192, 134, 0.2)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                textAlign: 'center'
              }"
              @click.stop="goToActivityAlbum(val.huodong_info?.id || val.huodong_id)"
            ></u-button>
          </view>
        </view>
      </mescroll-uni>
    </view>

    <!-- {{ AURA-X: Add - 添加底部操作菜单. Confirmed via 寸止. }} -->
    <!-- 操作菜单遮罩和弹窗 -->
    <view v-if="showMenu" class="action-menu-overlay" @click="closeActionMenu">
      <view class="action-menu" @click.stop>
        <view class="action-menu-header">
          <text class="menu-title">{{ selectedActivity?.huodong_info?.name || '活动操作' }}</text>
        </view>

        <view class="action-menu-options">
          <view
            class="menu-option"
            :class="{ disabled: !canModifyActivity(selectedActivity) }"
            @click="handleMenuAction('cancel')"
          >
            <u-icon name="close-circle" color="#dc3545" size="20"></u-icon>
            <text class="option-text" :style="{ color: canModifyActivity(selectedActivity) ? '#333' : '#999' }">
              取消活动
            </text>
          </view>

          <view
            class="menu-option"
            :class="{ disabled: !canModifyActivity(selectedActivity) }"
            @click="handleMenuAction('edit')"
          >
            <u-icon name="edit-pen" color="#6AC086" size="20"></u-icon>
            <text class="option-text" :style="{ color: canModifyActivity(selectedActivity) ? '#333' : '#999' }">
              修改活动
            </text>
          </view>

          <view class="menu-option" @click="handleMenuAction('duplicate')">
            <u-icon name="plus-circle" color="#17a2b8" size="20"></u-icon>
            <text class="option-text">再办一场</text>
          </view>
        </view>

        <view class="action-menu-footer">
          <view class="menu-cancel" @click="handleMenuAction('close')">
            <text class="cancel-text">取消</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="less">
.page {
  min-height: 100vh;
  background: linear-gradient(180deg, #E6F6E1 0%, #FDFDFD 50%, #F8F8F8 100%);
}

.activity-item {
  display: flex;
  margin-bottom: 24rpx;
  padding: 24rpx;
  padding-bottom: 80rpx;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  border: 1rpx solid rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

/* {{ AURA-X: Add - 紧凑版卡片样式，用于没有上传按钮的情况. Confirmed via 寸止. }} */
.activity-item-compact {
  padding-bottom: 24rpx !important;
}

/* {{ AURA-X: Add - 我的报名卡片样式，增加高度. Confirmed via 寸止. }} */
.activity-item-registration {
  padding-bottom: 120rpx !important;
}

/* {{ AURA-X: Modify - 调整操作菜单图标为横向长方形加圆角. Confirmed via 寸止. }} */
.activity-menu-icon {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  width: 120rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 10;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(106, 192, 134, 0.2);
}

.activity-menu-icon:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.05);
}

.activity-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.12);
}

.activity-thumb {
  flex-shrink: 0;
  width: 160rpx;
  height: 160rpx;
  margin-right: 24rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 16rpx rgba(106, 192, 134, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.8);
}

.activity-image {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
}

.activity-info {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-bottom: 50rpx;
}

.activity-status-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12rpx;
}

.status-tag {
  padding: 6rpx 16rpx;
  margin-right: 12rpx;
  margin-bottom: 6rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #88D7A0, #6AC086);
  font-size: 20rpx;
  color: #ffffff;
  font-weight: 500;
  box-shadow: 0 2rpx 6rpx rgba(106, 192, 134, 0.3);
  position: relative;
  display: flex;
  align-items: center;

  /* 移除状态指示器圆点 */
  &::before {
    display: none;
  }
}

.status-tag.secondary {
  background: linear-gradient(135deg, #A8E6C1, #88D7A0);
  color: #ffffff;

  /* 移除状态指示器圆点 */
  &::before {
    display: none;
  }
}

/* 全局移除可能的状态圆点 */
.status-tag text::before,
.status-tag text::after {
  display: none !important;
}

/* 移除可能的uView组件默认圆点 */
.u-text::before,
.u-text::after {
  display: none !important;
}

.activity-title {
  margin-bottom: 16rpx;
  font-weight: 600;
  font-size: 30rpx;
  color: #212529;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
}

.activity-date {
  margin-bottom: 12rpx;
}

.date-line {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.date-line-hr {
  flex: 1;
  height: 1rpx;
  background: linear-gradient(to right, #dee2e6, transparent);
  margin-left: 12rpx;
}

.activity-location {
  margin-bottom: 12rpx;
  color: #6c757d;
  font-size: 24rpx;
}

.activity-participants {
  color: #6c757d;
  margin-bottom: 10rpx;
  padding-right: 180rpx;
  font-size: 24rpx;
}

/* {{ AURA-X: Modify - 优化活动操作按钮布局，支持居中显示. Confirmed via 寸止. }} */
.activity-actions {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  left: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 5;
  gap: 12rpx;
}

/* {{ AURA-X: Add - 添加操作菜单弹窗样式. Confirmed via 寸止. }} */
/* 操作菜单样式 */
.action-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.action-menu {
  width: 100%;
  max-width: 750rpx;
  background: #ffffff;
  border-radius: 32rpx 32rpx 0 0;
  padding: 40rpx 32rpx;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.action-menu-header {
  text-align: center;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
}

.action-menu-options {
  margin-bottom: 32rpx;
}

.menu-option {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
  transition: all 0.3s ease;
}

.menu-option:last-child {
  border-bottom: none;
}

.menu-option.disabled {
  opacity: 0.5;
}

.option-text {
  margin-left: 24rpx;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.action-menu-footer {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 24rpx;
}

.menu-cancel {
  text-align: center;
  padding: 24rpx 0;
  background: #f8f9fa;
  border-radius: 16rpx;
}

/* {{ AURA-X: Add - tab容器样式优化，符合项目设计系统. Confirmed via 寸止. }} */
.tab-container {
  padding: 24rpx 30rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 16rpx;
}

.tab-container .u-tabs {
  background: #f8f9fa;
  border-radius: 24rpx;
  padding: 8rpx;
}

.cancel-text {
  font-size: 30rpx;
  color: #666;
  font-weight: 500;
}
</style>
