<!DOCTYPE html>
<html>
<?php
	dbConn();
	$houtai_logo = \core\Cache::getCache("houtai_logo");
	if(empty($houtai_logo)){
		$houtai_logo = Db()->table("img_config")->where("mark='houtai_logo'")->getColumn("img_url");
		if(empty($houtai_logo)){
			$houtai_logo = ASSET_PATH . "images/logo.png";
		}
		\core\Cache::setCache("houtai_logo",$houtai_logo,24*60*60);
	}
	$houtai_name = \core\Cache::getCache("houtai_name");
	if(empty($houtai_name)){
		$houtai_name = Db()->table("config")->where("name='houtai_name'")->getColumn("val");
		if(empty($houtai_name)){
			$houtai_name = "管理后台";
		}
		\core\Cache::setCache("houtai_name",$houtai_name,24*60*60);
	}
?>
<head>
    <meta charset="utf-8">
    <title><?php echo $houtai_name; ?></title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta http-equiv="Access-Control-Allow-Origin" content="*">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
	<meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache,no-store,must-revalidate,post-check=0,ptr-check=0">
	<meta http-equiv="expires" content="-1">
    <link rel="icon" href="<?php  echo $houtai_logo; ?>">
    <link rel="stylesheet" href="<?php echo ASSET_PATH; ?>lib/font-awesome-4.7.0/css/font-awesome.min.css" media="all">
    <link rel="stylesheet" href="<?php echo ASSET_PATH; ?>css/layuimini.css" media="all">
    <link rel="stylesheet" href="<?php echo ASSET_PATH; ?>css/themes/default.css" media="all">
    <link rel="stylesheet" href="<?php echo ASSET_PATH; ?>css/public.css" media="all">
    <link rel="stylesheet" href="<?php echo ASSET_PATH; ?>css/common.css" media="all">
    <link rel="stylesheet" href="<?php echo ASSET_PATH; ?>css/zoom.css" media="all">
    <link rel="stylesheet" href="<?php echo ASSET_PATH; ?>css/wangEditor.css" media="all">
	<link rel="stylesheet" href="<?php echo ASSET_PATH; ?>lib/layui-v2.8.18/css/layui.css" media="all">
	<script src="<?php echo ASSET_PATH; ?>lib/layui-v2.8.18/layui.js" charset="utf-8"></script>
	<script src="<?php echo ASSET_PATH; ?>js/lay-config.js" charset="utf-8"></script>
	<script src="<?php echo ASSET_PATH; ?>lib/jquery-3.4.1/jquery-3.4.1.min.js" charset="utf-8"></script>
	<script src="<?php echo ASSET_PATH; ?>js/transition.js" charset="utf-8"></script>
	<script src="<?php echo ASSET_PATH; ?>js/common.js" charset="utf-8"></script>
	<script src="<?php echo ASSET_PATH; ?>js/clipboard.min.js" charset="utf-8"></script>
	<script src="<?php echo ASSET_PATH; ?>js/zoom.js" charset="utf-8"></script>
	<script src="<?php echo ASSET_PATH; ?>js/wangEditor.js" charset="utf-8"></script>
	<script src="<?php echo ASSET_PATH; ?>js/china.js" charset="utf-8"></script>
    <style id="layuimini-bg-color">
    </style>
	<style>
	.layui-table-view .layui-table{
		width:100%;
	}	
	::-webkit-scrollbar {
		width: 8px;
		height: 8px;
	}
	.layui-nav .layui-nav-more{top:50%;}
	.layui-table-view{margin-top:5px;}
	</style>
    <!--[if lt IE 9]>
    <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
    <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body class="layui-layout-body layuimini-multi-module layuimini-all">
<div class="layui-layout layui-layout-admin">
    <div class="layui-header header">
        <div class="layui-logo layuimini-logo layuimini-back-home">
		<img src="<?php  echo $houtai_logo; ?>" alt="logo"><a href="<?php echo url("index/index"); ?>"><h1 style="font-family: 'Shojumaru', cursive, Arial, serif;"><?php echo $houtai_name; ?></h1></a>
		</div>
        <div class="layuimini-header-content">
            <div class="layuimini-tool"><i title="展开" class="fa fa-outdent" data-side-fold></i></div>
            <!--常用菜单-->
			<?php if(
						in_array("user.index",$_SESSION["root_info"]["privileges"]) ||
						in_array("goods.orders",$_SESSION["root_info"]["privileges"]) ||
						in_array("user.tixian_order",$_SESSION["root_info"]["privileges"]) ||
						in_array("sys.databak",$_SESSION["root_info"]["privileges"]) ||
						in_array("report.index",$_SESSION["root_info"]["privileges"])
				){
			?>
			<ul class="layui-nav layui-layout-left layuimini-header-menu">
				<li class="layui-nav-item top_caidan">
					<a style="cursor:pointer;"><i class="fa fa-star"></i>&nbsp;常用菜单<i class="layui-icon layui-icon-down layui-nav-more" style="top:0px;"></i></a>
					<dl class="layui-nav-child layui-anim layui-anim-upbit top_caidan_child">
						<?php if(in_array("user.index",$_SESSION["root_info"]["privileges"])){ ?><dd><a href="<?php echo url("user/index"); ?>"><span class="layui-left-nav"><i class="fa fa-user-circle"></i> 用户管理</span></a></dd><?php } ?>
						<?php if(in_array("goods.orders",$_SESSION["root_info"]["privileges"])){ ?><dd><a href="<?php echo url("goods/orders"); ?>"><span class="layui-left-nav"><i class="fa fa-file-text-o"></i> 商城订单</span></a></dd><?php } ?>
						<?php if(in_array("user.tixian_order",$_SESSION["root_info"]["privileges"])){ ?><dd><a href="<?php echo url("user/tixian_order"); ?>"><span class="layui-left-nav"><i class="fa fa-file-text-o"></i> 提现订单</span></a></dd><?php } ?>
						<?php if(in_array("report.index",$_SESSION["root_info"]["privileges"])){ ?><dd><a href="<?php echo url("report/index"); ?>"><span class="layui-left-nav"><i class="fa fa-flag"></i> 举报管理</span></a></dd><?php } ?>
						<?php if(in_array("sys.databak",$_SESSION["root_info"]["privileges"])){ ?><dd><a href="<?php echo url("sys/databak"); ?>"><span class="layui-left-nav"><i class="fa fa-database"></i> 数据备份</span></a></dd><?php } ?>
					</dl>
				</li>
				<span class="layui-nav-bar" style="left: 0px; top: 60px; width: 0px; opacity: 0;"></span>
			</ul>
			<?php } ?>
            <ul class="layui-nav layui-layout-right">
				<li class="layui-nav-item" lay-unselect>
					<a style="cursor:pointer;" id="toggle_rchat_div">
					  <i class="layui-icon layui-icon-notice"></i>  
					  <span class="layui-badge-dot" id="rchat_message_tips" style="display:none;"></span>
					</a>
				</li>

                <li class="layui-nav-item" lay-unselect>
                    <a href="javascript:window.location.reload();" data-refresh="刷新"><i class="fa fa-refresh"></i></a>
                </li>
                <li class="layui-nav-item mobile layui-hide-xs" lay-unselect>
                    <a href="javascript:;" data-check-screen="full"><i class="fa fa-arrows-alt"></i></a>
                </li>
                <li class="layui-nav-item layuimini-setting top_caidan">
                    <a href="javascript:;"><i class="fa fa-user-circle"></i> <?php echo $_SESSION["root_info"]["r_name"]; ?> <i class="layui-icon layui-icon-down layui-nav-more" style="top:0px;"></i></a>
                    <dl class="layui-nav-child top_caidan_child">
                        <dd><a href="<?php echo url("root/update_password"); ?>" ><i class="fa fa-edit"></i> 修改密码</a></dd>
                        <dd><hr></dd>
                        <dd><a href="<?php echo url("login/logout"); ?>" class="login-out"><i class="fa fa-sign-out"></i> 退出登录</a></dd>
                    </dl>
                </li>
                <li class="layui-nav-item layuimini-select-bgcolor" lay-unselect>
                    <a href="javascript:;" data-bgcolor="配色方案"><i class="fa fa-ellipsis-v"></i></a>
                </li>
            </ul>
        </div>
    </div>

    <!--无限极左侧菜单-->
    <div class="layui-side layui-bg-black layuimini-menu-left">
		<?php $privileges = strtolower(\core\Route::$controller.".".\core\Route::$action); ?>
		<ul class="layui-nav layui-nav-tree layui-this" id="multi_module_menu"> 
			<?php if(
						in_array("user.index",$_SESSION["root_info"]["privileges"]) ||
						in_array("user.chongzhi_order",$_SESSION["root_info"]["privileges"]) ||
						in_array("user.zhangdan_log",$_SESSION["root_info"]["privileges"]) ||
						in_array("user.log",$_SESSION["root_info"]["privileges"]) ||
						in_array("user.addr",$_SESSION["root_info"]["privileges"]) ||
						in_array("user.bank",$_SESSION["root_info"]["privileges"]) ||
						in_array("user.huiyuan_order",$_SESSION["root_info"]["privileges"]) ||
						in_array("user.chongzhi_config",$_SESSION["root_info"]["privileges"]) ||
						in_array("report.index",$_SESSION["root_info"]["privileges"])
				){
			?>
			<li class="layui-nav-item menu-li"> 
				<a href="javascript:;"> <i class="fa fa-user-circle"></i> <span class="layui-left-nav">用户管理</span><span class="layui-nav-more"></span></a> 
				<dl class="layui-nav-child">
					<?php if(in_array("user.index",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("user/index"); ?>"> <i class="fa fa-user-circle"></i> <span class="layui-left-nav"> 用户列表</span></a></dd><?php } ?>
					<?php if(in_array("user.chongzhi_order",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("user/chongzhi_order"); ?>"> <i class="fa fa-file-text-o"></i> <span class="layui-left-nav"> 充值订单</span></a></dd><?php } ?>
					<?php if(in_array("user.zhangdan_log",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("user/zhangdan_log"); ?>"> <i class="fa fa-file-text"></i> <span class="layui-left-nav"> 账单记录</span></a></dd><?php } ?>
					<?php if(in_array("user.addr",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("user/addr"); ?>"> <i class="fa fa-address-book"></i> <span class="layui-left-nav"> 收货地址</span></a></dd><?php } ?>
					<?php if(in_array("user.bank",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("user/bank"); ?>"> <i class="fa fa-credit-card-alt"></i> <span class="layui-left-nav"> 收款账户</span></a></dd><?php } ?>
					<?php if(in_array("user.log",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("user/log"); ?>"> <i class="fa fa-history"></i> <span class="layui-left-nav"> 操作记录</span></a></dd><?php } ?>
					<?php if(in_array("user.level",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("user/huiyuan_order"); ?>"> <i class="fa fa-cogs"></i> <span class="layui-left-nav"> 购买会员</span></a></dd><?php } ?>
					<?php if(in_array("user.chongzhi_config",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("user/chongzhi_config"); ?>"> <i class="fa fa-cogs"></i> <span class="layui-left-nav"> 充值配置</span></a></dd><?php } ?>
					<?php if(in_array("report.index",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("report/index"); ?>"> <i class="fa fa-flag"></i> <span class="layui-left-nav"> 举报管理</span></a></dd><?php } ?>
				</dl>
			</li>  
			<?php } ?>
			<?php if(
						in_array("goods.type",$_SESSION["root_info"]["privileges"]) || 
						in_array("goods.index",$_SESSION["root_info"]["privileges"]) || 
						in_array("goods.orders",$_SESSION["root_info"]["privileges"]) || 
						in_array("goods.pingjia",$_SESSION["root_info"]["privileges"])
				){
			?>
			<li class="layui-nav-item menu-li"> 
				<a href="javascript:;"> <i class="fa fa-shopping-bag"></i> <span class="layui-left-nav">商城管理</span><span class="layui-nav-more"></span></a> 
				<dl class="layui-nav-child"> 
					<?php if(in_array("goods.type",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("goods/type"); ?>"> <i class="fa fa-sitemap"></i> <span class="layui-left-nav"> 商品分类</span></a></dd><?php } ?> 
					<?php if(in_array("goods.index",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("goods/index"); ?>"> <i class="fa fa-list-ul"></i> <span class="layui-left-nav"> 商品列表</span></a></dd><?php } ?> 
					<?php if(in_array("goods.orders",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("goods/orders"); ?>"> <i class="fa fa-files-o"></i> <span class="layui-left-nav"> 订单管理</span></a></dd><?php } ?> 
					<?php if(in_array("goods.pingjia",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("goods/pingjia"); ?>"> <i class="fa fa-commenting-o"></i> <span class="layui-left-nav"> 评价列表</span></a></dd><?php } ?> 
				</dl> 
			</li>		
			<?php } ?>
			<?php if(
						in_array("huodong.type",$_SESSION["root_info"]["privileges"]) || 
						in_array("huodong.index",$_SESSION["root_info"]["privileges"]) || 
						in_array("huodong.orders",$_SESSION["root_info"]["privileges"]) || 
						in_array("huodong.pingjia",$_SESSION["root_info"]["privileges"]) || 
						in_array("huodong.zhongjiang_log",$_SESSION["root_info"]["privileges"])
				){
			?>
			<li class="layui-nav-item menu-li"> 
				<a href="javascript:;"> <i class="fa fa-calendar"></i> <span class="layui-left-nav">活动管理</span><span class="layui-nav-more"></span></a> 
				<dl class="layui-nav-child"> 
					<?php if(in_array("huodong.type",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("huodong/type"); ?>"> <i class="fa fa-sitemap"></i> <span class="layui-left-nav"> 分类管理</span></a></dd><?php } ?> 
					<?php if(in_array("huodong.index",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("huodong/index"); ?>"> <i class="fa fa-list"></i> <span class="layui-left-nav"> 活动列表</span></a></dd><?php } ?> 
					<?php if(in_array("huodong.orders",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("huodong/orders"); ?>"> <i class="fa fa-file-text-o"></i> <span class="layui-left-nav"> 报名订单</span></a></dd><?php } ?> 
					<?php if(in_array("huodong.pingjia",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("huodong/pingjia"); ?>"> <i class="fa fa-commenting-o"></i> <span class="layui-left-nav"> 评价列表</span></a></dd><?php } ?> 
					<?php if(in_array("huodong.zhongjiang_log",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("huodong/zhongjiang_log"); ?>"> <i class="fa fa-file-o"></i> <span class="layui-left-nav"> 中奖记录</span></a></dd><?php } ?> 
				</dl> 
			</li>
			<?php } ?>

			<?php if(
						in_array("community.cards",$_SESSION["root_info"]["privileges"]) ||
						in_array("community.feeds",$_SESSION["root_info"]["privileges"]) ||
						in_array("community.diaries",$_SESSION["root_info"]["privileges"]) ||
						in_array("community.quotes",$_SESSION["root_info"]["privileges"])
				){
			?>
			<!-- {{ AURA-X: Add - 添加社区管理菜单. Confirmed via 寸止 }} -->
			<li class="layui-nav-item menu-li">
				<a href="javascript:;"> <i class="fa fa-comments"></i> <span class="layui-left-nav">社区管理</span><span class="layui-nav-more"></span></a>
				<dl class="layui-nav-child">
					<?php if(in_array("community.cards",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("community/cards"); ?>"> <i class="fa fa-calendar-o"></i> <span class="layui-left-nav"> 日卡管理</span></a></dd><?php } ?>
					<?php if(in_array("community.feeds",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("community/feeds"); ?>"> <i class="fa fa-rss"></i> <span class="layui-left-nav"> 动态管理</span></a></dd><?php } ?>
					<?php if(in_array("community.diaries",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("community/diaries"); ?>"> <i class="fa fa-book"></i> <span class="layui-left-nav"> 日记管理</span></a></dd><?php } ?>
					<?php if(in_array("community.quotes",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("community/quotes"); ?>"> <i class="fa fa-quote-left"></i> <span class="layui-left-nav"> 摘录管理</span></a></dd><?php } ?>
				</dl>
			</li>
			<?php } ?>

			<?php if(
						in_array("branchpresident.applications",$_SESSION["root_info"]["privileges"]) ||
						in_array("branchpresident.branches",$_SESSION["root_info"]["privileges"])
				){
			?>
			<li class="layui-nav-item menu-li">
				<a href="javascript:;"> <i class="fa fa-users"></i> <span class="layui-left-nav">分会长管理</span><span class="layui-nav-more"></span></a>
				<dl class="layui-nav-child">
					<?php if(in_array("branchpresident.applications",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("branchpresident/applications"); ?>"> <i class="fa fa-file-text-o"></i> <span class="layui-left-nav"> 申请审核</span></a></dd><?php } ?>
					<?php if(in_array("branchpresident.branches",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("branchpresident/branches"); ?>"> <i class="fa fa-home"></i> <span class="layui-left-nav"> 分会管理</span></a></dd><?php } ?>
				</dl>
			</li>
			<?php } ?>
			<?php if(
						in_array("commission.index",$_SESSION["root_info"]["privileges"]) ||
						in_array("commission.records",$_SESSION["root_info"]["privileges"]) ||
						in_array("commission.config_management",$_SESSION["root_info"]["privileges"]) ||
						in_array("user.yongjin_log",$_SESSION["root_info"]["privileges"]) ||
						in_array("user.tixian_order",$_SESSION["root_info"]["privileges"])
				){
			?>
			<li class="layui-nav-item menu-li">
				<a href="javascript:;"> <i class="fa fa-rmb"></i> <span class="layui-left-nav">佣金管理</span><span class="layui-nav-more"></span></a>
				<dl class="layui-nav-child">
					<?php if(in_array("commission.index",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("commission/index"); ?>"> <i class="fa fa-dashboard"></i> <span class="layui-left-nav"> 佣金概览</span></a></dd><?php } ?>
					<?php if(in_array("commission.records",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("commission/records"); ?>"> <i class="fa fa-table"></i> <span class="layui-left-nav"> 运营佣金记录</span></a></dd><?php } ?>
					<?php if(in_array("commission.config_management",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("commission/config_management"); ?>"> <i class="fa fa-list-alt"></i> <span class="layui-left-nav"> 佣金配置管理</span></a></dd><?php } ?>
					<?php if(in_array("user.yongjin_log",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("user/yongjin_log"); ?>"> <i class="fa fa-file-text-o"></i> <span class="layui-left-nav"> 销售佣金记录</span></a></dd><?php } ?>
					<?php if(in_array("user.tixian_order",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("user/tixian_order"); ?>"> <i class="fa fa-money"></i> <span class="layui-left-nav"> 提现订单</span></a></dd><?php } ?>
				</dl>
			</li>
			<?php } ?>
			<?php if(
						in_array("root.index",$_SESSION["root_info"]["privileges"]) ||
						in_array("root.group_index",$_SESSION["root_info"]["privileges"]) ||
						in_array("root.log",$_SESSION["root_info"]["privileges"]) ||
						in_array("root.ip_index",$_SESSION["root_info"]["privileges"])
				){
			?>
			<li class="layui-nav-item menu-li"> 
				<a href="javascript:;"> <i class="fa fa-user"></i> <span class="layui-left-nav">管理员</span><span class="layui-nav-more"></span></a> 
				<dl class="layui-nav-child"> 
					<?php if(in_array("root.index",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("root/index"); ?>"> <i class="fa fa-user"></i> <span class="layui-left-nav"> 管理员</span></a></dd><?php } ?>
					<?php if(in_array("root.group_index",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("root/group_index"); ?>"> <i class="fa fa-users"></i> <span class="layui-left-nav"> 权限组</span></a></dd><?php } ?>
					<?php if(in_array("root.ip_index",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("root/ip_index"); ?>"> <i class="fa fa-globe"></i> <span class="layui-left-nav"> 登录IP</span></a></dd><?php } ?>
					<?php if(in_array("root.log",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("root/log"); ?>"> <i class="fa fa-history"></i> <span class="layui-left-nav"> 管理员记录</span></a></dd><?php } ?>
				</dl> 
			</li>		
			<?php } ?>
			<?php if(
						in_array("html.index",$_SESSION["root_info"]["privileges"]) || 
						in_array("smscode.index",$_SESSION["root_info"]["privileges"]) || 
						in_array("sys.config",$_SESSION["root_info"]["privileges"]) || 
						in_array("imgconfig.index",$_SESSION["root_info"]["privileges"]) || 
						in_array("lunbotu.index",$_SESSION["root_info"]["privileges"])
				){
			?>
			<li class="layui-nav-item menu-li"> 
				<a href="javascript:;"> <i class="fa fa-info-circle"></i> <span class="layui-left-nav">系统信息</span><span class="layui-nav-more"></span></a> 
				<dl class="layui-nav-child"> 
					<?php if(in_array("html.index",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("html/index"); ?>"> <i class="fa fa-file-word-o"></i> <span class="layui-left-nav"> 协议类</span></a></dd><?php } ?>
					<?php if(in_array("smscode.index",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("smscode/index"); ?>"> <i class="fa fa-envelope-o"></i> <span class="layui-left-nav"> 短信验证码</span></a></dd><?php } ?>
					<?php if(in_array("sys.config",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("sys/config"); ?>"> <i class="fa fa-cogs"></i> <span class="layui-left-nav"> 系统配置</span></a></dd><?php } ?>
					<?php if(in_array("imgconfig.index",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("imgconfig/index"); ?>"> <i class="fa fa-file-photo-o"></i> <span class="layui-left-nav"> 图片配置</span></a></dd><?php } ?>
					<?php if(in_array("lunbotu.index",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("lunbotu/index"); ?>"> <i class="fa fa-file-image-o"></i> <span class="layui-left-nav"> 轮播图</span></a></dd><?php } ?>
				</dl> 
			</li>	
			<?php } ?>
			<?php if(
						in_array("sys.databak",$_SESSION["root_info"]["privileges"]) || 
						in_array("sys.document",$_SESSION["root_info"]["privileges"]) || 
						in_array("sys.exception",$_SESSION["root_info"]["privileges"]) || 
						in_array("sys.sys_log",$_SESSION["root_info"]["privileges"]) || 
						in_array("sys.sys_env",$_SESSION["root_info"]["privileges"]) || 
						in_array("sys.access_log",$_SESSION["root_info"]["privileges"]) || 
						in_array("sys.daemon",$_SESSION["root_info"]["privileges"]) || 
						in_array("hardware.index",$_SESSION["root_info"]["privileges"]) || 
						in_array("sys.clear_cache",$_SESSION["root_info"]["privileges"]) || 
						in_array("root.flush_role",$_SESSION["root_info"]["privileges"])
				){
			?>			
			<li class="layui-nav-item menu-li"> 
				<a href="javascript:;"> <i class="fa fa-wrench"></i> <span class="layui-left-nav">数据维护</span><span class="layui-nav-more"></span></a> 
				<dl class="layui-nav-child"> 
					<?php if(in_array("sys.databak",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("sys/databak"); ?>"> <i class="fa fa-database"></i> <span class="layui-left-nav"> 数据库备份</span></a></dd><?php } ?>
					<?php if(in_array("sys.document",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("sys/document"); ?>"> <i class="fa fa-cloud"></i> <span class="layui-left-nav"> 文档管理</span></a></dd><?php } ?>
					<?php if(in_array("sys.exception",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("sys/exception"); ?>"> <i class="fa fa-exclamation-triangle"></i> <span class="layui-left-nav"> 系统异常</span></a></dd><?php } ?>
					<?php if(in_array("sys.sys_log",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("sys/sys_log"); ?>"> <i class="fa fa-history"></i> <span class="layui-left-nav"> 系统日志</span></a></dd><?php } ?>
					<?php if(in_array("sys.sys_env",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("sys/sys_env"); ?>"> <i class="fa fa-file-text"></i> <span class="layui-left-nav"> 服务器环境</span></a></dd><?php } ?>
					<?php if(in_array("sys.access_log",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("sys/access_log"); ?>"> <i class="fa fa-globe"></i> <span class="layui-left-nav"> 访问分析</span></a></dd><?php } ?>
					<?php if(in_array("sys.daemon",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("sys/daemon"); ?>"> <i class="fa fa-terminal"></i> <span class="layui-left-nav"> 守护进程</span></a></dd><?php } ?>
					<?php if(in_array("hardware.index",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("hardware/index"); ?>"> <i class="fa fa-server"></i> <span class="layui-left-nav"> 硬件信息</span></a></dd><?php } ?>
					<?php if(in_array("sys.clear_cache",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("sys/clear_cache"); ?>"> <i class="fa fa-recycle"></i> <span class="layui-left-nav"> 清除缓存</span></a></dd><?php } ?>
					<?php if(in_array("root.flush_role",$_SESSION["root_info"]["privileges"])){ ?><dd class="menu-dd"> <a href="<?php echo url("root/flush_role"); ?>"> <i class="fa fa-refresh"></i> <span class="layui-left-nav"> 刷新权限</span></a></dd><?php } ?>
				</dl> 
			</li>	
			<?php } ?>		
		</ul>
    </div>

    <!--初始化加载层-->
    <div class="layuimini-loader">
        <div class="layuimini-loader-inner"></div>
    </div>

    <!--手机端遮罩层-->
    <div class="layuimini-make"></div>

    <!-- 移动导航 -->
    <div class="layuimini-site-mobile"><i class="layui-icon"></i></div>

    <div class="layui-body">
		<?php if(isset($nav_ui) && is_array($nav_ui)){?>
        <div class="layui-card layuimini-page-header">
            <div class="layui-breadcrumb layuimini-page-title">
				<?php foreach($nav_ui as $nav_key=>$nav_sub){ ?>
					<?php if(is_array($nav_sub)){ ?>
						<a lay-href="" href="<?php echo isset($nav_sub['url']) ? $nav_sub['url'] : "#"; ?>"><?php echo isset($nav_sub['name']) ? $nav_sub['name'] : "-"; ?></a>
					<?php }else if(is_string($nav_sub)){ ?>
						<a><cite><?php echo $nav_sub; ?></cite></a>
					<?php } ?>
					<?php if($nav_key < count($nav_ui)-1)echo '<span lay-separator="">/</span>'; ?>
				<?php } ?>
            </div>
        </div>
		<?php } ?>
        <div class="layuimini-content-page">
			<div class="layuimini-container layuimini-page-anim">
				<div class="layuimini-main" style="padding-bottom:20px;">