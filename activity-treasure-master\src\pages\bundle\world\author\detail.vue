<script setup>
import { ref, onMounted } from 'vue';
import { getAuthorDetail } from '@/api/index.js';
import { store } from '@/store';
import customNavbar from '@/components/customNavbar.vue';

// {{ AURA-X: Add - 作者详情页面. Confirmed via 寸止 }}
const authorInfo = ref(null);
const loading = ref(true);
const error = ref('');

// 获取页面参数
const pages = getCurrentPages();
const currentPageInstance = pages[pages.length - 1];
const authorId = currentPageInstance.options.id;

// 获取作者详情
const fetchAuthorDetail = async () => {
  if (!authorId) {
    error.value = '作者ID无效';
    loading.value = false;
    return;
  }

  try {
    loading.value = true;
    const params = {
      uid: store().$state.userInfo?.uid || 0,
      token: store().$state.userInfo?.token || '',
      author_id: authorId
    };

    const response = await getAuthorDetail(params);
    
    if (response.status === 'ok') {
      authorInfo.value = response.data.author;
    } else {
      error.value = response.msg || '获取作者详情失败';
    }
  } catch (err) {
    console.error('获取作者详情失败:', err);
    error.value = '网络错误，请稍后重试';
  } finally {
    loading.value = false;
  }
};

// 格式化时间信息
const formatLifespan = (birthYear, deathYear) => {
  if (!birthYear && !deathYear) return '';
  if (birthYear && deathYear) return `${birthYear} - ${deathYear}`;
  if (birthYear && !deathYear) return `${birthYear} - 至今`;
  if (!birthYear && deathYear) return `? - ${deathYear}`;
  return '';
};

// 页面加载
onMounted(() => {
  fetchAuthorDetail();
});
</script>

<template>
  <view class="author-detail-page">
    <!-- 统一导航栏 -->
    <customNavbar 
      :title="authorInfo?.name || '作者详情'" 
      backIcon="arrow-left"
    />
    
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <u-loading-icon mode="spinner" color="#6AC086" size="40"></u-loading-icon>
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 错误状态 -->
    <view v-else-if="error" class="error-container">
      <u-icon name="error-circle" size="60" color="#ff4757"></u-icon>
      <text class="error-text">{{ error }}</text>
      <view class="retry-btn" @click="fetchAuthorDetail">
        <text>重试</text>
      </view>
    </view>
    
    <!-- 作者详情内容 -->
    <view v-else-if="authorInfo" class="detail-container">
      <!-- 作者基本信息 -->
      <view class="author-header">
        <view class="avatar-section">
          <image 
            v-if="authorInfo.avatar" 
            :src="authorInfo.avatar" 
            class="author-avatar"
            mode="aspectFill"
          />
          <view v-else class="default-avatar">
            <u-icon name="account-fill" size="60" color="#ccc"></u-icon>
          </view>
        </view>
        
        <view class="info-section">
          <view class="author-name">{{ authorInfo.name }}</view>
          <view v-if="authorInfo.category" class="author-category">{{ authorInfo.category }}</view>
          <view v-if="authorInfo.nationality" class="author-nationality">{{ authorInfo.nationality }}</view>
          <view v-if="formatLifespan(authorInfo.birth_year, authorInfo.death_year)" class="author-lifespan">
            {{ formatLifespan(authorInfo.birth_year, authorInfo.death_year) }}
          </view>
        </view>
      </view>
      
      <!-- 统计信息 -->
      <view class="stats-section">
        <view class="stat-item">
          <view class="stat-number">{{ authorInfo.quote_count || 0 }}</view>
          <view class="stat-label">摘录引用</view>
        </view>
      </view>
      
      <!-- 作者简介 -->
      <view v-if="authorInfo.description" class="description-section">
        <view class="section-title">简介</view>
        <view class="description-content">{{ authorInfo.description }}</view>
      </view>
      
      <!-- 详细信息 -->
      <view class="details-section">
        <view class="section-title">详细信息</view>
        
        <view v-if="authorInfo.birth_year" class="detail-item">
          <view class="detail-label">出生年份</view>
          <view class="detail-value">{{ authorInfo.birth_year }}</view>
        </view>
        
        <view v-if="authorInfo.death_year" class="detail-item">
          <view class="detail-label">逝世年份</view>
          <view class="detail-value">{{ authorInfo.death_year }}</view>
        </view>
        
        <view v-if="authorInfo.nationality" class="detail-item">
          <view class="detail-label">国籍</view>
          <view class="detail-value">{{ authorInfo.nationality }}</view>
        </view>
        
        <view v-if="authorInfo.category" class="detail-item">
          <view class="detail-label">类别</view>
          <view class="detail-value">{{ authorInfo.category }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.author-detail-page {
  min-height: 100vh;
  background-color: #f8f9fa;
  
  .loading-container, .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 32rpx;
    
    .loading-text, .error-text {
      margin-top: 24rpx;
      font-size: 28rpx;
      color: #666;
    }
    
    .retry-btn {
      margin-top: 32rpx;
      padding: 16rpx 32rpx;
      background-color: #6AC086;
      border-radius: 50rpx;
      color: white;
      font-size: 28rpx;
    }
  }
  
  .detail-container {
    padding: 32rpx;
    
    .author-header {
      background-color: white;
      border-radius: 20rpx;
      padding: 40rpx;
      margin-bottom: 32rpx;
      display: flex;
      align-items: center;
      
      .avatar-section {
        margin-right: 32rpx;
        
        .author-avatar {
          width: 120rpx;
          height: 120rpx;
          border-radius: 60rpx;
        }
        
        .default-avatar {
          width: 120rpx;
          height: 120rpx;
          border-radius: 60rpx;
          background-color: #f5f5f5;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      
      .info-section {
        flex: 1;
        
        .author-name {
          font-size: 36rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 12rpx;
        }
        
        .author-category {
          font-size: 28rpx;
          color: #6AC086;
          margin-bottom: 8rpx;
        }
        
        .author-nationality, .author-lifespan {
          font-size: 26rpx;
          color: #666;
          margin-bottom: 6rpx;
        }
      }
    }
    
    .stats-section {
      background-color: white;
      border-radius: 20rpx;
      padding: 32rpx;
      margin-bottom: 32rpx;
      display: flex;
      justify-content: center;
      
      .stat-item {
        text-align: center;
        
        .stat-number {
          font-size: 48rpx;
          font-weight: 600;
          color: #6AC086;
          margin-bottom: 8rpx;
        }
        
        .stat-label {
          font-size: 26rpx;
          color: #666;
        }
      }
    }
    
    .description-section, .details-section {
      background-color: white;
      border-radius: 20rpx;
      padding: 32rpx;
      margin-bottom: 32rpx;
      
      .section-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 24rpx;
      }
      
      .description-content {
        font-size: 30rpx;
        color: #666;
        line-height: 1.6;
      }
      
      .detail-item {
        display: flex;
        align-items: center;
        padding: 20rpx 0;
        border-bottom: 1rpx solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .detail-label {
          width: 160rpx;
          font-size: 30rpx;
          color: #666;
          flex-shrink: 0;
        }
        
        .detail-value {
          flex: 1;
          font-size: 30rpx;
          color: #333;
        }
      }
    }
  }
}
</style>
