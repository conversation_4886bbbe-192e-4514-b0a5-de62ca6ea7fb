<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { searchAuthors, createAuthor } from '@/api/index.js';
import { store } from '@/store';
import customNavbar from '@/components/customNavbar.vue';

// {{ AURA-X: Add - 创建作者搜索选择页面. Confirmed via 寸止 }}
const searchKeyword = ref('');
const authorList = ref([]);
const isLoading = ref(false);
const isLoadingMore = ref(false);
const hasMore = ref(true);
const currentPage = ref(1);
const pageSize = 20;

// 获取页面参数
const pages = getCurrentPages();
const currentPageInstance = pages[pages.length - 1];
const isSelectMode = currentPageInstance.options.type === 'select';

// 搜索作者
const searchAuthorList = async (isLoadMore = false) => {
  if (isLoading.value || (isLoadMore && isLoadingMore.value)) return;
  
  if (isLoadMore) {
    isLoadingMore.value = true;
  } else {
    isLoading.value = true;
    currentPage.value = 1;
    authorList.value = [];
    hasMore.value = true;
  }
  
  try {
    const response = await searchAuthors({
      uid: store.userInfo.uid,
      token: store.userInfo.token,
      keyword: searchKeyword.value,
      page: currentPage.value,
      page_size: pageSize
    });
    
    if (response.status === 'ok') {
      const newAuthors = response.data.authors || [];
      
      if (isLoadMore) {
        authorList.value = [...authorList.value, ...newAuthors];
      } else {
        authorList.value = newAuthors;
      }
      
      hasMore.value = newAuthors.length === pageSize;
      if (hasMore.value) {
        currentPage.value++;
      }
    } else {
      uni.showToast({ title: response.msg || '搜索失败', icon: 'none' });
    }
  } catch (error) {
    console.error('搜索作者失败:', error);
    uni.showToast({ title: '搜索失败，请稍后重试', icon: 'none' });
  } finally {
    isLoading.value = false;
    isLoadingMore.value = false;
  }
};

// 选择作者
const selectAuthor = (author) => {
  if (isSelectMode) {
    // 发送选择结果给父页面
    uni.$emit('authorSelected', author);
    uni.navigateBack();
  } else {
    // 跳转到作者详情页
    uni.navigateTo({
      url: `/pages/bundle/world/author/detail?id=${author.id}`
    });
  }
};

// 创建新作者
const createNewAuthor = () => {
  if (isSelectMode) {
    uni.navigateTo({
      url: `/pages/bundle/world/author/create?type=select&keyword=${encodeURIComponent(searchKeyword.value)}`
    });
  } else {
    uni.navigateTo({
      url: '/pages/bundle/world/author/create'
    });
  }
};

// 搜索防抖
let searchTimer = null;
const handleSearch = () => {
  clearTimeout(searchTimer);
  searchTimer = setTimeout(() => {
    searchAuthorList();
  }, 500);
};

// 加载更多
const loadMore = () => {
  if (hasMore.value && !isLoadingMore.value) {
    searchAuthorList(true);
  }
};

// 页面加载
onMounted(() => {
  searchAuthorList();
});

// 监听创建作者成功事件
uni.$on('authorCreated', (newAuthor) => {
  if (isSelectMode) {
    selectAuthor(newAuthor);
  } else {
    // 刷新列表
    searchAuthorList();
  }
});

// 组件销毁时移除事件监听
onUnmounted(() => {
  uni.$off('authorCreated');
});
</script>

<template>
  <view class="author-search-page">
    <!-- 统一导航栏 -->
    <customNavbar 
      :title="isSelectMode ? '选择作者' : '搜索作者'" 
      backIcon="arrow-left"
    />
    
    <!-- 搜索框 -->
    <view class="search-section">
      <view class="search-box">
        <u-icon name="search" size="20" color="#999" class="search-icon"></u-icon>
        <input 
          v-model="searchKeyword" 
          @input="handleSearch"
          placeholder="搜索作者姓名、类别..."
          class="search-input"
          confirm-type="search"
          @confirm="searchAuthorList"
        />
        <view v-if="searchKeyword" @click="searchKeyword = ''; searchAuthorList()" class="clear-btn">
          <u-icon name="close-circle-fill" size="18" color="#ccc"></u-icon>
        </view>
      </view>
    </view>
    
    <!-- 作者列表 -->
    <view class="author-list">
      <view v-if="isLoading && authorList.length === 0" class="loading-wrapper">
        <u-loading-icon mode="spinner" color="#6AC086" size="40"></u-loading-icon>
        <text class="loading-text">搜索中...</text>
      </view>
      
      <view v-else-if="authorList.length === 0 && !isLoading" class="empty-wrapper">
        <u-icon name="account" size="80" color="#ddd"></u-icon>
        <text class="empty-text">暂无相关作者</text>
        <view class="create-btn" @click="createNewAuthor">
          <u-icon name="plus" size="16" color="#6AC086" style="margin-right: 8rpx;"></u-icon>
          <text class="create-text">创建新作者</text>
        </view>
      </view>
      
      <view v-else>
        <view 
          v-for="author in authorList" 
          :key="author.id"
          class="author-item"
          @click="selectAuthor(author)"
        >
          <image 
            v-if="author.avatar" 
            :src="author.avatar" 
            class="avatar"
            mode="aspectFill"
          ></image>
          <view v-else class="avatar-placeholder">
            {{ author.name.charAt(0) }}
          </view>
          
          <view class="author-info">
            <text class="name">{{ author.name }}</text>
            <text v-if="author.category" class="category">{{ author.category }}</text>
            <text v-if="author.description" class="description">{{ author.description }}</text>
            <view class="stats">
              <text class="quote-count">{{ author.quote_count || 0 }} 条摘录</text>
            </view>
          </view>
          
          <u-icon name="arrow-right" size="16" color="#ccc" class="arrow"></u-icon>
        </view>
        
        <!-- 加载更多 -->
        <view v-if="hasMore" class="load-more" @click="loadMore">
          <u-loading-icon v-if="isLoadingMore" mode="spinner" color="#6AC086" size="20"></u-loading-icon>
          <text class="load-more-text">{{ isLoadingMore ? '加载中...' : '加载更多' }}</text>
        </view>
        
        <!-- 创建新作者按钮 -->
        <view class="create-new-section">
          <view class="create-btn" @click="createNewAuthor">
            <u-icon name="plus" size="16" color="#6AC086" style="margin-right: 8rpx;"></u-icon>
            <text class="create-text">创建新作者</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.author-search-page {
  min-height: 100vh;
  background-color: #f8f9fa;
  
  .search-section {
    padding: 32rpx;
    background-color: white;
    border-bottom: 1rpx solid #f0f0f0;
    
    .search-box {
      display: flex;
      align-items: center;
      background-color: #f8f9fa;
      border-radius: 50rpx;
      padding: 24rpx 32rpx;
      
      .search-icon {
        margin-right: 16rpx;
        flex-shrink: 0;
      }
      
      .search-input {
        flex: 1;
        font-size: 32rpx;
        color: #333;
        
        &::placeholder {
          color: #999;
        }
      }
      
      .clear-btn {
        margin-left: 16rpx;
        flex-shrink: 0;
      }
    }
  }
  
  .author-list {
    flex: 1;
    
    .loading-wrapper,
    .empty-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 120rpx 32rpx;
      
      .loading-text,
      .empty-text {
        margin-top: 32rpx;
        font-size: 28rpx;
        color: #999;
      }
      
      .create-btn {
        margin-top: 48rpx;
        display: flex;
        align-items: center;
        padding: 24rpx 48rpx;
        background-color: #6AC086;
        border-radius: 50rpx;
        
        .create-text {
          font-size: 28rpx;
          color: white;
          font-weight: 500;
        }
      }
    }
    
    .author-item {
      display: flex;
      align-items: center;
      padding: 32rpx;
      background-color: white;
      border-bottom: 1rpx solid #f0f0f0;
      min-height: 88rpx;
      
      &:active {
        background-color: #f8f9fa;
      }
      
      .avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-right: 32rpx;
        flex-shrink: 0;
      }
      
      .avatar-placeholder {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        background-color: #6AC086;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32rpx;
        font-weight: 500;
        margin-right: 32rpx;
        flex-shrink: 0;
      }
      
      .author-info {
        flex: 1;
        
        .name {
          display: block;
          font-size: 32rpx;
          color: #333;
          font-weight: 500;
          line-height: 1.4;
          margin-bottom: 8rpx;
        }
        
        .category {
          display: block;
          font-size: 24rpx;
          color: #6AC086;
          margin-bottom: 8rpx;
        }
        
        .description {
          display: block;
          font-size: 24rpx;
          color: #666;
          line-height: 1.4;
          margin-bottom: 8rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        
        .stats {
          .quote-count {
            font-size: 24rpx;
            color: #999;
          }
        }
      }
      
      .arrow {
        margin-left: 16rpx;
        flex-shrink: 0;
      }
    }
    
    .load-more {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 48rpx 32rpx;
      background-color: white;
      border-bottom: 1rpx solid #f0f0f0;
      
      .load-more-text {
        margin-left: 16rpx;
        font-size: 28rpx;
        color: #666;
      }
    }
    
    .create-new-section {
      padding: 48rpx 32rpx;
      background-color: white;
      display: flex;
      justify-content: center;
      
      .create-btn {
        display: flex;
        align-items: center;
        padding: 24rpx 48rpx;
        background-color: #6AC086;
        border-radius: 50rpx;
        
        .create-text {
          font-size: 28rpx;
          color: white;
          font-weight: 500;
        }
      }
    }
  }
}
</style>
