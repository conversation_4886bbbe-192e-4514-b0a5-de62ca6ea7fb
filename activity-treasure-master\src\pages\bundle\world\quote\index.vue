<script setup>
import { ref, onMounted, onActivated, onUnmounted } from 'vue';
// {{ AURA-X: Modify - 添加摘录评论和收藏功能API. Confirmed via 寸止 }}
import { getQuotes, likeQuote, favoriteQuote, getQuoteComments, postQuoteComment } from '@/api/index.js';
import { store } from '@/store';
import { navto } from '@/utils';
import { requireLogin } from '@/utils/auth';

// 状态管理
const quotes = ref([]);
const loading = ref(true);
const refreshing = ref(false);
const loadingMore = ref(false);
const hasMore = ref(true);
const currentPage = ref(1);
const pageSize = 20;

// 修复：添加摘录缓存机制，避免重复加载
const quoteCache = ref(new Map()); // 缓存摘录数据
const quoteCacheKey = 'quotes_list'; // 摘录缓存键

// 生成缓存键
const generateQuoteCacheKey = () => {
  const uid = store().$state.userInfo?.uid || 'anonymous';
  return `${quoteCacheKey}_user_${uid}`;
};

// 加载摘录列表 - 修复：添加缓存机制和调试信息
const loadQuotes = async (page = 1, isRefresh = false) => {
  try {
    console.log('QuoteIndex: 开始加载摘录数据', { page, isRefresh });

    if (isRefresh) {
      refreshing.value = true;
      currentPage.value = 1;
    } else if (page > 1) {
      loadingMore.value = true;
    } else {
      loading.value = true;

      // 检查缓存（仅第一页且非刷新时）
      if (page === 1 && !isRefresh) {
        const cacheKey = generateQuoteCacheKey();
        const cachedData = quoteCache.value.get(cacheKey);
        if (cachedData && cachedData.timestamp && (Date.now() - cachedData.timestamp < 5 * 60 * 1000)) {
          quotes.value = cachedData.data;
          currentPage.value = cachedData.page;
          hasMore.value = cachedData.hasMore;
          loading.value = false;
          console.log(`QuoteIndex: 从缓存恢复摘录数据，共 ${quotes.value.length} 条`);
          return;
        }
      }
    }

    const params = {
      page: page,
      page_size: pageSize,
      uid: store().$state.userInfo?.uid || 0,
      token: store().$state.userInfo?.token || ''
    };

    console.log('QuoteIndex: API请求参数', params);
    const res = await getQuotes(params);
    console.log('QuoteIndex: API响应结果', res);

    if (res.status === 'ok') {
      const newQuotes = res.data?.list || [];
      console.log('QuoteIndex: 获取到摘录数据', newQuotes.length, '条');

      if (isRefresh || page === 1) {
        quotes.value = newQuotes;

        // 缓存第一页数据
        if (page === 1) {
          const cacheKey = generateQuoteCacheKey();
          quoteCache.value.set(cacheKey, {
            data: [...newQuotes],
            page: page,
            hasMore: newQuotes.length === pageSize,
            timestamp: Date.now()
          });
          console.log(`QuoteIndex: 缓存摘录数据，共 ${newQuotes.length} 条`);
        }
      } else {
        quotes.value = [...quotes.value, ...newQuotes];
      }

      hasMore.value = newQuotes.length === pageSize;
      currentPage.value = page;
    } else if (res.status === 'empty') {
      console.log('QuoteIndex: 服务器返回空数据');
      if (isRefresh || page === 1) {
        quotes.value = [];
      }
      hasMore.value = false;
    } else {
      console.warn('QuoteIndex: API返回错误状态', res.status, res.msg);
      uni.showToast({ title: res.msg || '加载失败', icon: 'none' });
    }
  } catch (error) {
    console.error('QuoteIndex: 加载摘录失败:', error);
    uni.showToast({ title: '加载失败', icon: 'none' });
  } finally {
    loading.value = false;
    refreshing.value = false;
    loadingMore.value = false;
  }
};

// 下拉刷新
const onRefresh = () => {
  loadQuotes(1, true);
};

// 上拉加载更多
const onLoadMore = () => {
  if (!loadingMore.value && hasMore.value) {
    loadQuotes(currentPage.value + 1);
  }
};

// 查看摘录详情
const viewQuote = (quote) => {
  console.log('查看摘录:', quote);
  navto(`/pages/bundle/world/quote/detail?id=${quote.id}`);
};

// 点赞摘录
const handleLike = async (quote, event) => {
  event.stopPropagation(); // 阻止事件冒泡

  // 使用统一的登录校验
  if (!requireLogin('', '请先登录后再点赞')) {
    return;
  }

  try {
    const res = await likeQuote({
      id: quote.id,
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token
    });

    if (res.status === 'ok') {
      // 更新本地状态
      quote.is_liked = !quote.is_liked;
      quote.like_count = quote.is_liked ? (quote.like_count || 0) + 1 : (quote.like_count || 1) - 1;

      uni.showToast({
        title: quote.is_liked ? '点赞成功' : '取消点赞',
        icon: 'success'
      });
    } else {
      uni.showToast({ title: res.msg || '操作失败', icon: 'none' });
    }
  } catch (error) {
    console.error('点赞失败:', error);
    uni.showToast({ title: '网络错误，请稍后重试', icon: 'none' });
  }
};

// 收藏摘录
const handleFavorite = async (quote, event) => {
  event.stopPropagation(); // 阻止事件冒泡

  // 使用统一的登录校验
  if (!requireLogin('', '请先登录后再收藏')) {
    return;
  }

  try {
    const res = await favoriteQuote({
      id: quote.id,
      uid: store().$state.userInfo.uid,
      token: store().$state.userInfo.token
    });

    if (res.status === 'ok') {
      // 更新本地状态
      quote.is_favorited = !quote.is_favorited;
      quote.favorite_count = quote.is_favorited ? (quote.favorite_count || 0) + 1 : (quote.favorite_count || 1) - 1;

      uni.showToast({
        title: quote.is_favorited ? '收藏成功' : '取消收藏',
        icon: 'success'
      });
    } else {
      uni.showToast({ title: res.msg || '操作失败', icon: 'none' });
    }
  } catch (error) {
    console.error('收藏失败:', error);
    uni.showToast({ title: '网络错误，请稍后重试', icon: 'none' });
  }
};

// {{ AURA-X: Add - 添加摘录评论功能. Confirmed via 寸止 }}
// 评论摘录
const handleComment = (quote, event) => {
  event.stopPropagation(); // 阻止事件冒泡

  // 使用统一的登录校验
  if (!requireLogin('', '请先登录后再评论')) {
    return;
  }

  // 跳转到摘录详情页面，显示评论区
  navto(`/pages/bundle/world/quote/detail?id=${quote.id}&showComments=true`);
};

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '';

  // 修复iOS日期格式问题
  const formattedTimeStr = timeStr.replace(/-/g, '/');
  const time = new Date(formattedTimeStr);
  const now = new Date();
  const diff = now - time;

  // 1小时内显示xx分钟前
  if (diff < 3600000) { // 1小时 = 3600000毫秒
    const minutes = Math.floor(diff / 60000);
    return minutes <= 0 ? '刚刚' : `${minutes}分钟前`;
  }

  // 1天内显示xx小时前
  if (diff < 86400000) { // 1天 = 86400000毫秒
    const hours = Math.floor(diff / 3600000);
    return `${hours}小时前`;
  }

  // 超过1天显示具体日期
  const year = time.getFullYear();
  const month = String(time.getMonth() + 1).padStart(2, '0');
  const day = String(time.getDate()).padStart(2, '0');
  const hours = String(time.getHours()).padStart(2, '0');
  const minutes = String(time.getMinutes()).padStart(2, '0');

  // 判断是否是今年
  if (year === now.getFullYear()) {
    return `${month}-${day} ${hours}:${minutes}`;
  } else {
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }
};

// {{ AURA-X: Remove - 移除重复的onMounted，统一在下方处理. Confirmed via 寸止. }}

// {{ AURA-X: Add - 添加获取摘录图片和图片预览方法. Confirmed via 寸止 }}
// 获取摘录的第一张图片
const getQuoteImage = (quote) => {
  if (!quote.images) return null;

  try {
    // 如果images是字符串，尝试解析为JSON
    if (typeof quote.images === 'string') {
      const imageArray = JSON.parse(quote.images);
      return Array.isArray(imageArray) && imageArray.length > 0 ? imageArray[0] : null;
    }
    // 如果images已经是数组
    if (Array.isArray(quote.images)) {
      return quote.images.length > 0 ? quote.images[0] : null;
    }
  } catch (error) {
    console.warn('解析摘录图片失败:', error, quote.images);
  }

  return null;
};

// 预览图片
const previewImage = (imageUrl) => {
  uni.previewImage({
    urls: [imageUrl],
    current: imageUrl
  });
};

// {{ AURA-X: Add - 监听发布成功事件，自动刷新摘录列表. Confirmed via 寸止 }}
// 监听发布成功事件
const handleRefreshList = () => {
  console.log('QuoteIndex: 收到刷新事件，重新加载数据');
  loadQuotes(1, true);
};

// {{ AURA-X: Modify - 修改为按需加载，不在挂载时自动加载. Confirmed via 寸止. }}
// 组件挂载时不自动加载数据
onMounted(() => {
  console.log('QuoteIndex: 组件挂载，等待按需加载');

  // 监听发布成功事件
  uni.$on('refreshQuoteList', handleRefreshList);
});

// {{ AURA-X: Add - 添加外部调用接口，供父组件按需加载数据. Confirmed via 寸止. }}
/**
 * 外部调用接口：加载摘录数据
 * 供父组件在切换到摘录tab时调用
 */
const loadQuoteData = () => {
  console.log('QuoteIndex loadQuoteData - 被父组件调用');
  loadQuotes();
};

// 暴露给父组件的方法
defineExpose({
  loadQuoteData
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  uni.$off('refreshQuoteList', handleRefreshList);
});
</script>

<template>
  <view class="quote-container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <u-loading-icon mode="circle" size="30" color="#6AC086" />
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 摘录列表 -->
    <scroll-view 
      v-else
      class="quote-scroll"
      scroll-y
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="onLoadMore"
    >
      <!-- 空状态 -->
      <view v-if="quotes.length === 0" class="empty-container">
        <u-empty mode="list" text="还没有摘录哦" description="收藏美好的文字片段" />
      </view>

      <!-- {{ AURA-X: Modify - 从双列瀑布流改为单列布局 }} -->
      <!-- 单列摘录列表 -->
      <view v-else class="quote-list">
        <view
          v-for="quote in quotes"
          :key="quote.id"
          class="quote-card"
          @click="viewQuote(quote)"
        >
            <!-- 摘录内容 -->
            <view class="quote-content">
              <text class="quote-text">{{ quote.content }}</text>
            </view>

            <!-- {{ AURA-X: Add - 添加摘录图片显示功能. Confirmed via 寸止 }} -->
            <!-- 摘录图片 -->
            <view v-if="getQuoteImage(quote)" class="quote-image-container">
              <image
                :src="getQuoteImage(quote)"
                class="quote-image"
                mode="aspectFill"
                @click.stop="previewImage(getQuoteImage(quote))"
              ></image>
            </view>

            <!-- 作者和出处 -->
            <view v-if="quote.author || quote.source" class="quote-meta">
              <text v-if="quote.author" class="quote-author">— {{ quote.author }}</text>
              <text v-if="quote.source" class="quote-source">《{{ quote.source }}》</text>
            </view>

            <!-- 底部信息 -->
            <view class="quote-footer">
              <!-- 用户信息 -->
              <view class="user-info">
                <image
                  :src="quote.user?.avatar_url || '/static/default-avatar.png'"
                  class="user-avatar"
                  mode="aspectFill"
                ></image>
                <text class="user-nickname">{{ quote.user?.nickname || '匿名' }}</text>
              </view>

              <!-- 私密标识 -->
              <view v-if="quote.privacy === 'private'" class="privacy-badge">
                <u-icon name="lock" size="10" color="#999" />
              </view>
            </view>

            <!-- 时间和操作按钮 -->
            <view class="quote-bottom">
              <view class="quote-time">
                <text class="time-text">{{ formatTime(quote.created_at) }}</text>
              </view>

              <!-- 操作按钮 -->
              <view class="quote-actions">
                <view class="action-btn" @click="handleLike(quote, $event)">
                  <u-icon
                    :name="quote.is_liked ? 'heart-fill' : 'heart'"
                    :color="quote.is_liked ? '#ff4757' : '#999'"
                    size="16"
                  />
                  <text class="action-count">{{ quote.like_count || 0 }}</text>
                </view>

                <view class="action-btn" @click="handleFavorite(quote, $event)">
                  <u-icon
                    :name="quote.is_favorited ? 'star-fill' : 'star'"
                    :color="quote.is_favorited ? '#ffa502' : '#999'"
                    size="16"
                  />
                  <text class="action-count">{{ quote.favorite_count || 0 }}</text>
                </view>

                <!-- {{ AURA-X: Add - 添加摘录评论按钮. Confirmed via 寸止 }} -->
                <view class="action-btn" @click="handleComment(quote, $event)">
                  <u-icon
                    name="chat"
                    color="#999"
                    size="16"
                  />
                  <text class="action-count">{{ quote.comment_count || 0 }}</text>
                </view>
              </view>
            </view>
          </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="loadingMore" class="loading-more">
        <u-loading-icon mode="circle" size="20" color="#6AC086" />
        <text class="loading-more-text">加载更多...</text>
      </view>

      <!-- 没有更多 -->
      <view v-if="!hasMore && quotes.length > 0" class="no-more">
        <text class="no-more-text">没有更多了</text>
      </view>
    </scroll-view>
  </view>
</template>

<style lang="scss" scoped>
.quote-container {
  height: 100%;
  background-color: var(--color-background, #f8f9fa);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;

  .loading-text {
    margin-top: var(--spacing-md, 20rpx);
    font-size: var(--font-size-md, 28rpx);
    color: var(--color-text-secondary, #666);
  }
}

.quote-scroll {
  height: 100%;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}

/* {{ AURA-X: Modify - 从双列瀑布流改为单列布局样式 }} */
.quote-list {
  display: flex;
  flex-direction: column;
  padding: var(--spacing-md, 20rpx);
  gap: var(--spacing-md, 20rpx);
}

.quote-card {
  background: var(--color-surface, #ffffff);
  border-radius: var(--radius-md, 16rpx);
  padding: var(--spacing-lg, 24rpx);
  box-shadow: var(--shadow-sm, 0 2rpx 12rpx rgba(0, 0, 0, 0.05));
  border-left: 4rpx solid var(--color-primary, #6AC086);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  /* 扩大点击区域 */
  margin: 8rpx;
  min-height: 88rpx; /* 确保最小触摸区域 */
}

.quote-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(106, 192, 134, 0.15);
  background: #f8f9fa;
}

.quote-card:hover {
  box-shadow: 0 6rpx 20rpx rgba(106, 192, 134, 0.12);
}

.quote-content {
  margin-bottom: 20rpx;

  .quote-text {
    font-size: 30rpx;
    line-height: 1.8;
    color: #333;
    font-style: italic;
  }
}

// {{ AURA-X: Add - 添加摘录图片样式. Confirmed via 寸止 }}
.quote-image-container {
  margin-bottom: 20rpx;

  .quote-image {
    width: 100%;
    height: 300rpx;
    border-radius: 12rpx;
    background: #f5f5f5;
  }
}

.quote-meta {
  margin-bottom: 20rpx;
  
  .quote-author {
    display: block;
    font-size: 26rpx;
    color: #666;
    text-align: right;
    margin-bottom: 8rpx;
  }
  
  .quote-source {
    display: block;
    font-size: 24rpx;
    color: #999;
    text-align: right;
  }
}

.quote-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
  
  .user-info {
    display: flex;
    align-items: center;
    
    .user-avatar {
      width: 40rpx;
      height: 40rpx;
      border-radius: 50%;
      margin-right: 12rpx;
    }
    
    .user-nickname {
      font-size: 24rpx;
      color: #666;
    }
  }
  
  .privacy-badge {
    padding: 4rpx 8rpx;
    background: #f5f5f5;
    border-radius: 12rpx;
  }
}

.quote-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .quote-time {
    .time-text {
      font-size: 22rpx;
      color: #999;
    }
  }

  .quote-actions {
    display: flex;
    gap: 24rpx;

    .action-btn {
      display: flex;
      align-items: center;
      gap: 8rpx;
      padding: 8rpx;
      border-radius: 12rpx;
      transition: background-color 0.2s ease;

      &:active {
        background-color: #f5f5f5;
      }

      .action-count {
        font-size: 20rpx;
        color: #999;
        min-width: 20rpx;
      }
    }
  }
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  
  .loading-more-text {
    margin-left: 16rpx;
    font-size: 28rpx;
    color: #666;
  }
}

.no-more {
  display: flex;
  justify-content: center;
  padding: 40rpx;
  
  .no-more-text {
    font-size: 28rpx;
    color: #999;
  }
}
</style>
