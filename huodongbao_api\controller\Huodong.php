<?php

namespace controller;

use core\Controller;
use core\Db;

/*
 * @className 活动
*/

class <PERSON><PERSON><PERSON> extends Controller
{

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 记录活动相关错误日志
     * @param string $message 错误消息
     * @param array $business_data 业务相关数据
     * @param string $level 日志级别 (ERROR, WARNING, INFO)
     */

	private function logWriteError($message, $business_data = [], $level = 'ERROR') {
		// 使用正确的日志路径
		$log_file = '/home/<USER>/huodong_errors_' . date('Ymd') . '.log';

		try {
			// {{ AURA-X: Modify - 确保www用户可写的日志目录存在. Confirmed via 寸止. }}
			// 检查并创建目录
			$log_dir = dirname($log_file);
			if (!is_dir($log_dir)) {
				// 尝试创建目录，使用更宽松的权限
				if (!mkdir($log_dir, 0777, true)) {
					// 如果创建失败，尝试使用备用路径
					$log_file = '/tmp/huodong_errors_' . date('Ymd') . '.log';
					$log_dir = dirname($log_file);
				} else {
					// 创建成功后设置权限
					chmod($log_dir, 0777);
				}
			}

			// 检查并创建文件
			if (!file_exists($log_file)) {
				if (!touch($log_file)) {
					// 如果创建失败，使用备用路径
					$log_file = '/tmp/huodong_errors_' . date('Ymd') . '.log';
					touch($log_file);
				} else {
					// 创建成功后设置权限
					chmod($log_file, 0666);
				}
			}

			// {{ AURA-X: Add - 检测当前执行用户身份和权限诊断. Confirmed via 寸止. }}
			// 检测当前执行用户身份（在目录创建后）
			$user_diagnosis = [];

			if (function_exists('posix_geteuid')) {
				$euid = posix_geteuid();
				$egid = posix_getegid();
				$current_user_info = posix_getpwuid($euid);
				$current_group_info = posix_getgrgid($egid);

				$user_diagnosis = [
					'effective_uid' => $euid,
					'effective_gid' => $egid,
					'username' => $current_user_info['name'] ?? 'unknown',
					'home_dir' => $current_user_info['dir'] ?? 'unknown',
					'shell' => $current_user_info['shell'] ?? 'unknown',
					'group_name' => $current_group_info['name'] ?? 'unknown',
					'is_root' => ($euid === 0),
					'log_file_path' => $log_file,
					'log_dir_exists' => is_dir(dirname($log_file)),
					'log_dir_writable' => is_writable(dirname($log_file)),
					'log_file_exists' => file_exists($log_file),
					'log_file_writable' => file_exists($log_file) ? is_writable($log_file) : 'file_not_exists'
				];
			} else {
				$user_diagnosis['posix_functions'] = 'not_available';
			}

			// 构建日志条目
			$log_entry = [
				'timestamp' => date('Y-m-d H:i:s'),
				'level' => $level,
				'message' => $message,
				'business_data' => $business_data,
				'user_diagnosis' => $user_diagnosis,
				'server_info' => [
					'php_version' => PHP_VERSION,
					'server_time' => date('c'),
					'memory_usage' => memory_get_usage(true),
					'request_uri' => $_SERVER['REQUEST_URI'] ?? 'CLI',
					'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
				]
			];

			// 格式化为JSON
			$json_log = json_encode($log_entry, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n" . str_repeat('-', 80) . "\n";

			// 直接写入，与test_log_simple.php保持一致
			$write_result = file_put_contents($log_file, $json_log, FILE_APPEND | LOCK_EX);

			if ($write_result === false) {
				// {{ AURA-X: Remove - 移除exception_log调用避免循环. Confirmed via 寸止. }}
				// 写入失败，但不再调用exception_log避免循环
			}

		} catch (\Throwable $e) {
			// {{ AURA-X: Modify - 异常时记录权限诊断信息. Confirmed via 寸止. }}
			try {
				// {{ AURA-X: Remove - 移除exception_log调用避免循环. Confirmed via 寸止. }}
				// 记录异常但不调用exception_log避免循环
			} catch (\Throwable $e2) {
				// 最后的备用方案：写入到临时文件
				$temp_file = sys_get_temp_dir() . '/huodong_errors_backup_' . date('Ymd') . '.log';
				$backup_content = $json_log ?? "Huodong logWriteError异常: " . $e->getMessage();
				if (isset($user_diagnosis)) {
					$backup_content .= " | 权限诊断: " . json_encode($user_diagnosis, JSON_UNESCAPED_UNICODE);
				}
				@file_put_contents($temp_file, $backup_content, FILE_APPEND | LOCK_EX);
			}
		}
	}

	// {{ AURA-X: Add - 日期格式化方法，处理各种日期格式. Confirmed via 寸止. }}
	private function formatDateTime($dateTime) {
		if (empty($dateTime)) {
			return date('Y-m-d H:i:s');
		}

		// 尝试直接解析
		$timestamp = strtotime($dateTime);
		if ($timestamp !== false) {
			return date('Y-m-d H:i:s', $timestamp);
		}

		// 如果strtotime失败，尝试使用DateTime类
		try {
			$dt = new \DateTime($dateTime);
			return $dt->format('Y-m-d H:i:s');
		} catch (\Exception $e) {
			// 如果都失败了，返回当前时间
			return date('Y-m-d H:i:s');
		}
	}

    /**
     * 根据代码获取地区名称（支持高德adcode和旧格式）
     * @param string $code 地区代码
     * @param string $level 级别：province/city/district
     * @return string 地区名称
     */
    private function getAreaNameByCode($code, $level) {
        if (empty($code)) {
            return '';
        }

        try {
            dbConn();

            // {{ AURA-X: Modify - 只使用高德数据表，移除旧china表回退逻辑. Confirmed via 寸止. }}
            // 直接从高德数据表查询
            $result = Db()->_fetch("SELECT name FROM gaode_district WHERE adcode = :code AND level = :level", [
                ":code" => $code,
                ":level" => $level
            ]);

            return $result ? $result['name'] : '';
        } catch (\Throwable $e) {
            error_log("获取地区名称失败: " . $e->getMessage());
            return '';
        }
    }

    /**
     * 统一错误处理方法
     */
    private function handleError($message, $logMessage = null)
    {
        if ($logMessage) {
            $this->exception_log($logMessage);
        }
        return ["status" => "error", "msg" => $message];
    }

    /**
     * 验证位置数据的公共方法
     * @param int $is_online 是否线上活动
     * @param int $sheng_id 省份ID
     * @param int $shi_id 城市ID
     * @param int $qu_id 区县ID
     * @param string $addr 详细地址
     * @param float $lng 经度
     * @param float $lat 纬度
     * @return array 验证结果
     */
    private function validateLocationData($is_online, $sheng_id, $shi_id, $qu_id, $addr, $lng, $lat)
    {
        // 初始化位置变量
        $sheng = '';
        $shi = '';
        $qu = '';
        $sheng_id = (int)$sheng_id;
        $shi_id = (int)$shi_id;
        $qu_id = (int)$qu_id;

        if ($is_online == 0) {
            // 线下活动需要验证位置信息
            if (
                empty($sheng_id) || !check($sheng_id, "intgt0") ||
                empty($shi_id) || !check($shi_id, "intgt0") ||
                empty($qu_id) || !check($qu_id, "intgt0") ||
                empty($addr) ||
                empty($lng) || !is_numeric($lng) || $lng <= 0 ||
                empty($lat) || !is_numeric($lat) || $lat <= 0
            ) {
                return $this->handleError("参数错误(位置)");
            }

            // 获取地区信息（支持高德adcode和旧格式）
            $sheng = $this->getAreaNameByCode($sheng_id, 'province');
            if (empty($sheng)) {
                return $this->handleError("获取省份信息失败");
            }

            $shi = $this->getAreaNameByCode($shi_id, 'city');
            if (empty($shi)) {
                return $this->handleError("获取市信息失败");
            }

            $qu = $this->getAreaNameByCode($qu_id, 'district');
            if (empty($qu)) {
                return $this->handleError("获取县区信息失败");
            }
        } else {
            // 线上活动设置默认值
            $sheng_id = 0;
            $shi_id = 0;
            $qu_id = 0;
            $addr = '';
            $lng = 0;
            $lat = 0;
        }

        return [
            "status" => "ok",
            "data" => [
                "sheng" => $sheng,
                "shi" => $shi,
                "qu" => $qu,
                "sheng_id" => $sheng_id,
                "shi_id" => $shi_id,
                "qu_id" => $qu_id,
                "addr" => $addr,
                "lng" => $lng,
                "lat" => $lat
            ]
        ];
    }

    /*
	* @apiName 活动分类
	* @method get_type
	* @GET
	* @return {"status":"ok","data":[{"id":1,"name":"分类1","icon":"http:\/\/************\/test.jpg"},{"id":2,"name":"分类2","icon":"http:\/\/************\/test.jpg"},{"id":3,"name":"分类3","icon":"http:\/\/************\/test.jpg"}]}
	*/
    public function get_type()
    {
        try {
            $data = \core\Cache::getCache("huodong_get_type");
            if (!empty($data)) return ["status" => "ok", "data" => $data];
            dbConn();
            $where = "1";
            $data = Db()->table("huodong_type")->where($where)->fetchAll();
            foreach ($data as &$item) {
                $item['icon'] = strip_tags($item['icon']); // 去除 HTML 标签
            }
            if (empty($data)) {
                return ["status" => "empty"];
            } else {
                //\core\Cache::getCache("huodong_get_type",$data,600);
                return ["status" => "ok", "data" => $data];
            }
        } catch(\Throwable $e) {
            // 记录详细的错误日志
            $this->logWriteError("获取活动分类失败", [
                'exception_message' => $e->getMessage(),
                'exception_trace' => $e->getTraceAsString()
            ]);
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
	* @apiName 添加活动信息
	* @method add_huodong
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param name string 名称
	* @param title string 标题
	* @param type_id string 分类编号
	* @param img_url string 列表图
	* @param money string 费用
	* @param pay_type string 收费方式:1=线上,2=线下
	* @param contents string 介绍
	* @param liucheng json 流程json['111','222']
	* @param guige string 规格
	* @param num string 人数
	* @param baoming_start_time string 报名开始时间
	* @param baoming_end_time string 报名结束时间
	* @param start_time string 活动开始时间
	* @param end_time string 活动结束时间
	* @param sheng_id string 省编号
	* @param shi_id string 市编号
	* @param qu_id string 县区编号
	* @param addr string 具体位置
	* @param lng string 地图坐标经度
	* @param lat string 地图坐标纬度
	* @param yongjin_bili string 邀请佣金比例，5即5%
	* @param imgs_url string 图片集逗号隔开
	* @param is_choujiang string 是否抽奖:1=是,0=否
	* @param choujiang_huiyuan_num string 抽奖会员需达到数量
	* @param choujiang_zhongjiang_num string 抽奖中奖人数
	* @param choujiang_zhekou string 中奖折扣:0-99,0=免单,5=0.5折,60=6折
	* @param lianxi_name string 联系人姓名
	* @param lianxi_mobile string 联系人电话
	* @param lianxi_qrcode string 联系客服微信二维码地址
	* @param is_online string 是否线上:1=是,0=否
	* @return {"status":"ok","msg":"\u64cd\u4f5c\u6210\u529f"}
	*/
    public function add_huodong(
        $uid,
        $token,
        $name,
        $title,
        $type_id,
        $img_url,
        $money,
        $contents,
        $num,
        $start_time,
        $sheng_id,
        $shi_id,
        $qu_id,
        $addr,
        $lng,
        $lat,
        $guige = "",
        $imgs_url = "",
        $is_choujiang = 0,
        $choujiang_huiyuan_num = 1,
        $choujiang_zhongjiang_num = 1,
        $choujiang_zhekou = 0,
        $lianxi_name = "",
        $lianxi_mobile = "",
        $lianxi_qrcode = "",
        $qun_qrcode = "",
        $pay_type = 1,
        $is_online = 0,
        $member_money = 0.00,
        $yongjin_bili = 0,  // 添加佣金比例参数，默认为0（不使用佣金功能）
        $member_only = 0,   // 新增：是否仅会员可参加，0=否，1=是
        $baoming_end_time = "",
        $refund_rule = 1,
        $end_time = "",     // 活动结束时间
        $liucheng = "",     // 活动流程
        $imgs = "",         // 活动图片（备用字段）
        $enable_checkin = 0 // {{ AURA-X: Add - 是否启用签到功能. Confirmed via 寸止. }}
    )
    {

        $this->logWriteError("add_huodong方法开始执行", [
            'received_params' => [
                'uid' => $uid,
                'token' => $token ? substr($token, 0, 8) . '...' : 'empty',
                'name' => $name,
                'title' => $title,
                'type_id' => $type_id,
                'img_url' => $img_url,
                'money' => $money,
                'member_money' => $member_money,
                'contents' => substr($contents, 0, 50) . '...',
                'num' => $num,
                'start_time' => $start_time,
                'end_time' => $end_time,
                'lianxi_name' => $lianxi_name,
                'lianxi_qrcode' => $lianxi_qrcode,
                'qun_qrcode' => $qun_qrcode,
                'baoming_end_time' => $baoming_end_time,
                'is_online' => $is_online,
                'member_only' => $member_only,
                'refund_rule' => $refund_rule
            ],
            'all_post_data' => $_POST,
            'all_route_params' => \core\Route::$params
        ], 'INFO');


        // 基础参数验证
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32 ||
            empty($name) ||
            empty($title) ||
            empty($img_url) ||
            !check($img_url, "url") ||
            !is_numeric($money) ||
            !is_numeric($member_money) ||
            empty($contents) ||
            empty($num) ||
            !check($num, "intgt0") ||
            empty($type_id) ||
            !check($type_id, "intgt0") ||
            empty($start_time) ||
            !check($start_time, "datetime") ||
            empty($lianxi_name) ||
            empty($lianxi_qrcode) || !check($lianxi_qrcode, "url") ||
            empty($qun_qrcode) || !check($qun_qrcode, "url") ||
            !in_array($is_online, [0, 1])
        ) {
            // 记录活动添加失败详细日志
            $this->logWriteError("添加活动失败", [
                'uid' => $uid,
                'name' => $name,
                'title' => $title,
                'type_id' => $type_id,
                'money' => $money,
                'num' => $num,
                'start_time' => $start_time
            ]);
                        $this->exception_log("系统繁忙，请稍后再试");


            return $this->handleError("系统繁忙，请稍后再试");
        }
         $this->logWriteError("添加活动失败", [
                'uid' => $uid,
                'name' => $name,
                'title' => $title,
                'type_id' => $type_id,
                'money' => $money,
                'num' => $num,
                'start_time' => $start_time
            ]);
                    $this->exception_log("系统繁忙，请稍后再试");

        // 参数类型转换
        $is_online = (int)$is_online;
        $member_only = (int)$member_only;
        $refund_rule = (int)$refund_rule;
        $sheng = '';
        $shi = '';
        $qu = '';
        $sheng_id = (int)$sheng_id;
        $shi_id = (int)$shi_id;
        $qu_id = (int)$qu_id;

        // Location specific checks and lookups for offline events
        $locationResult = $this->validateLocationData($is_online, $sheng_id, $shi_id, $qu_id, $addr, $lng, $lat);
        if ($locationResult['status'] !== 'ok') {
            return $locationResult;
        }

        // Extract validated location data
        $sheng = $locationResult['data']['sheng'];
        $shi = $locationResult['data']['shi'];
        $qu = $locationResult['data']['qu'];
        $sheng_id = $locationResult['data']['sheng_id'];
        $shi_id = $locationResult['data']['shi_id'];
        $qu_id = $locationResult['data']['qu_id'];
        $addr = $locationResult['data']['addr'];
        $lng = $locationResult['data']['lng'];
        $lat = $locationResult['data']['lat'];

        $imgs_arr = [];
        if (!empty($imgs_url)) {
            $imgs_arr = explode(",", $imgs_url);
            foreach ($imgs_arr as $img) {
                if (!check($img, "url")) {
                    return $this->handleError("参数错误");
                }
            }
        }
        $uid = (int)$uid;
        $pay_type = (int)$pay_type;
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        // Check user role type
        $user_role_type = Db()->table("user")->where("uid={$uid}")->getColumn("role_type");
        if ($user_role_type !== '0') {
            $money = 0.00;        // Force fees to 0 for non-admin users
            $member_money = 0.00;
        }

        // 根据用户角色设置member_only默认值
        // 可操作角色：管理员(0)、场地与活动第三方(3)、城市分会长(4)、场地与活动第三方-不负责分会运营(5)
        $can_set_member_only = in_array($user_role_type, ['0', '3', '4', '5']);
        if (!$can_set_member_only) {
            $member_only = 1; // 其他角色默认设置为"仅会员可参加"
        }
        $member_only = (int)$member_only;

        // 用户角色说明：
        // role_type = 0: 管理员
        // role_type = 1: 分会长
        // role_type = 2: 普通用户
        //
        dbConn();
        $user_info = Db()->table("user")->select("uid,mobile")->where("uid={$uid}")->fetch();


        $data = [
            "uid" => $uid,
            "money" => number_format($money, 2, ".", ""),
            "member_money" => number_format($member_money, 2, ".", ""),
            "lng" => number_format($lng, 6, ".", ""),
            "lat" => number_format($lat, 6, ".", ""),
            "type_id" => intval($type_id),
            "num" => intval($num),
            "sheng_id" => $sheng_id, // {{ AURA-X: Fix - 支持adcode格式，不再强制转换为整数. Confirmed via 寸止. }}
            "shi_id" => $shi_id,
            "qu_id" => $qu_id,
            "name" => ":name",
            "title" => ":title",
            "img_url" => ":img_url",
            "contents" => ":contents",
            "addr" => ":addr",
            "guige" => ":guige",
            "sheng" => ":sheng",
            "shi" => ":shi",
            "qu" => ":qu",
            "lianxi_name" => ":lianxi_name",
            "lianxi_mobile" => ":lianxi_mobile",
            "lianxi_qrcode" => ":lianxi_qrcode",
            "qun_qrcode" => ":qun_qrcode",

            "start_time" => $this->formatDateTime($start_time),
            "end_time" => $this->formatDateTime($start_time),
            "baoming_end_time" => !empty($baoming_end_time) ? $this->formatDateTime($baoming_end_time) : $this->formatDateTime($start_time),
            // 根据用户角色设置审核状态：
            // 管理员(role_type=0): 直接通过审核 status=1
            // 分会长(role_type=1): 直接通过审核 status=1
            // 普通用户(role_type=2): 需要审核 status=0
            "status" => ($user_role_type == 2) ? 1 : 1, //测试写死生产要改成($user_role_type == 2) ? 0 : 1
            "is_online" => (int)$is_online,
            "member_only" => $member_only,
            "refund_rule" => (int)$refund_rule, //退款规则
            "enable_checkin" => (int)$enable_checkin // {{ AURA-X: Add - 添加签到功能开关. Confirmed via 寸止. }}
        ];

        // 时间验证逻辑已移除，如需要可重新添加
        $check_type = Db()->table("huodong_type")->where("id={$data['type_id']}")->fetch();
        if (empty($check_type)) {
            return $this->handleError("活动分类不存在");
        }

        $prepareParam = [
            ":name" => htmlspecialchars(trim($name)),
            ":title" => htmlspecialchars(trim($title)),
            ":img_url" => htmlspecialchars(trim($img_url)),
            ":contents" => htmlspecialchars(trim($contents)),
            ":addr" => htmlspecialchars(trim($addr)),
            ":guige" => htmlspecialchars(trim($guige)),
            ":sheng" => htmlspecialchars(trim($sheng)),
            ":shi" => htmlspecialchars(trim($shi)),
            ":qu" => htmlspecialchars(trim($qu)),
            ":lianxi_name" => htmlspecialchars(trim($lianxi_name)),
            ":lianxi_mobile" => htmlspecialchars(trim($lianxi_mobile)),
            ":lianxi_qrcode" => htmlspecialchars(trim($lianxi_qrcode)),
            ":qun_qrcode" => htmlspecialchars(trim($qun_qrcode)),
//            ":liucheng" => json_encode(json_decode(stripslashes(trim($liucheng, '"')), true), JSON_UNESCAPED_UNICODE),
        ];
        dbConn();
        try {
            Db()->table("huodong")->prepareParam($prepareParam)->insert($data);
            $insert_id = Db()->insertId();
            if (!empty($imgs_arr)) {
                foreach ($imgs_arr as $img) {
                    Db()->table("huodong_img")->prepareParam([":img_url" => $img])->insert(["huodong_id" => $insert_id, "img_url" => ":img_url"]);
                }
            }
            //
            return ["status" => "ok", "msg" => "操作成功,审核中"];
        } catch (\Throwable $e) {
            // 记录活动添加失败详细日志
            $this->logWriteError("添加活动失败", [
                'uid' => $uid,
                'name' => $name,
                'title' => $title,
                'type_id' => $type_id,
                'money' => $money,
                'num' => $num,
                'start_time' => $start_time,
                'exception_message' => $e->getMessage(),
                'exception_trace' => $e->getTraceAsString()
            ]);

            return $this->handleError("系统繁忙，请稍后再试", $e->getMessage());
        }
    }

    /*
	* @apiName 修改活动信息
	* @method update_huodong
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param huodong_id string 活动编号
	* @param name string 名称
	* @param title string 标题
	* @param type_id string 分类编号
	* @param img_url string 列表图
	* @param money string 费用
	* @param pay_type string 收费方式:1=线上,2=线下
	* @param contents string 介绍
	* @param liucheng json 流程json
	* @param guige string 规格
	* @param num string 人数
	* @param baoming_start_time string 报名开始时间
	* @param baoming_end_time string 报名结束时间
	* @param start_time string 活动开始时间
	* @param end_time string 活动结束时间
	* @param sheng_id string 省编号
	* @param shi_id string 市编号
	* @param qu_id string 县区编号
	* @param addr string 具体位置
	* @param lng string 地图坐标经度
	* @param lat string 地图坐标纬度
	* @param yongjin_bili string 邀请佣金比例，5即5%
	* @param imgs_url string 图片集逗号隔开
	* @param is_choujiang string 是否抽奖:1=是,0=否
	* @param choujiang_huiyuan_num string 抽奖会员需达到数量
	* @param choujiang_zhongjiang_num string 抽奖中奖人数
	* @param choujiang_zhekou string 中奖折扣:0-99,0=免单,5=0.5折,60=6折
	* @param lianxi_name string 联系人姓名
	* @param lianxi_mobile string 联系人电话
	* @param lianxi_qrcode string 联系客服微信二维码地址
	* @param is_online string 是否线上:1=是,0=否
	* @return {"status":"ok","msg":"\u64cd\u4f5c\u6210\u529f"}
	*/
    public function update_huodong(
        $uid,
        $token,
        $huodong_id,
        $name,
        $title,
        $type_id,
        $img_url,
        $money,
        $contents,
        $liucheng,
        $num,
        $start_time,
        $sheng_id,
        $shi_id,
        $qu_id,
        $addr,
        $lng,
        $lat,
        $yongjin_bili,
        $guige = "",
        $imgs_url = "",
        $is_choujiang = 0,
        $choujiang_huiyuan_num = 1,
        $choujiang_zhongjiang_num = 1,
        $choujiang_zhekou = 0,
        $lianxi_name = "",
        $lianxi_mobile = "",
        $lianxi_qrcode = "",
        $pay_type = 1,
        $is_online = 0,
        $member_money = 0.00,
        $member_only = 0,   // 新增：是否仅会员可参加，0=否，1=是
        $baoming_end_time = "",
        $refund_rule = 1
    )
    {
        $is_online = (int)$is_online; // Cast to integer

        // Basic parameter checks (non-location)
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32 ||
            empty($huodong_id) || !check($huodong_id, "intgt0") ||
            empty($name) ||
            empty($title) ||
            empty($img_url) ||
            !check($img_url, "url") ||
            !is_numeric($money) ||
            !is_numeric($member_money) ||
            empty($contents) ||
            empty($num) ||
            !check($num, "intgt0") ||
            empty($type_id) ||
            !check($type_id, "intgt0") ||
            empty($start_time) ||
            !check($start_time, "datetime") ||
            empty($lianxi_name) ||
            empty($lianxi_qrcode) || !check($lianxi_qrcode, "url") ||
            !in_array($is_online, [0, 1])
        ) return ["status" => "error", "msg" => "参数错误(基础)"];

        // Initialize location variables
        $sheng = '';
        $shi = '';
        $qu = '';
        $sheng_id = (int)$sheng_id;
        $shi_id = (int)$shi_id;
        $qu_id = (int)$qu_id;


        // Location specific checks and lookups for offline events
        $locationResult = $this->validateLocationData($is_online, $sheng_id, $shi_id, $qu_id, $addr, $lng, $lat);
        if ($locationResult['status'] !== 'ok') {
            return $locationResult;
        }

        // Extract validated location data
        $sheng = $locationResult['data']['sheng'];
        $shi = $locationResult['data']['shi'];
        $qu = $locationResult['data']['qu'];
        $sheng_id = $locationResult['data']['sheng_id'];
        $shi_id = $locationResult['data']['shi_id'];
        $qu_id = $locationResult['data']['qu_id'];
        $addr = $locationResult['data']['addr'];
        $lng = $locationResult['data']['lng'];
        $lat = $locationResult['data']['lat'];

        //
        $imgs_arr = [];
        if (!empty($imgs_url)) {
            $imgs_arr = explode(",", $imgs_url);
            foreach ($imgs_arr as $img) {
                if (!check($img, "url")) {
                    return ["status" => "error", "msg" => "参数错误"];
                }
            }
        }
        //
        $uid = (int)$uid;
        $pay_type = (int)$pay_type;
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        // Check user role type
        $user_role_type = Db()->table("user")->where("uid={$uid}")->getColumn("role_type");
        if ($user_role_type !== '0') {
            $money = 0.00;        // Force fees to 0 for non-admin users
            $member_money = 0.00;
        }

        // 根据用户角色设置member_only默认值
        // 可操作角色：管理员(0)、场地与活动第三方(3)、城市分会长(4)、场地与活动第三方-不负责分会运营(5)
        $can_set_member_only = in_array($user_role_type, ['0', '3', '4', '5']);
        if (!$can_set_member_only) {
            $member_only = 1; // 其他角色默认设置为"仅会员可参加"
        }
        $member_only = (int)$member_only;

        $huodong_id = (int)$huodong_id;
        dbConn();
        //
        $huodong_info = Db()->table("huodong")->select("id,baoming_num,status")->where("id={$huodong_id} AND uid={$uid}")->fetch();
        if (empty($huodong_info)) {
            return ["status" => "error", "msg" => "活动信息不存在"];
        }
        if ($huodong_info['baoming_num'] > 0) {
            return ["status" => "error", "msg" => "活动信息已有人报名，不支持修改"];
        }
        /*
		if($huodong_info['status'] != 0){
			return ["status"=>"error","msg"=>"活动信息已不支持修改"];
		}
		*/
        $config = $this->get_config(["huodong_choujiang_start_time", "huodong_jiesuan_days"]);
        if (
            empty($config) ||
            !isset($config['huodong_choujiang_start_time']) ||
            !isset($config['huodong_jiesuan_days']) ||
            !check($config['huodong_choujiang_start_time'], "intgt0") ||
            !check($config['huodong_jiesuan_days'], "intgt0")
        ) {
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试:c"];
        }
        $huodong_choujiang_start_time = intval($config['huodong_choujiang_start_time']);
        $huodong_jiesuan_days = intval($config['huodong_jiesuan_days']);
        //
        $data = [
            "money" => number_format($money, 2, ".", ""),
            "member_money" => number_format($member_money, 2, ".", ""),
            "lng" => number_format($lng, 6, ".", ""),
            "lat" => number_format($lat, 6, ".", ""),
            "pay_type" => intval($pay_type),
            "type_id" => intval($type_id),
            "num" => intval($num),
            "yongjin_bili" => intval($yongjin_bili),
            "sheng_id" => intval($sheng_id),
            "shi_id" => intval($shi_id),
            "qu_id" => intval($qu_id),
            "name" => ":name",
            "title" => ":title",
            "img_url" => ":img_url",
            "contents" => ":contents",
            "liucheng" => ":liucheng",
            "addr" => ":addr",
            "guige" => ":guige",
            "sheng" => ":sheng",
            "shi" => ":shi",
            "qu" => ":qu",
            "lianxi_name" => ":lianxi_name",
            "lianxi_mobile" => ":lianxi_mobile",
            "lianxi_qrcode" => ":lianxi_qrcode",
            "qun_qrcode" => ":qun_qrcode",
            "start_time" => date("Y-m-d H:i:s", strtotime($start_time)),
            "end_time" => date("Y-m-d H:i:s", strtotime($start_time)),
            // {{ AURA-X: Add - 处理报名截止时间. Confirmed via 寸止. }}
            "baoming_end_time" => !empty($baoming_end_time) ? date("Y-m-d H:i:s", strtotime($baoming_end_time)) : date("Y-m-d H:i:s", strtotime($start_time)),
            "time" => DATETIME,
            "status" => 0,
            "is_online" => (int)$is_online,
            "member_only" => $member_only,
            // {{ AURA-X: Add - 处理退款规则. Confirmed via 寸止. }}
            "refund_rule" => (int)$refund_rule,
        ];
        if ($pay_type == 1) {
            $data['jiesuan_status'] = 0;
            $data['jiesuan_time'] = date("Y-m-d H:i:s", strtotime("+{$huodong_jiesuan_days} days", strtotime($data['end_time'])));
        } else {
            $data['jiesuan_status'] = 2;
        }
        //
        /*if (strtotime($data['baoming_start_time']) >= strtotime($data['baoming_end_time'])) {
            return ["status" => "error", "msg" => "报名开始和结束时间错误"];
        }
        if (strtotime($data['start_time']) >= strtotime($data['end_time'])) {
            return ["status" => "error", "msg" => "活动开始和结束时间错误"];
        }
        if (strtotime($data['baoming_end_time']) >= strtotime($data['start_time'])) {
            return ["status" => "error", "msg" => "活动时间和报名时间冲突"];
        }*/
        $check_type = Db()->table("huodong_type")->where("id={$data['type_id']}")->fetch();
        if (empty($check_type)) {
            return ["status" => "error", "msg" => "活动分类不存在"];
        }
        //
        // 注释掉佣金验证逻辑 - 活动发布功能现在不需要佣金逻辑
        // if ($pay_type == 2 && $data['yongjin_bili'] > 0) {
        //     return ["status" => "error", "msg" => "线下收费不支持佣金"];
        // }
        //
        $prepareParam = [
            ":name" => htmlspecialchars(trim($name)),
            ":title" => htmlspecialchars(trim($title)),
            ":img_url" => htmlspecialchars(trim($img_url)),
            ":contents" => htmlspecialchars(trim($contents)),
            ":addr" => htmlspecialchars(trim($addr)),
            ":guige" => htmlspecialchars(trim($guige)),
            ":sheng" => htmlspecialchars(trim($sheng)),
            ":shi" => htmlspecialchars(trim($shi)),
            ":qu" => htmlspecialchars(trim($qu)),
            ":lianxi_name" => htmlspecialchars(trim($lianxi_name)),
            ":lianxi_mobile" => htmlspecialchars(trim($lianxi_mobile)),
            ":lianxi_qrcode" => htmlspecialchars(trim($lianxi_qrcode)),
            ":liucheng" => json_encode(json_decode(stripslashes(trim($liucheng, '"')), true), JSON_UNESCAPED_UNICODE),
        ];
        if (is_null($prepareParam[':liucheng']) || $prepareParam[':liucheng'] == "null" || $prepareParam[':liucheng'] == "") {
            return ["status" => "error", "msg" => "流程参数错误"];
        }
        //
        $is_choujiang = intval($is_choujiang);
        $choujiang_huiyuan_num = intval($choujiang_huiyuan_num);
        $choujiang_zhongjiang_num = intval($choujiang_zhongjiang_num);
        $choujiang_zhekou = intval($choujiang_zhekou);
        if ($pay_type == 2 && $is_choujiang == 1) {
            return ["status" => "error", "msg" => "线下收费不支持抽奖"];
        }
        if ($is_choujiang == 1) {
            $data['choujiang_status'] = 0;
            $data['choujiang_config_json'] = json_encode([
                "huiyuan_num" => $choujiang_huiyuan_num,
                "zhongjiang_num" => $choujiang_zhongjiang_num,
                "zhekou" => $choujiang_zhekou,
            ]);
            $data['choujiang_time'] = date("Y-m-d H:i:s", strtotime("+{$huodong_choujiang_start_time} minute", strtotime($data['start_time'])));
            //
            // 注释掉佣金相关的抽奖验证逻辑 - 活动发布功能现在不需要佣金逻辑
            // if ($data['yongjin_bili'] > 0 && $choujiang_zhekou == 0) {
            //     return ["status" => "error", "msg" => "佣金和免单不能同时存在"];
            // }
            // if ($data['yongjin_bili'] + (100 - $choujiang_zhekou) > 100) {
            //     return ["status" => "error", "msg" => "佣金或抽奖比例过高"];
            // }
            //
        } else {
            $data['choujiang_status'] = 2;
            $data["choujiang_config_json"] = json_encode(new \stdClass());
        }
        //
        dbConn();
        try {
            $rowCount = Db()->table("huodong")->where("id={$huodong_id} AND uid={$uid}")->prepareParam($prepareParam)->update($data);
            //
            Db()->table("huodong_img")->where("huodong_id={$huodong_id}")->del();
            //
            if (!empty($imgs_arr)) {
                foreach ($imgs_arr as $img) {
                    Db()->table("huodong_img")->prepareParam([":img_url" => $img])->insert(["huodong_id" => $huodong_id, "img_url" => ":img_url"]);
                }
            }
            //
            $this->user_log($uid, "编辑活动信息【{$huodong_id}】");
            //
            return ["status" => "ok", "msg" => "操作成功,审核中"];
        } catch (\Throwable $e) {
            // 记录活动更新失败详细日志
            $this->logWriteError("更新活动失败", [
                'uid' => $uid,
                'huodong_id' => $huodong_id,
                'name' => $name,
                'title' => $title,
                'type_id' => $type_id,
                'money' => $money,
                'num' => $num,
                'start_time' => $start_time,
                'exception_message' => $e->getMessage(),
                'exception_trace' => $e->getTraceAsString()
            ]);

            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
	* @apiName 取消活动
	* @method cancel_huodong
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param huodong_id string 活动编号
	* @return {"status":"ok","msg":"\u64cd\u4f5c\u6210\u529f"}
	*/
    public function cancel_huodong($uid, $token, $huodong_id)
    {
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            !check($huodong_id, "intgt0") ||
            empty($token) ||
            strlen($token) != 32
        ) return ["status" => "error", "msg" => "参数错误"];
        $uid = (int)$uid;
        $huodong_id = (int)$huodong_id;
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        dbConn();
        $huodong_info = Db()->table("huodong")->select("id,baoming_num,status")->where("id={$huodong_id} AND uid={$uid}")->fetch();
        if (empty($huodong_info)) {
            return ["status" => "error", "msg" => "活动信息不存在"];
        }
        if ($huodong_info['baoming_num'] > 0) {
            return ["status" => "error", "msg" => "活动信息已有人报名，不支持取消"];
        }
        /*
		if($huodong_info['status'] != 0){
			return ["status"=>"error","msg"=>"活动信息已不支持取消"];
		}
		*/
        // 0=>"审核中",1=>"审核通过",2=>"审核未通过",3=>"取消"
        // 这里改为：所有状态下均可取消
        try {
            // 获取活动信息
            $huodong_info = Db()->table("huodong")->where("id={$huodong_id} AND uid={$uid}")->fetch();
            if (empty($huodong_info)) {
                return ["status" => "error", "msg" => "活动不存在"];
            }

            $result = !Db()->table("huodong")->where("id={$huodong_id} AND uid={$uid}")->update(["status" => 3]);
            if ($result) {
                // 记录取消活动失败日志
                $this->logWriteError("取消活动失败", [
                    'uid' => $uid,
                    'huodong_id' => $huodong_id,
                    'operation' => 'cancel_huodong'
                ]);
                return ["status" => "error", "msg" => "操作失败"];
            }

            // 获取所有已报名的用户，发送取消通知
            $baoming_users = Db()->table("huodong_baoming_order")
                ->select("uid")
                ->where("huodong_id={$huodong_id} AND status=1")
                ->fetchAll();

            if (!empty($baoming_users)) {
                foreach ($baoming_users as $user) {
                    $this->create_notification(
                        $user['uid'],
                        "activity_cancelled",
                        "活动取消通知",
                        "您报名的活动「{$huodong_info['title']}」已被主办方取消",
                        $huodong_id
                    );
                }
            }

            // 给活动发布者发送通知
            $this->create_notification(
                $uid,
                "activity_cancelled",
                "活动取消成功",
                "您的活动「{$huodong_info['title']}」已成功取消",
                $huodong_id
            );

            $this->user_log($uid, "取消活动【{$huodong_id}】");

            return ["status" => "ok", "msg" => "操作成功"];
        } catch (\Throwable $e) {
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
	* @apiName 获取活动列表
	* @method get_list
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param type_id string 分类(0=全部)
	* @param page string 页码,默认1
	* @param page_size string 每页多少条,默认20
	* @param is_tuijian string 是否推荐:1=是,0=否(可选)
	* @param keyword string 关键词(可选)
	* @param shi_id string 市级编号
	* @param qu_id string 区级编号(可选)
	* @param sort string 排序:1=默认,2=距离,3=人气(可选)
	* @param lng string 用户经度坐标(可选)
	* @param lat string 用户纬度坐标(可选)
	* @param baoming_status string 报名状态:1=未开始,2=报名中,3=报名已结束(可选)
	* @param huodong_status string 活动状态:1=未开始,2=进行中,3=已结束(可选)
	* @param baoming_date string 可报名日期(可选)
	* @param huodong_date string 活动日期(可选)
    * @param list_type string 列表类型:1=全部活动,2=线上活动,3=历史活动(可选)
	* @return {"status":"ok","data":[{"id":2,"uid":2,"name":"活动3","title":"活动标题","img_url":"http:\/\/************\/test.jpg","money":"100.00","guige":"规格1111122","num":80,"baoming_start_time":"2023-11-18 00:00:00","baoming_end_time":"2023-11-18 11:30:00","baoming_num":0,"date":"2023-11-18","start_time":"12:00:00","end_time":"14:00:00","sheng":"安徽省","shi":"铜陵市","qu":"铜官区","addr":"某某街道某公园11","yongjin_bili":10,"distance":"0米","user":{"uid":2,"nickname":"test","avatar":"http:\/\/127.0.0.1\/test.jpg","sex":2,"is_huiyuan":0,"mobile":"15800000000"}}],"count":1}
	*/
    public function get_list($uid = 0, $token = "", $shi_id = 0, $qu_id = 0, $page = 1, $page_size = 20, $type_id = 0, $is_tuijian = "ALL", $keyword = "", $sort = 1, $lng = 0, $lat = 0, $baoming_status = "ALL", $huodong_status = "ALL", $baoming_date = "", $huodong_date = "", $list_type = 1)
    {
        try {
            if (
                empty($page) ||
                !check($page, "intgt0") ||
                empty($page_size) ||
                !check($page_size, "intgt0")
            ) return ["status" => "error", "msg" => "参数错误"];

            $page = (int)$page;
            $page_size = (int)$page_size;
            $type_id = (int)$type_id;
            $sort = (int)$sort;
            $lng = (float)$lng;
            $lat = (float)$lat;
            $list_type = (int)$list_type;

            // 处理城市ID：支持adcode格式（6位数字）和旧格式（整数）
            $shi_id_condition = "";
            $qu_id_condition = "";

            if (!empty($shi_id)) {
                if (strlen($shi_id) == 6 && is_numeric($shi_id)) {
                    // 新的adcode格式
                    $shi_id_condition = "shi_id='{$shi_id}'";
                } else {
                    // 旧的整数格式
                    $shi_id = (int)$shi_id;
                    $shi_id_condition = "shi_id={$shi_id}";
                }
            }

            if (!empty($qu_id)) {
                if (strlen($qu_id) == 6 && is_numeric($qu_id)) {
                    // 新的adcode格式
                    $qu_id_condition = "qu_id='{$qu_id}'";
                } else {
                    // 旧的整数格式
                    $qu_id = (int)$qu_id;
                    $qu_id_condition = "qu_id={$qu_id}";
                }
            }

            dbConn();

        // 基础查询条件
        $where = "1 AND status=1";
        if ($type_id > 0) $where .= " AND type_id={$type_id}";
        if (!empty($keyword)) $where .= " AND (name LIKE '%{$keyword}%' OR title LIKE '%{$keyword}%')";

        // 根据list_type添加筛选条件
        switch ($list_type) {
            case 1: // 全部活动 - 显示本城市线下活动 + 所有线上活动
                $today = date('Y-m-d H:i:s');
                $where .= " AND start_time >= '{$today}'";

                // 城市筛选：本城市的线下活动 OR 所有线上活动
                if (!empty($shi_id_condition)) {
                    $city_condition = "({$shi_id_condition}";
                    if (!empty($qu_id_condition)) {
                        $city_condition .= " AND {$qu_id_condition}";
                    }
                    $city_condition .= ") OR is_online = 1";
                    $where .= " AND ({$city_condition})";
                }
                break;

            case 2: // 线上活动 - 只显示线上活动，不受城市限制
                $where .= " AND is_online = 1";
                $today = date('Y-m-d H:i:s');
                $where .= " AND start_time >= '{$today}'";
                break;

            case 3: // 历史活动 - 历史活动按原逻辑，可以有城市筛选
                $today = date('Y-m-d H:i:s');
                $where .= " AND start_time <= '{$today}'";

                // 历史活动的城市筛选
                if (!empty($shi_id_condition)) $where .= " AND {$shi_id_condition}";
                if (!empty($qu_id_condition)) $where .= " AND {$qu_id_condition}";
                break;
        }

        // 报名状态筛选
        /*if ($baoming_status !== "ALL") {
            switch ($baoming_status) {
                case "1": // 未开始
                    $where .= " AND baoming_start_time > NOW()";
                    break;
                case "2": // 报名中
                    $where .= " AND baoming_start_time <= NOW() AND baoming_end_time >= NOW()";
                    break;
                case "3": // 报名已结束
                    $where .= " AND baoming_end_time < NOW()";
                    break;
            }
        }*/

        // 活动状态筛选
        if ($huodong_status !== "ALL") {
            switch ($huodong_status) {
                case "1": // 未开始
                    $where .= " AND CONCAT(start_time, ' ', start_time) > NOW()";
                    break;
                case "2": // 进行中
                    $where .= " AND CONCAT(start_time, ' ', start_time) <= NOW() AND CONCAT(start_time, ' ', end_time) >= NOW()";
                    break;
                case "3": // 已结束
                    $where .= " AND CONCAT(start_time, ' ', end_time) < NOW()";
                    break;
            }
        }

        // 日期筛选
        /*if (!empty($baoming_date)) {
            $where .= " AND DATE(baoming_start_time) <= '{$baoming_date}' AND DATE(baoming_end_time) >= '{$baoming_date}'";
        }*/
        if (!empty($huodong_date)) {
            $where .= " AND DATE(start_time) = '{$huodong_date}'";
        }

        // 排序
        $order = " start_time ";

        // 历史活动按开始时间倒序排列
        if ($list_type == 3) {
            $order = " start_time DESC ";
        }

        /* switch ($sort) {
             case 2: // 距离
                 if ($lng > 0 && $lat > 0) {
                     $order = "SQRT(POW(lng-{$lng},2)+POW(lat-{$lat},2)) ASC, id DESC";
                 } else {
                     $order = "id DESC";
                 }
                 break;
             case 3: // 人气
                 $order = "baoming_num DESC, id DESC";
                 break;
             default: // 默认
                 $order = "id DESC";
                 break;
         }*/

        // 查询数据
        $data = Db()->table("huodong")
            ->select("id,uid,name,title,img_url,money,member_money,is_online,guige,num,baoming_start_time,baoming_end_time,baoming_num,start_time,end_time,sheng,shi,qu,addr,yongjin_bili,lng,lat,pay_type,member_only,refund_rule")
            ->where($where)
            ->order($order)
            ->page($page, $page_size);
        if (empty($data)) {
            return ["status" => "empty", "msg" => "暂无数据"];
        }



        // 优化：批量获取用户信息，避免N+1查询问题
        $user_ids = array_unique(array_column($data, 'uid'));
        $users = [];
        if (!empty($user_ids)) {
            $user_list = Db()->table("user")
                ->select("uid,nickname,avatar,sex,is_huiyuan,mobile")
                ->where("uid IN (" . implode(',', $user_ids) . ")")
                ->fetchAll();
            foreach ($user_list as $user) {
                $users[$user['uid']] = $user;
            }
        }

        // 处理数据
        foreach ($data as &$row) {
            // 使用预加载的用户信息
            $row['user'] = isset($users[$row['uid']]) ? $users[$row['uid']] : new \stdClass();

            // 计算距离
            if ($lng > 0 && $lat > 0 && $row['lng'] > 0 && $row['lat'] > 0) {
                $distance = sqrt(pow($row['lng'] - $lng, 2) + pow($row['lat'] - $lat, 2)) * 111000;
                if ($distance < 1000) {
                    $row['distance'] = round($distance) . "米";
                } else {
                    $row['distance'] = round($distance / 1000, 1) . "公里";
                }
            } else {
                $row['distance'] = "0米";
            }
        }

        return ["status" => "ok", "data" => $data, "count" => \core\Page::$count];
        } catch(\Throwable $e) {
            // 记录详细的错误日志
            $this->logWriteError("获取活动列表失败", [
                'uid' => $uid,
                'type_id' => $type_id,
                'list_type' => $list_type,
                'shi_id' => $shi_id,
                'qu_id' => $qu_id,
                'page' => $page,
                'page_size' => $page_size,
                'exception_message' => $e->getMessage(),
                'exception_trace' => $e->getTraceAsString()
            ]);
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
	* @apiName 获取和我相关的活动列表
	* @method get_my_list
	* @POST
	* @param type string 类型:1=发布的,2=报名的,3=收藏的,4=评价的,5=分享的
	* @param uid string 用户编号
	* @param token string token
	* @param page string 页码,默认1
	* @param page_size string 每页多少条,默认20
	* @return {"status":"ok","data":[{"id":2,"huodong_id":2,"contents":"评价内容","imgs_url":["http:\/\/************\/test.jpg","http:\/\/************\/test.jpg","http:\/\/************\/test.jpg"],"time":"2023-11-18 13:21:00","huodong_info":{"id":2,"uid":2,"name":"活动3","title":"活动标题","img_url":"http:\/\/************\/test.jpg","guige":"规格1111122","num":80,"baoming_start_time":"2023-11-18 00:00:00","baoming_end_time":"2023-11-19 11:30:00","date":"2023-11-19","start_time":"12:00:00","end_time":"14:00:00","sheng":"安徽省","shi":"铜陵市","qu":"铜官区","addr":"某某街道某公园11","yongjin_bili":10,"status":1,"user":{"uid":2,"avatar":"http:\/\/127.0.0.1\/test.jpg","nickname":"test","mobile":"15800000000"}},"user":{"uid":2,"nickname":"test","avatar":"http:\/\/127.0.0.1\/test.jpg","sex":2,"is_huiyuan":0,"mobile":"15800000000"}}]}
	*/
    public function get_my_list($uid, $token, $page = 1, $page_size = 20, $type = 1)
    {
        try {
            if (
                empty($uid) ||
                !check($uid, "intgt0") ||
                empty($token) ||
                strlen($token) != 32 ||
                empty($page) ||
                !check($page, "intgt0") ||
                empty($page_size) ||
                !check($page_size, "intgt0") ||
                !check($type, "intgt0")
            ) return ["status" => "error", "msg" => "参数错误"];
            $uid = (int)$uid;
            $page = (int)$page;
            $page_size = (int)$page_size;
            if (!$this->auth($uid, $token)) {
                return ["status" => "relogin", "msg" => "登录信息验证失败"];
            }
            dbConn();
            // {{ AURA-X: Modify - 修改排序逻辑，我的发布和我的报名按活动开始时间倒序，我的收藏和我的提问按创建时间倒序. Confirmed via 寸止. }}
            if ($type == 1) {
                // 我的发布 - 按活动开始时间倒序排列
                $data = Db()->table("huodong")->select("id as huodong_id,jiesuan_status,jiesuan_money,jiesuan_yongjin,jiesuan_choujiang,is_online,money,member_money,start_time")->where("uid={$uid}")->order("start_time DESC,pinned_at DESC,id DESC")->page($page, $page_size);
            } else if ($type == 2) {
                // 我的报名 - 暂时保持原有查询方式，避免复杂的JOIN查询
                $data = Db()->table("huodong_baoming_order")->select("id,order_id,money,yongjin_money,pay_type,status,huodong_id,time")->where("uid={$uid}")->order("id DESC")->page($page, $page_size);
            } else if ($type == 3) {
                // 我的收藏 - 按创建时间倒序排列
                $data = Db()->table("huodong_shoucang")->select("id,huodong_id,time")->where("uid={$uid}")->order("time DESC,id DESC")->page($page, $page_size);
            } else if ($type == 4) {
                // 我的提问 - 按创建时间倒序排列
                $data = Db()->table("huodong_pingjia")->select("id,huodong_id,contents,imgs_url,time")->where("uid={$uid}")->order("time DESC,id DESC")->page($page, $page_size);
            } else if ($type == 5) {
                $data = Db()->table("user_fenxiang_log")->select("id,item_id AS huodong_id,time")->where("uid={$uid} AND type=2")->order("id DESC")->page($page, $page_size);
            } else {
                return ["status" => "error", "msg" => "参数错误"];
            }
            if (empty($data)) {
                return ["status" => "empty", "msg" => "资源不存在"];
            }
            foreach ($data as &$row) {
                //
                if (isset($row['imgs_url'])) {
                    $row['imgs_url'] = !empty($row['imgs_url']) ? explode("|", trim($row['imgs_url'], "|")) : [];
                }
                //
                $huodong_info = Db()->table("huodong")->select("id,uid,name,title,img_url,type_id,guige,money,member_money,is_online,pay_type,num,baoming_num,baoming_start_time,baoming_end_time,start_time,end_time,sheng,shi,qu,addr,yongjin_bili,status,liucheng,contents,lat,lng,sheng_id,shi_id,qu_id,choujiang_status,choujiang_config_json,lianxi_name,lianxi_mobile,lianxi_qrcode,pinned_at")->where("id=:huodong_id")->prepareParam([":huodong_id"=>$row['huodong_id']])->fetch(); // Added member_money, is_online
                if (!empty($huodong_info)) {
                    $user = Db()->table("user")->select("uid,nickname,avatar,sex,is_huiyuan,mobile")->where("uid=:uid")->prepareParam([":uid"=>$huodong_info['uid']])->fetch();
                    $huodong_info['user'] = $user ?: new \stdClass();
                    $huodong_info['liucheng'] = json_decode($huodong_info['liucheng'], true);
                    $huodong_info['choujiang_config'] = json_decode($huodong_info['choujiang_config_json'], true);
                }
                $row['huodong_info'] = $huodong_info ?: new \stdClass();
                //
            }
            return ["status" => "ok", "data" => $data];
        } catch(\Throwable $e) {
            // 记录详细的错误日志
            $this->logWriteError("获取我的活动列表失败", [
                'uid' => $uid,
                'type' => $type,
                'page' => $page,
                'page_size' => $page_size,
                'exception_message' => $e->getMessage(),
                'exception_trace' => $e->getTraceAsString()
            ]);
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
	* @apiName 获取活动详情
	* @method get_info
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param huodong_id string 活动编号
	* @return {"status":"ok","data":{"id":3,"uid":2,"name":"活动2","title":"活动标题","img_url":"http:\/\/************\/test.jpg","money":"100.00","contents":"活动介绍","liucheng":["流程1","流程2"],"guige":"规格1111","num":80,"baoming_start_time":"2023-11-18 00:00:00","baoming_end_time":"2023-11-18 11:00:00","baoming_num":0,"date":"2023-11-18","start_time":"12:00:00","end_time":"14:00:00","sheng":"安徽省","shi":"铜陵市","qu":"铜官区","addr":"某某街道某公园","yongjin_bili":5,"time":"2023-11-18 09:49:52","pingjia_times":0,"user":{"uid":2,"nickname":"test","avatar":"http:\/\/127.0.0.1\/test.jpg","sex":2,"is_huiyuan":0,"mobile":"15800000000"},"imgs":["http:\/\/************\/test.jpg","http:\/\/************\/test.jpg","http:\/\/************\/test.jpg"],"is_shoucang":0,"is_zan":0}}
	*/
    public function get_info($uid, $token, $huodong_id)
    {
        try {
            if (!check($huodong_id, "intgt0")) {
                return ["status" => "error", "msg" => "参数错误"];
            }

            // 修复：允许未登录用户访问活动详情，uid可以为0或空
            // 只验证huodong_id是必需的
            $huodong_id = (int)$huodong_id;
            $where = "id=:huodong_id";
            $uid = (int)$uid; // 未登录时uid为0
            dbConn();
            // {{ AURA-X: Remove - 移除普通执行流程中的exception_log. Confirmed via 寸止. }}

        $data = Db()->table("huodong")->select(
            "id,uid,name,title,type_id,img_url,money,member_money,is_online,pay_type,
            contents,liucheng,guige,num,baoming_start_time,baoming_end_time,
            baoming_num,start_time,end_time,sheng,shi,qu,addr,lng,lat,
            yongjin_bili,time,pingjia_times,status,choujiang_status,
            choujiang_config_json,lianxi_name,lianxi_mobile,lianxi_qrcode,qun_qrcode,
            jiesuan_status,jiesuan_money,jiesuan_yongjin,jiesuan_choujiang,
            sheng_id,shi_id,qu_id,pinned_at,member_only,refund_rule"
        )->where($where)->prepareParam([":huodong_id"=>$huodong_id])->fetch();
        // {{ AURA-X: Remove - 移除普通执行流程中的exception_log. Confirmed via 寸止. }}
        if (empty($data)) {
            return ["status" => "empty", "msg" => "资源不存在"];
        }
        // 判断是否为真实登录用户（uid > 0 且有有效token）
        $isLoggedInUser = $uid > 0 && !empty($token) && strlen($token) == 32;
        $authSuccess = false;

        if ($isLoggedInUser) {
            // 对于已登录用户，尝试进行token验证
            $authSuccess = $this->auth($uid, $token);
        }

        if ($authSuccess) {
            // 认证成功，获取用户相关状态
            // 获取当前用户的收藏情况
            $shoucang = Db()->table("huodong_shoucang")->where("uid={$uid} AND huodong_id={$huodong_id}")->fetch();
            $data['is_shoucang'] = $shoucang ? 1 : 0;

            // 获取赞的情况
            $zan = Db()->table("huodong_zan")->where("uid={$uid} AND huodong_id={$huodong_id}")->fetch();
            $data['is_zan'] = $zan ? 1 : 0;

            // 获取当前用户报名的情况
            $baoming_order = Db()->table("huodong_baoming_order")->where("huodong_id={$huodong_id} AND uid={$uid} AND status=1")->fetch();
            $data['baoming_order'] = $baoming_order ?: new \stdClass();
        } else {
            // 未登录或认证失败，使用默认值（不阻断请求）
            $data['is_shoucang'] = 0;
            $data['is_zan'] = 0;
            $data['baoming_order'] = new \stdClass();
        }

        // {{ AURA-X: Remove - 移除普通执行流程中的exception_log. Confirmed via 寸止. }}
        $user = Db()->table("user")->select("uid,nickname,avatar,sex,is_huiyuan,mobile")->where("uid={$data['uid']}")->fetch();
        $data['user'] = $user ?: new \stdClass();

        //
        $imgs = Db()->table("huodong_img")->select("img_url")->where("huodong_id={$data['id']}")->fetchAll();
        $data['imgs'] = $imgs ? array_column($imgs, "img_url") : [];
        // {{ AURA-X: Remove - 移除普通执行流程中的exception_log. Confirmed via 寸止. }}
        // 处理过去内容
        $data['contents'] = htmlspecialchars_decode($data['contents']);

        // 在 get_info 方法中加入用户报名查询（仅在认证成功时）
        if ($authSuccess && $uid) {
            $baoming_info = Db()->table("huodong_baoming_order")
                ->select("id,order_id,status,time")
                ->where("huodong_id={$huodong_id} AND uid={$uid} AND status=1")
                ->fetch();
            if ($baoming_info) {
                // 确保报名订单信息被添加到返回数据中
                $data['baoming_order'] = $baoming_info;
            }
        }
        // {{ AURA-X: Remove - 移除普通执行流程中的exception_log. Confirmed via 寸止. }}
        return ["status" => "ok", "data" => $data];
        } catch(\Throwable $e) {
            // 记录详细的错误日志
            $this->logWriteError("获取活动详情失败", [
                'uid' => $uid,
                'huodong_id' => $huodong_id,
                'exception_message' => $e->getMessage(),
                'exception_trace' => $e->getTraceAsString()
            ]);
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
	* @apiName 报名下单
	* @method add_baoming
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param huodong_id string 活动编号
	* @param is_choujiang string 是否参与抽奖:1=是,0=否
	* @param lianxi_name string 联系人姓名
	* @param lianxi_mobile string 联系人手机号
	* @param lianxi_sex string 联系人性别
	* @return {"status":"ok","msg":"操作成功","order_id":"20231118113306655667","money":"100.00"}
	*/
    public function add_baoming($uid, $token, $huodong_id, $is_choujiang, $lianxi_name, $lianxi_mobile, $lianxi_sex = 0)
    {
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32 ||
            !check($huodong_id, "intgt0") ||
            !in_array($is_choujiang, [0, 1]) ||
            empty($lianxi_name) ||
            empty($lianxi_mobile) ||
            !check($lianxi_mobile, "mobile") ||
            !in_array($lianxi_sex, [0, 1, 2])
        ) return ["status" => "error", "msg" => "参数错误"];

        $authResult = $this->auth($uid, $token);
        if ($authResult === false) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        } elseif ($authResult === 'frozen') {
            return ["status" => "error", "msg" => "账户已冻结，请联系管理员"];
        }

        $uid = (int)$uid;
        $huodong_id = (int)$huodong_id;
        $is_choujiang = (int)$is_choujiang;
        $lianxi_sex = (int)$lianxi_sex;

        dbConn();

        // 查询用户信息，包含会员状态
        $user_info = Db()->table("user")->select("uid, mobile, is_huiyuan")->where("uid={$uid}")->fetch();



        // 获取审核通过的指定 id 的活动，包含member_only字段
        $huodong_info = Db()->table("huodong")->select("*")->where("id={$huodong_id} AND status=1")->fetch();

        if (empty($huodong_info)) {
            return $this->handleError("活动不存在");
        }

        // 检查活动是否仅会员可参加
        if ($huodong_info['member_only'] == 1 && $user_info['is_huiyuan'] != 1) {
            return $this->handleError("该活动仅限会员参加，请先开通会员");
        }

        // {{ AURA-X: Modify - 改为基于报名截止时间判断. Confirmed via 寸止. }}
        // 检查活动的报名截止时间、人数等信息
        if (strtotime($huodong_info['baoming_end_time']) < time()) {
            return $this->handleError("活动报名已结束");
        }
        if ($huodong_info['baoming_num'] >= $huodong_info['num']) {
            return $this->handleError("活动报名人数已满");
        }

        // 检查当前用户是否报名过该活动 (查找未支付或已支付的订单)
        $baoming_order = Db()->table("huodong_baoming_order")->where("huodong_id={$huodong_id} AND uid={$uid} AND status IN (0, 1)")->fetch();
        if (!empty($baoming_order)) {
            if($baoming_order['status'] == 0){
                // 检查订单创建时间，如果超过30分钟未支付，则自动取消
                $order_time = strtotime($baoming_order['time']);
                $current_time = time();
                $time_diff = $current_time - $order_time;

                // 如果订单超过30分钟未支付，自动取消
                if ($time_diff > 1800) { // 1800秒 = 30分钟
                    // 更新订单状态为已取消
                    Db()->table("huodong_baoming_order")->where("id={$baoming_order['id']}")->update(["status" => 2]);
                    // 返回新的报名请求
                    return ["status" => "ok", "msg" => "之前的未支付订单已过期，可以重新报名"];
                } else {
                    // 未过期的未支付订单
                    return ["status" => "error", "msg" => "您有待支付的报名订单，请先完成支付或取消", "order_id" => $baoming_order['order_id']]; // 可选返回order_id
                }
            } else {
                 return ["status" => "error", "msg" => "您已成功报名该活动"];
            }
        }

        // 根据会员状态计算支付金额
        $pay_money = 0;
        if ($user_info['is_huiyuan'] == 1) {
            $pay_money = $huodong_info['member_money'];
        } else {
            $pay_money = $huodong_info['money'];
        }
        $pay_money = number_format($pay_money, 2, ".", "");

        // 检查是否需要支付 (免费或线下支付)
        $nopay = false;
        // 使用计算出的 $pay_money 判断
        if (bccomp($pay_money, "0.00", 2) <= 0 || $huodong_info['pay_type'] == 2) {
            $nopay = true;
            // 如果是免费或线下，确保金额为0
            $pay_money = number_format(0, 2, ".", "");
        }
        $order_id = date("YmdHis") . makeCode(6);
        $data = [
            "order_id" => $order_id,
            "huodong_id" => $huodong_id,
            "uid" => $uid,
            "money" => $pay_money, // 存储计算出的金额
            "lianxi_sex" => $lianxi_sex,
            "yongjin_money" => 0,
            "is_choujiang" => 0,
            "lianxi_name" => ":lianxi_name",
            "lianxi_mobile" => ":lianxi_mobile",
            "status" => 0, // 默认状态为未支付
            "pay_time" => null, // 默认支付时间为空
            "pay_type" => 0, // 默认支付方式
        ];

        if ($nopay) {
            $data['pay_time'] = DATETIME;
            $data['pay_type'] = 3;
            $data['status'] = 1; // 免费或线下，直接设为已支付
        }

        // 佣金
        // 如果佣金比例存在（大于0）且活动需要付款。
        /*  if ($huodong_info['yongjin_bili'] > 0 && bccomp($huodong_info['money'], "0.00", 2) > 0) {
              // 获取平台需要获取的比例（20%）
              $huodong_yongjin_pingtai_bili = $this->get_config("huodong_yongjin_pingtai_bili");
              if ($huodong_yongjin_pingtai_bili === false) {
                  return ["status" => "error", "msg" => "系统配置参数错误"];
              }

              $huodong_yongjin_pingtai_bili = intval($huodong_yongjin_pingtai_bili);
              $yongjin_zong = number_format($huodong_info['money'] * $huodong_info['yongjin_bili'] / 100, 2, ".", "");
              // 用户佣金
              $yongjin_money = number_format($yongjin_zong * (1 - $huodong_yongjin_pingtai_bili / 100), 2, ".", "");

              // 活动发布者粉丝报名，去掉平台抽成（逻辑）
              // uid：关注者，to_uid：被关注者
              $check = Db()->table("user_guanzhu")->where("uid={$uid} AND to_uid={$huodong_info['uid']}")->fetch();
              if(!empty($check)){
                  // 关注的关系存在, 则用户的佣金为 100%，不需要减扣平台的比例。
                  $yongjin_money = number_format($yongjin_zong * (1 - 0 / 100), 2, ".", "");
              }

              if (bccomp($yongjin_money, "0.00", 2) > 0) {

                  // 获取当前想要报名用户的父亲uid
                  $p_uid = Db()->table("user")->where("uid={$uid}")->getColumn("p_uid");

                  // 多级获取父亲 User
                  if (!empty($p_uid)) {
                      $c_p_uid = $p_uid;
                      while (true) {
                          if (empty($c_p_uid)) {
                              break;
                          }

                          // 获取父亲 User
                          $c_p_user = Db()->table("user")->select("uid,p_uid,is_huiyuan")->where("uid={$c_p_uid}")->fetch();

                          if (!empty($c_p_user) && $c_p_user['is_huiyuan'] == 1) {
                              $data['yongjin_zong'] = $yongjin_zong;
                              $data['yongjin_money'] = $yongjin_money;
                              $data['yongjin_uid'] = $c_p_uid;
                              break;
                          }
                          if (empty($c_p_user) || empty($c_p_user['p_uid'])) {
                              break;
                          }
                          $c_p_uid = $c_p_user['p_uid'];
                      }
                  }
              }
          }

          //是否参与抽奖
          if ($huodong_info['choujiang_status'] == 0 && $is_choujiang == 1) {
              $is_huiyuan = Db()->table("user")->where("uid={$uid}")->getColumn("is_huiyuan");
              if ($is_huiyuan == 1) {
                  $huodong_choujiang_year_zhongjiang_num = $this->get_config('huodong_choujiang_year_zhongjiang_num');
                  $limit_time = date("Y-m-d H:i:s", strtotime("-1 year"));
                  $zhongjian_count = Db()->table("huodong_zhongjiang_log")->where("uid={$uid} AND time>'{$limit_time}'")->count();
                  if ($zhongjian_count < $huodong_choujiang_year_zhongjiang_num) {
                      $data['is_choujiang'] = 1;
                  }
              }
          }
        */
        //
        $prepareParam = [
            ":lianxi_name" => $lianxi_name,
            ":lianxi_mobile" => $lianxi_mobile,
        ];
        dbConn();
        Db::begin();
        try {
            //
            Db()->table("huodong_baoming_order")->prepareParam($prepareParam)->insert($data);
            $insert_id = Db()->insertId();
            //
            // 只有在免费或线下支付($nopay=true, status=1)时才增加报名人数
            if ($nopay) {
                $sql = "UPDATE `huodong` SET baoming_num=baoming_num+1 WHERE id=:huodong_id AND baoming_num<num";
                $rowCount = Db()->_exec($sql, [":huodong_id" => $huodong_id]);
                if (empty($rowCount)) {
                    // 记录更新报名人数失败日志
                    $this->logWriteError("更新已报名人数失败", [
                        'uid' => $uid,
                        'huodong_id' => $huodong_id,
                        'operation' => 'baoming_add'
                    ]);
                    return ["status" => "error", "msg" => "更新已报名人数失败"];
                }
            }
            // 如果是免费活动或线下支付，直接创建报名成功通知
            if ($nopay) {
                $this->create_notification($uid, "activity_registration", "活动报名成功", "您已成功报名活动：{$huodong_info['name']}", $huodong_id);

                // {{ AURA-X: Add - 线下收款活动也需要记录收入. Confirmed via 寸止. }}
                // 如果是线下收款活动（非免费），记录收入
                if ($huodong_info['pay_type'] == 2 && bccomp($pay_money, "0.00", 2) > 0) {
                    $this->record_offline_activity_income([
                        'order_id' => $order_id,
                        'huodong_id' => $huodong_id,
                        'uid' => $uid,
                        'money' => $pay_money
                    ]);
                }
            }
            Db::commit();
            return ["status" => "ok", "msg" => "操作成功", "order_id" => $order_id, "money" => $pay_money];
        } catch (\Throwable $e) {
            Db::rollback();
            return $this->handleError("系统繁忙，请稍后再试", $e->getMessage());
        }
    }


    /**
     * @apiName 活动核销
     * @method hexiao_baoming
     * @POST
     * @param $uid
     * @param $token
     * @param $baoming_uid
     * @param $huodong_id
     * @param $id
     * @param $order_id
     * @return void
     */
    public function hexiao_baoming($uid, $token, $baoming_uid, $huodong_id, $order_id, $id)
    {
        // 1. 通过报名用户的 uid, huodong_id, 查询到报名订单
        // 2. 设置订单的状态为已核销
        // 3. 完成核销
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32 ||
            empty($order_id)
        ) return ["status" => "error", "msg" => "参数错误"];
        $uid = (int)$uid;
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        dbConn();
        $baoming_order = Db()->table("huodong_baoming_order")
            ->prepareParam([":order_id" => $order_id])
            ->where("order_id=:order_id AND uid={$baoming_uid}")
            ->fetch();

        // status
        // 状态:0=未支付,1=已支付,2=已取消,3=退款中,4=退款成功,5=退款失败
        if (empty($baoming_order)) {
            return ["status" => "error", "msg" => "报名信息不存在"];
        }
        // 必须要已支付才能算报名成功
        if ($baoming_order['status'] != 1) {
            return ["status" => "error", "msg" => "报名状态异常"];
        }

        $huodong_info = Db()->table("huodong")
            ->where("id={$baoming_order['huodong_id']}")
            ->fetch();
        if (empty($huodong_info)) {
            return ["status" => "error", "msg" => "活动信息不存在"];
        }

        try {
            $data = [
                "status_hexiao"=>":status_hexiao"
            ];
            $prepareParam = [
                ":status_hexiao"=>1
            ];
            $res = Db()->table("huodong_baoming_order")
                ->where("order_id={$order_id} AND uid={$baoming_uid}")
                ->prepareParam($prepareParam)
                ->update($data);
            // {
            //    "status": "ok",
            //    "msg": "核销成功",
            //    "data": 1
            // }
            return ["status" => "ok", "msg" => "核销成功", "data"=>$res];
        }catch (\Throwable $e) {
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
	* @apiName 取消报名
	* @method cancel_baoming
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param order_id string 报名订单编号
	* @return {"status":"ok","msg":"操作成功"}
	*/
    public function cancel_baoming($uid, $token, $order_id)
    {
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32 ||
            empty($order_id)
        ) return ["status" => "error", "msg" => "参数错误"];
        $uid = (int)$uid;
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        dbConn();
        $baoming_order = Db()->table("huodong_baoming_order")->prepareParam([":order_id" => $order_id])->where("order_id=:order_id AND uid={$uid}")->fetch();
        if (empty($baoming_order)) {
            return ["status" => "error", "msg" => "报名信息不存在"];
        }
        if ($baoming_order['status'] != 1) {
            return ["status" => "error", "msg" => "报名状态异常"];
        }
        $huodong_info = Db()->table("huodong")->where("id={$baoming_order['huodong_id']}")->fetch();
        if (empty($huodong_info)) {
            return ["status" => "error", "msg" => "活动信息不存在"];
        }
        if ($huodong_info['status'] != 1 || !in_array($huodong_info['jiesuan_status'], [0, 2])) {
            return ["status" => "error", "msg" => "活动状态已不支持取消报名"];
        }
        if (time() > strtotime($huodong_info['start_time'])) {
            return ["status" => "error", "msg" => "活动报名已截止,不支持取消"];
        }
        Db::begin();
        try {
            // {{ AURA-X: Modify - 根据是否支付费用决定初始状态. Confirmed via 寸止. }}
            // 检查是否支付了费用
            $baoming_money = $baoming_order['money'];
            $initial_status = (bccomp($baoming_money, "0.00", 2) > 0) ? 3 : 2; // 有费用=退款中，无费用=已取消

            $sql = "UPDATE `huodong_baoming_order` SET `status`={$initial_status} WHERE id={$baoming_order['id']} AND uid={$uid} AND status=1";
            $rowCount = Db()->_exec($sql);
            if (empty($rowCount)) {
                // 记录修改报名订单失败日志
                $this->logWriteError("修改报名订单信息失败", [
                    'uid' => $uid,
                    'baoming_order_id' => $baoming_order['id'],
                    'huodong_id' => $baoming_order['huodong_id'],
                    'operation' => 'baoming_del'
                ]);
                return ["status" => "error", "msg" => "修改报名订单信息失败"];
            }
            //
            $sql = "UPDATE `huodong` SET `baoming_num`=`baoming_num`-1 WHERE id=:huodong_id";
            Db()->_exec($sql, [":huodong_id" => $baoming_order['huodong_id']]);

            // {{ AURA-X: Add - 获取活动信息用于退款计算. Confirmed via 寸止. }}
            // 获取活动信息，包含退款规则和开始时间
            $huodong_info = Db()->_fetch(
                "SELECT refund_rule, start_time FROM huodong WHERE id = :huodong_id",
                [":huodong_id" => $baoming_order['huodong_id']]
            );

            // {{ AURA-X: Remove - $baoming_money已在前面定义. Confirmed via 寸止. }}
            // $baoming_money变量已在前面定义，无需重复定义
            if (bccomp($baoming_money, "0.00", 2) > 0) {
                // {{ AURA-X: Add - 基于退款规则计算退款金额. Confirmed via 寸止. }}
                // 计算实际退款金额
                $refund_amount = $this->calculateRefundAmount($huodong_info, $baoming_money);

                if ($baoming_order['pay_type'] == 1) {//微信支付
                    $refund_amount_fen = intval($refund_amount * 100);
                    $total_amount_fen = intval($baoming_money * 100);
                    $res = \model\Wechat::tuikuan($refund_amount_fen, $total_amount_fen, $baoming_order['order_id'], "取消报名");
                    if ($res['status'] == "ok") {
                        // {{ AURA-X: Modify - 移除直接设置退款成功状态，由微信回调处理. Confirmed via 寸止. }}
                        // 微信退款申请成功，状态已在前面设置为3（退款中）
                        // 最终状态将由微信退款回调通知（weixinpay_tuikuan_notify）来更新
                        // 成功：status=4，失败：status=5

                        // 预先处理收入记录调整（如果退款成功，此调整有效；如果失败，需要在回调中回滚）
                        $this->handleRefundIncomeAdjustment($baoming_order['order_id'], $refund_amount, $baoming_money);
                    } else {
                        $this->logWriteError("微信退款申请失败", [
                            'order_id' => $baoming_order['order_id'],
                            'error_msg' => $res['msg'],
                            'operation' => 'wechat_refund_request'
                        ]);
                        return ["status" => "error", "msg" => $res['msg']];
                    }
                } else if ($baoming_order['pay_type'] == 2) {//余额支付
                    // {{ AURA-X: Modify - 余额支付直接退款成功，使用计算后的退款金额. Confirmed via 寸止. }}
                    $sql = "UPDATE `user` SET `money`=`money`+{$refund_amount} WHERE uid={$baoming_order['uid']}";
                    $rowCount = Db()->_exec($sql);
                    if (empty($rowCount)) {
                        $this->logWriteError("余额通道退款失败", [
                            'order_id' => $baoming_order['order_id'],
                            'uid' => $baoming_order['uid'],
                            'money' => $refund_amount,
                            'operation' => 'balance_refund'
                        ]);
                        return ["status" => "error", "msg" => "余额通道退款失败"];
                    }
                    //
                    $shengyu = Db()->table("user")->where("uid={$baoming_order['uid']}")->getColumn("money");
                    $zhangdan = [
                        "uid" => $baoming_order['uid'],
                        "money" => $refund_amount,
                        "type" => 9,
                        "shengyu" => $shengyu,
                        "msg" => "取消报名退费:{$baoming_order['order_id']}",
                    ];
                    Db()->table("user_zhangdan")->insert($zhangdan);
                    //
                    // 余额支付可以立即退款成功
                    $sql = "UPDATE `huodong_baoming_order` SET `status`=4 WHERE id={$baoming_order['id']}";
                    Db()->_exec($sql);

                    // {{ AURA-X: Add - 处理退款对收入记录的影响. Confirmed via 寸止. }}
                    $this->handleRefundIncomeAdjustment($baoming_order['order_id'], $refund_amount, $baoming_money);
                    //
                } else {
                    $this->logWriteError("退款时未找到原支付方式", [
                        'order_id' => $baoming_order['order_id'],
                        'pay_type' => $baoming_order['pay_type'],
                        'operation' => 'refund_unknown_pay_type'
                    ]);
                    return ["status" => "error", "msg" => "退款时未找到原支付方式"];
                }
            } else {
                // {{ AURA-X: Modify - 免费活动直接设置为已取消状态. Confirmed via 寸止. }}
                // 免费活动无需退款，状态已在前面设置为2（已取消）
                // 无需额外处理
            }
            // 创建取消报名通知
            $this->create_notification(
                $uid,
                "registration_cancelled",
                "取消报名成功",
                "您已成功取消活动「{$huodong_info['title']}」的报名",
                $baoming_order['huodong_id']
            );

            // 通知活动发布者
            $this->create_notification(
                $huodong_info['uid'],
                "registration_cancelled",
                "有用户取消报名",
                "用户取消了活动「{$huodong_info['title']}」的报名",
                $baoming_order['huodong_id']
            );

            $this->user_log($uid, "取消报名【{$baoming_order['order_id']}】");

            Db::commit();
            return ["status" => "ok", "msg" => "操作成功"];
        } catch (\Throwable $e) {
            Db::rollback();
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    // {{ AURA-X: Add - 基于退款规则计算退款金额的方法. Confirmed via 寸止. }}
    /**
     * 根据活动退款规则计算实际退款金额
     * @param array $huodong_info 活动信息
     * @param float $original_amount 原始支付金额
     * @return float 实际退款金额
     */
    private function calculateRefundAmount($huodong_info, $original_amount) {
        // 获取退款规则
        $refund_rule = isset($huodong_info['refund_rule']) ? (int)$huodong_info['refund_rule'] : 1;

        // 获取活动开始时间
        $start_time = strtotime($huodong_info['start_time']);
        $current_time = time();

        // 如果活动已开始，不支持退款
        if ($current_time >= $start_time) {
            return 0.00;
        }

        // 计算距离活动开始的小时数
        $hours_before_start = ($start_time - $current_time) / 3600;

        // 根据退款规则计算退款比例
        $refund_percentage = 1.0; // 默认100%退款

        switch ($refund_rule) {
            case 1: // 随时退款
                $refund_percentage = 1.0; // 100%
                break;

            case 2: // 12小时规则
                if ($hours_before_start >= 12) {
                    $refund_percentage = 1.0; // 100%
                } else {
                    $refund_percentage = 0.5; // 50%
                }
                break;

            case 3: // 24小时规则
                if ($hours_before_start >= 24) {
                    $refund_percentage = 1.0; // 100%
                } else {
                    $refund_percentage = 0.5; // 50%
                }
                break;

            case 4: // 48小时规则
                if ($hours_before_start >= 48) {
                    $refund_percentage = 1.0; // 100%
                } else {
                    $refund_percentage = 0.3; // 30%
                }
                break;

            case 5: // 72小时规则
                if ($hours_before_start >= 72) {
                    $refund_percentage = 1.0; // 100%
                } else {
                    $refund_percentage = 0.3; // 30%
                }
                break;

            default:
                $refund_percentage = 1.0; // 默认100%
                break;
        }

        // 计算实际退款金额，保留两位小数
        $refund_amount = $original_amount * $refund_percentage;
        return number_format($refund_amount, 2, '.', '');
    }

    // {{ AURA-X: Add - 处理退款对收入记录的影响. Confirmed via 寸止. }}
    /**
     * 处理退款对活动发布方收入记录的影响
     * @param string $order_id 订单号
     * @param float $refund_amount 退款金额
     * @param float $original_amount 原始金额
     */
    private function handleRefundIncomeAdjustment($order_id, $refund_amount, $original_amount) {
        try {
            // 查找原始收入记录
            $income_record = Db()->_fetch(
                "SELECT * FROM activity_income_log WHERE order_id = :order_id",
                [":order_id" => $order_id]
            );

            if (empty($income_record)) {
                // 如果没有收入记录，可能是免费活动或线下支付，无需处理
                return;
            }

            // 计算退款损失金额
            $refund_loss = $original_amount - $refund_amount;

            if ($refund_loss > 0) {
                // 插入退款调整记录
                $adjustment_data = [
                    "activity_id" => $income_record['activity_id'],
                    "publisher_uid" => $income_record['publisher_uid'],
                    "order_id" => $order_id . "_refund",
                    "total_amount" => -$refund_loss, // 负数表示收入减少
                    "platform_fee_rate" => 0.00,
                    "platform_fee" => 0.00,
                    "publisher_income" => -$refund_loss, // 负数表示收入减少
                    "status" => $income_record['status'], // 保持与原记录相同的状态
                    "time" => date('Y-m-d H:i:s'),
                    "remark" => "退款调整，原订单：{$order_id}，退款金额：{$refund_amount}，损失：{$refund_loss}"
                ];

                Db()->table("activity_income_log")->insert($adjustment_data);

                // {{ AURA-X: Remove - 移除普通执行流程中的exception_log. Confirmed via 寸止. }}
            } else {
                // 全额退款，直接删除原收入记录
                Db()->_exec(
                    "DELETE FROM activity_income_log WHERE order_id = :order_id",
                    [":order_id" => $order_id]
                );

                // {{ AURA-X: Remove - 移除普通执行流程中的exception_log. Confirmed via 寸止. }}
            }

        } catch (\Throwable $e) {
            $this->exception_log("处理退款收入调整失败：" . $e->getMessage());
        }
    }

    // {{ AURA-X: Add - 活动结算逻辑，更新huodong表的结算字段. Confirmed via 寸止. }}
    /**
     * 结算活动收入，更新huodong表的结算相关字段
     * 此方法通常由定时任务调用，处理已结束的活动
     * @param int $huodong_id 活动ID
     */
    public function settleActivityIncome($huodong_id) {
        try {
            // 获取活动信息
            $huodong_info = Db()->_fetch(
                "SELECT id, uid, name, end_time, jiesuan_status, pay_type FROM huodong WHERE id = :huodong_id",
                [":huodong_id" => $huodong_id]
            );

            if (empty($huodong_info)) {
                $this->exception_log("活动结算失败：活动不存在，ID：{$huodong_id}");
                return false;
            }

            // 只处理线上支付的活动
            if ($huodong_info['pay_type'] != 1) {
                // 线下支付活动设置为无需结算
                Db()->_exec(
                    "UPDATE huodong SET jiesuan_status = 2 WHERE id = :huodong_id",
                    [":huodong_id" => $huodong_id]
                );
                return true;
            }

            // 检查是否已经结算过
            if ($huodong_info['jiesuan_status'] == 1) {
                return true; // 已经结算过
            }

            // 统计活动的总收入
            $income_stats = Db()->_fetch(
                "SELECT
                    SUM(CASE WHEN total_amount > 0 THEN publisher_income ELSE 0 END) as total_income,
                    SUM(CASE WHEN total_amount < 0 THEN ABS(publisher_income) ELSE 0 END) as total_refund
                FROM activity_income_log
                WHERE activity_id = :activity_id",
                [":activity_id" => $huodong_id]
            );

            $total_income = floatval($income_stats['total_income'] ?? 0);
            $total_refund = floatval($income_stats['total_refund'] ?? 0);
            $net_income = $total_income - $total_refund;

            // 更新活动结算信息
            $settlement_data = [
                "jiesuan_status" => 1, // 已结算
                "jiesuan_money" => $net_income,
                "jiesuan_yongjin" => 0, // 活动收入不涉及佣金
                "jiesuan_choujiang" => 0 // 活动收入不涉及抽奖
            ];

            Db()->table("huodong")->where("id = {$huodong_id}")->update($settlement_data);

            // 更新activity_income_log表中的状态为可提取
            Db()->_exec(
                "UPDATE activity_income_log SET status = 1, settlement_time = NOW(), available_time = NOW()
                WHERE activity_id = :activity_id AND status = 0",
                [":activity_id" => $huodong_id]
            );

            // {{ AURA-X: Remove - 移除普通执行流程中的exception_log. Confirmed via 寸止. }}
            return true;

        } catch (\Throwable $e) {
            $this->exception_log("活动收入结算失败：" . $e->getMessage());
            return false;
        }
    }

    /*
	* @apiName 删除报名信息（支付失败时使用）
	* @method delete_baoming
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param order_id string 报名订单编号
	* @return {"status":"ok","msg":"操作成功"}
	* @description 将报名订单状态设置为6（支付失败报名已删除），用于支付失败后清理未支付订单
	*/
    public function delete_baoming($uid, $token, $order_id)
    {
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32 ||
            empty($order_id)
        ) return ["status" => "error", "msg" => "参数错误"];
        $uid = (int)$uid;
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        dbConn();
        $baoming_order = Db()->table("huodong_baoming_order")->prepareParam([":order_id" => $order_id])->where("order_id=:order_id AND uid={$uid}")->fetch();
        if (empty($baoming_order)) {
            return ["status" => "error", "msg" => "报名信息不存在"];
        }

        Db::begin();
        try {
            //
            // {{ AURA-X: Modify - 恢复删除报名逻辑，状态6用于支付失败报名已删除. Confirmed via 寸止. }}
            $sql = "UPDATE `huodong_baoming_order` SET `status`=6 WHERE id={$baoming_order['id']} AND uid={$uid} AND status=1";
            $rowCount = Db()->_exec($sql);
            if (empty($rowCount)) {
                $this->logWriteError("删除报名订单信息失败", [
                    'order_id' => $order_id,
                    'operation' => 'delete_baoming_order'
                ]);
                return ["status" => "error", "msg" => "删除报名订单信息失败"];
            }
            //
            $sql = "UPDATE `huodong` SET `baoming_num`=`baoming_num`-1 WHERE id=:huodong_id";
            Db()->_exec($sql, [":huodong_id" => $baoming_order['huodong_id']]);

            $this->user_log($uid, "报名失败删除报名信息【{$baoming_order['order_id']}】");
            //
            Db::commit();
            return ["status" => "ok", "msg" => "操作成功"];
        } catch (\Throwable $e) {
            Db::rollback();
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
	* @apiName 获取活动报名列表
	* @method get_baoming_list
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param huodong_id string 活动编号
	* @param page string 页码,默认1
	* @param page_size string 每页多少条,默认20
	* @return {"status":"ok","data":[{"id":4,"order_id":"20231118130603127893","uid":2,"money":"100.00","yongjin_money":"10.00","time":"2023-11-18 13:06:03","pay_time":null,"status":1,"is_jiesuan":0,"user":{"uid":2,"avatar":"http:\/\/127.0.0.1\/test.jpg","nickname":"test","mobile":"15800000000"}},{"id":3,"order_id":"20231118130602506823","uid":2,"money":"100.00","yongjin_money":"10.00","time":"2023-11-18 13:06:02","pay_time":null,"status":1,"is_jiesuan":0,"user":{"uid":2,"avatar":"http:\/\/127.0.0.1\/test.jpg","nickname":"test","mobile":"15800000000"}}]}
	*/
    public function get_baoming_list($uid, $token, $huodong_id, $page = 1, $page_size = 20)
    {
        try {
            if (
                empty($uid) ||
                !check($uid, "intgt0") ||
                empty($token) ||
                strlen($token) != 32 ||
                empty($page) ||
                !check($page, "intgt0") ||
                empty($page_size) ||
                !check($page_size, "intgt0") ||
                !check($huodong_id, "intgt0")
            ) return ["status" => "error", "msg" => "参数错误"];
            $uid = (int)$uid;
            $huodong_id = (int)$huodong_id;
            $page = (int)$page;
            $page_size = (int)$page_size;
            if (!$this->auth($uid, $token)) {
                return ["status" => "relogin", "msg" => "登录信息验证失败"];
            }
            dbConn();
            $huodong_info = Db()->table("huodong")->where("id={$huodong_id} AND uid={$uid}")->fetch();
            if (empty($huodong_info)) {
                return ["status" => "empty", "msg" => "活动信息未找到"];
            }
            $data = Db()->table("huodong_baoming_order")->select("id,order_id,uid,money,yongjin_money,time,pay_time,status,is_choujiang,is_jiesuan,lianxi_name,lianxi_mobile,lianxi_sex")->where("huodong_id={$huodong_id} AND status=1")->order("id DESC")->page($page, $page_size);
            if (empty($data)) {
                return ["status" => "empty", "msg" => "资源不存在"];
            }
            foreach ($data as &$row) {
                $user = Db()->table("user")->select("uid,avatar,nickname,mobile,sex")->where("uid={$row['uid']}")->fetch();
                $row['user'] = $user ?: new \stdClass();
                //
            }
            return ["status" => "ok", "data" => $data];
        } catch(\Throwable $e) {
            // 记录详细的错误日志
            $this->logWriteError("获取活动报名列表失败", [
                'uid' => $uid,
                'huodong_id' => $huodong_id,
                'page' => $page,
                'page_size' => $page_size,
                'exception_message' => $e->getMessage(),
                'exception_trace' => $e->getTraceAsString()
            ]);
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
	* @apiName 获取活动报名列表公开
	* @method get_baoming_list_public
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param huodong_id string 活动编号
	* @param page string 页码,默认1
	* @param page_size string 每页多少条,默认20
	* @return {"status":"ok","data":[{"id":4,"uid":2,"user":{"uid":2,"avatar":"http:\/\/127.0.0.1\/test.jpg","nickname":"test"}}]}
	*/
    public function get_baoming_list_public($uid, $token, $huodong_id, $page = 1, $page_size = 20)
    {
        try {
            if (
                empty($uid) ||
                !check($uid, "intgt0") ||
                empty($token) ||
                strlen($token) != 32 ||
                empty($page) ||
                !check($page, "intgt0") ||
                empty($page_size) ||
                !check($page_size, "intgt0") ||
                !check($huodong_id, "intgt0")
            ) return ["status" => "error", "msg" => "参数错误"];
            $uid = (int)$uid;
            $huodong_id = (int)$huodong_id;
            $page = (int)$page;
            $page_size = (int)$page_size;
            if (!$this->auth($uid, $token)) {
                return ["status" => "relogin", "msg" => "登录信息验证失败"];
            }
            dbConn();
            $huodong_info = Db()->table("huodong")->where("id={$huodong_id}")->fetch();
            if (empty($huodong_info)) {
                return ["status" => "empty", "msg" => "活动信息未找到"];
            }
            $data = Db()->table("huodong_baoming_order")->select("id,uid,pay_time")->where("huodong_id={$huodong_id} AND status=1")->order("id DESC")->page($page, $page_size);
            if (empty($data)) {
                return ["status" => "empty", "msg" => "资源不存在"];
            }
            foreach ($data as &$row) {
                $user = Db()->table("user")->select("uid,avatar,nickname,sex")->where("uid={$row['uid']}")->fetch();
                $row['user'] = $user ?: new \stdClass();
                //
            }
            return ["status" => "ok", "data" => $data];
        } catch(\Throwable $e) {
            // 记录详细的错误日志
            $this->logWriteError("获取活动公开报名列表失败", [
                'uid' => $uid,
                'huodong_id' => $huodong_id,
                'page' => $page,
                'page_size' => $page_size,
                'exception_message' => $e->getMessage(),
                'exception_trace' => $e->getTraceAsString()
            ]);
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
	* @apiName 添加收藏
	* @method shoucang_add
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param huodong_id string 活动编号
	* @return {"status":"ok","msg":"操作成功"}
	*/
    public function shoucang_add($uid, $token, $huodong_id)
    {
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32 ||
            !check($huodong_id, "intgt0")
        ) return ["status" => "error", "msg" => "参数错误"];
        if (!$this->auth($uid, $token)) return ["status" => "relogin", "msg" => "登录信息验证失败"];
        $uid = (int)$uid;
        $huodong_id = (int)$huodong_id;
        dbConn();
        $huodong_info = Db()->table("huodong")->select("id")->where("id={$huodong_id} AND status=1")->fetch();
        if (empty($huodong_info)) {
            return ["status" => "error", "msg" => "活动不存在"];
        }
        $data = [
            "uid" => $uid,
            "huodong_id" => $huodong_id,
        ];
        $prepareParam = [

        ];
        dbConn();
        try {
            Db()->table("huodong_shoucang")->prepareParam($prepareParam)->insert($data);
            //
            $this->user_log($uid, "收藏活动【{$huodong_id}】");
            //
            return ["status" => "ok", "msg" => "操作成功"];
        } catch (\Throwable $e) {
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
	* @apiName 删除收藏
	* @method shoucang_del
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param ids string 活动编号多个逗号隔开
	* @return {"status":"ok","msg":"操作成功"}
	*/
    public function shoucang_del($uid, $token, $ids)
    {
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32
        ) return ["status" => "error", "msg" => "参数错误"];
        $uid = (int)$uid;
        $ids_arr = explode(",", $ids);
        foreach ($ids_arr as $id) {
            if (!check($id, "intgt0")) {
                return ["status" => "error", "msg" => "参数错误"];
            }
        }
        $ids_str = implode(",", $ids_arr);
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        dbConn();
        try {
            if (!Db()->table("huodong_shoucang")->where("uid={$uid} AND huodong_id IN ({$ids_str})")->del()) {
                // 记录删除收藏失败日志
                $this->logWriteError("删除活动收藏失败", [
                    'uid' => $uid,
                    'huodong_ids' => $ids_str,
                    'operation' => 'shoucang_del'
                ]);
                return ["status" => "error", "msg" => "操作失败"];
            }
            //
            $this->user_log($uid, "删除活动收藏信息【{$ids_str}】");
            //
            return ["status" => "ok", "msg" => "操作成功"];
        } catch (\Throwable $e) {
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
	* @apiName 点赞
	* @method zan_add
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param huodong_id string 活动编号
	* @return {"status":"ok","msg":"操作成功"}
	*/
    public function zan_add($uid, $token, $huodong_id)
    {
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32 ||
            !check($huodong_id, "intgt0")
        ) return ["status" => "error", "msg" => "参数错误"];
        if (!$this->auth($uid, $token)) return ["status" => "relogin", "msg" => "登录信息验证失败"];
        $uid = (int)$uid;
        $huodong_id = (int)$huodong_id;
        dbConn();
        $huodong_info = Db()->table("huodong")->select("id")->where("id={$huodong_id} AND status=1")->fetch();
        if (empty($huodong_info)) {
            return ["status" => "error", "msg" => "活动不存在"];
        }
        $data = [
            "uid" => $uid,
            "huodong_id" => $huodong_id,
        ];
        $prepareParam = [

        ];
        dbConn();
        try {
            Db()->table("huodong_zan")->prepareParam($prepareParam)->insert($data);
            //
            $this->user_log($uid, "点赞活动【{$huodong_id}】");
            //
            return ["status" => "ok", "msg" => "操作成功"];
        } catch (\Throwable $e) {
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
	* @apiName 取消点赞
	* @method zan_del
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param huodong_ids string 活动编号多个逗号隔开
	* @return {"status":"ok","msg":"操作成功"}
	*/
    public function zan_del($uid, $token, $huodong_ids)
    {
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32
        ) return ["status" => "error", "msg" => "参数错误"];
        $uid = (int)$uid;
        $ids_arr = explode(",", $huodong_ids);
        foreach ($ids_arr as $id) {
            if (!check($id, "intgt0")) {
                return ["status" => "error", "msg" => "参数错误"];
            }
        }
        $ids_str = implode(",", $ids_arr);
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }
        dbConn();
        try {
            if (!Db()->table("huodong_zan")->where("huodong_id IN ({$ids_str}) AND uid={$uid}")->del()) {
                // 记录取消点赞失败日志
                $this->logWriteError("取消活动点赞失败", [
                    'uid' => $uid,
                    'huodong_ids' => $ids_str,
                    'operation' => 'zan_del'
                ]);
                return ["status" => "error", "msg" => "操作失败"];
            }
            //
            $this->user_log($uid, "取消活动点赞【{$ids_str}】");
            //
            return ["status" => "ok", "msg" => "操作成功"];
        } catch (\Throwable $e) {
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
	* @apiName 添加评价
	* @method add_pingjia
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param huodong_id string 活动编号
	* @param contents string 内容
	* @param imgs_url string 图片地址,多个英文逗号隔开(可选)
	* @return {"status":"ok","msg":"操作成功"}
	*/
    public function add_pingjia($uid, $token, $huodong_id, $contents = "", $imgs_url = "")
    {
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32 ||
            !check($huodong_id, "intgt0")
        ) return ["status" => "error", "msg" => "参数错误"];
        if (empty($contents) && empty($imgs_url)) {
            return ["status" => "error", "msg" => "缺少提问内容"];
        }
        if (!empty($imgs_url)) {
            $imgs_arr = explode(",", $imgs_url);
            foreach ($imgs_arr as $img) {
                if (!check($img, "url")) {
                    return ["status" => "error", "msg" => "参数错误"];
                }
            }
            $imgs_url = "|" . implode("|", $imgs_arr) . "|";
        }
        if (!$this->auth($uid, $token)) return ["status" => "relogin", "msg" => "登录信息验证失败"];
        $uid = (int)$uid;
        $huodong_id = (int)$huodong_id;
        $contents = mb_substr(htmlspecialchars($contents), 0, 500);
        //
        dbConn();
        //
        $huodong_info = Db()->table("huodong")->select("id,start_time")->where("id={$huodong_id} AND status=1")->fetch();
        if (empty($huodong_info)) {
            return ["status" => "error", "msg" => "活动不存在"];
        }
        // 修改：移除报名验证，允许所有用户提问
        /*if (strtotime("{$huodong_info['start_time']}") > _NOW_) {
            return ["status" => "error", "msg" => "活动还未开始"];
        }*/
        // 注释掉报名验证，允许所有用户提问
        /*$baoming_order = Db()->table("huodong_baoming_order")->where("huodong_id={$huodong_id} AND uid={$uid} AND status=1")->fetch();
        if (empty($baoming_order)) {
            return ["status" => "error", "msg" => "参与活动的人才能评价"];
        }*/
        /*if ($baoming_order['is_pingjia'] != 0) {
            return ["status" => "error", "msg" => "当前活动已评价过"];
        }*/
        //
        Db::begin();
        try {
            $data = [
                "uid" => $uid,
                "huodong_id" => $huodong_id,
                "imgs_url" => ":imgs_url",
                "contents" => ":contents",
            ];
            $prepareParam = [
                ":imgs_url" => $imgs_url,
                ":contents" => $contents,
            ];
            Db()->table("huodong_pingjia")->prepareParam($prepareParam)->insert($data);
            //
            $sql = "UPDATE `huodong` SET `pingjia_times`=`pingjia_times`+1 WHERE id=:huodong_id";
            Db()->_exec($sql, [":huodong_id" => $huodong_id]);

            // 修改：移除报名订单更新，因为不再需要报名验证
            /*$sql = "UPDATE `huodong_baoming_order` SET `is_pingjia`=1 WHERE id=:order_id";
            Db()->_exec($sql, [":order_id" => $baoming_order['id']]);*/

            // 添加：给活动发布者发送通知
            try {
                $activity_creator_uid = $huodong_info['uid'];
                if ($activity_creator_uid && $activity_creator_uid != $uid) {
                    // 获取提问者信息
                    $questioner_info = Db()->_fetch("SELECT nickname FROM user WHERE uid = :uid", [":uid" => $uid]);
                    $questioner_name = $questioner_info['nickname'] ?? '用户';

                    $this->create_notification(
                        $activity_creator_uid,
                        'activity_question',
                        '活动收到新提问',
                        "用户「{$questioner_name}」在您的活动「{$huodong_info['title']}」中提问：" . mb_substr($contents, 0, 50) . (mb_strlen($contents) > 50 ? '...' : ''),
                        $huodong_id
                    );
                }
            } catch (\Throwable $e) {
                // 通知发送失败不影响主流程
                $this->exception_log("发送提问通知失败：" . $e->getMessage());
            }
            //
            Db::commit();
            return ["status" => "ok", "msg" => "操作成功"];
        } catch (\Throwable $e) {
            Db::rollback();
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "操作失败"];
        }
    }

    /*
	* @apiName 回答活动提问
	* @method reply_pingjia
	* @POST
	* @param uid string 用户编号
	* @param token string token
	* @param pingjia_id string 提问ID
	* @param reply_content string 回答内容
	* @return {"status":"ok","msg":"回答成功"}
	*/
    public function reply_pingjia($uid, $token, $pingjia_id, $reply_content = "")
    {
        if (
            empty($uid) ||
            !check($uid, "intgt0") ||
            empty($token) ||
            strlen($token) != 32 ||
            !check($pingjia_id, "intgt0") ||
            empty($reply_content)
        ) return ["status" => "error", "msg" => "参数错误"];

        if (!$this->auth($uid, $token)) return ["status" => "relogin", "msg" => "登录信息验证失败"];

        $uid = (int)$uid;
        $pingjia_id = (int)$pingjia_id;

        dbConn();

        try {
            // 获取提问信息
            $pingjia_info = Db()->_fetch("SELECT p.*, h.uid as activity_creator_uid, h.title as activity_title
                                         FROM huodong_pingjia p
                                         LEFT JOIN huodong h ON p.huodong_id = h.id
                                         WHERE p.id = :pingjia_id", [":pingjia_id" => $pingjia_id]);

            if (empty($pingjia_info)) {
                return ["status" => "error", "msg" => "提问不存在"];
            }

            // 验证只有活动发布者可以回答
            if ($pingjia_info['activity_creator_uid'] != $uid) {
                return ["status" => "error", "msg" => "只有活动发布者可以回答提问"];
            }

            Db::begin();

            // 更新提问记录，添加回答内容
            $update_data = [
                "reply_content" => ":reply_content",
                "reply_time" => date('Y-m-d H:i:s'),
                "is_replied" => 1
            ];
            $prepareParam = [
                ":reply_content" => $reply_content
            ];

            Db()->table("huodong_pingjia")
                ->where("id = {$pingjia_id}")
                ->prepareParam($prepareParam)
                ->update($update_data);

            // 给提问者发送通知
            try {
                $questioner_uid = $pingjia_info['uid'];
                if ($questioner_uid && $questioner_uid != $uid) {
                    // 获取活动发布者信息
                    $creator_info = Db()->_fetch("SELECT nickname FROM user WHERE uid = :uid", [":uid" => $uid]);
                    $creator_name = $creator_info['nickname'] ?? '活动发布者';

                    $this->create_notification(
                        $questioner_uid,
                        'activity_answer',
                        '您的提问收到回答',
                        "活动发布者「{$creator_name}」回答了您在活动「{$pingjia_info['activity_title']}」中的提问：" . mb_substr($reply_content, 0, 50) . (mb_strlen($reply_content) > 50 ? '...' : ''),
                        $pingjia_info['huodong_id']
                    );
                }
            } catch (\Throwable $e) {
                // 通知发送失败不影响主流程
                $this->exception_log("发送回答通知失败：" . $e->getMessage());
            }

            Db::commit();
            return ["status" => "ok", "msg" => "回答成功"];

        } catch (\Throwable $e) {
            Db::rollback();
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "回答失败"];
        }
    }

    /*
	* @apiName 获取评价
	* @method get_pingjia
	* @POST
	* @param huodong_id string 活动编号
	* @param page string 页码,默认1
	* @param page_size string 每页多少条,默认20
	* @return {"status":"ok","data":[{"id":2,"uid":2,"contents":"评价内容","imgs_url":["http:\/\/************\/test.jpg","http:\/\/************\/test.jpg","http:\/\/************\/test.jpg"],"time":"2023-11-18 13:21:00","user":{"uid":2,"avatar":"http:\/\/127.0.0.1\/test.jpg","nickname":"test","is_huiyuan":0}}],"count":1}
	*/
    public function get_pingjia($huodong_id, $page = 1, $page_size = 20)
    {
        try {
            if (
                !check($huodong_id, "intgt0") ||
                empty($page) ||
                !check($page, "intgt0") ||
                empty($page_size) ||
                !check($page_size, "intgt0")
            ) return ["status" => "error", "msg" => "参数错误"];
            $huodong_id = (int)$huodong_id;
            $page = (int)$page;
            $page_size = (int)$page_size;
            //
            dbConn();
            $where = "`huodong_id`={$huodong_id}";
            //
            $data = Db()->table("huodong_pingjia")->select("id,uid,contents,imgs_url,time,reply_content,reply_time,is_replied")->where($where)->order("id DESC")->page($page, $page_size);
            if (empty($data)) {
                return ["status" => "empty", "msg" => "暂无数据"];
            }
            foreach ($data as &$row) {
                //
                $user_info = Db()->table("user")->select("uid,avatar,nickname,is_huiyuan")->where("uid={$row['uid']}")->fetch();
                $row['user'] = $user_info ? $user_info : new \stdClass();
                //
                $row['imgs_url'] = !empty($row['imgs_url']) ? explode("|", trim($row['imgs_url'], "|")) : [];
                //
            }
            return ["status" => "ok", "data" => $data, "count" => \core\Page::$count];
        } catch(\Throwable $e) {
            // 记录详细的错误日志
            $this->logWriteError("获取活动评价列表失败", [
                'huodong_id' => $huodong_id,
                'page' => $page,
                'page_size' => $page_size,
                'exception_message' => $e->getMessage(),
                'exception_trace' => $e->getTraceAsString()
            ]);
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
    * @apiName 活动签到
    * @method checkin
    * @POST
    * @param uid string 用户编号
    * @param token string token
    * @param huodong_id string 活动编号
    * @param lat string 用户纬度（线下活动必填）
    * @param lng string 用户经度（线下活动必填）
    * @return {"status":"ok","msg":"签到成功","data":{"points_awarded":10}}
    */
    public function checkin($uid, $token, $huodong_id, $lat = null, $lng = null)
    {
        // 参数验证
        if (
            empty($uid) || !check($uid, "intgt0") ||
            empty($token) || strlen($token) != 32 ||
            empty($huodong_id) || !check($huodong_id, "intgt0")
        ) {
            return ["status" => "error", "msg" => "参数错误"];
        }

        $uid = (int)$uid;
        $huodong_id = (int)$huodong_id;

        // 验证用户登录
        if (!$this->auth($uid, $token)) {
            return ["status" => "relogin", "msg" => "登录信息验证失败"];
        }

        dbConn();

        // 获取活动信息
        $huodong_info = Db()->table("huodong")
            ->select("id,name,start_time,end_time,is_online,lng,lat,addr")
            ->where("id={$huodong_id} AND status=1")
            ->fetch();

        if (empty($huodong_info)) {
            return ["status" => "error", "msg" => "活动不存在或已下架"];
        }

        // {{ AURA-X: Add - 检查活动是否启用签到功能. Confirmed via 寸止. }}
        // 检查活动是否启用签到功能
        if ($huodong_info['enable_checkin'] != 1) {
            return ["status" => "error", "msg" => "该活动未启用签到功能"];
        }

        // 检查是否已经签到过
        $existing_checkin = Db()->table("huodong_checkin")
            ->where("uid={$uid} AND huodong_id={$huodong_id}")
            ->fetch();

        if (!empty($existing_checkin)) {
            return ["status" => "error", "msg" => "您已经签到过了"];
        }

        // 验证签到时间（活动开始前30分钟到开始后30分钟）
        $current_time = time();
        $start_time = strtotime($huodong_info['start_time']);
        $checkin_start = $start_time - (30 * 60); // 开始前30分钟
        $checkin_end = $start_time + (30 * 60);   // 开始后30分钟

        if ($current_time < $checkin_start) {
            return ["status" => "error", "msg" => "签到时间未到，请在活动开始前30分钟内签到"];
        }

        if ($current_time > $checkin_end) {
            return ["status" => "error", "msg" => "签到时间已过，签到时间为活动开始前后30分钟内"];
        }

        $distance = null;
        $checkin_lat = null;
        $checkin_lng = null;

        // 线下活动需要验证地理位置
        if ($huodong_info['is_online'] == 0) {
            if (empty($lat) || empty($lng) || !is_numeric($lat) || !is_numeric($lng)) {
                return ["status" => "error", "msg" => "线下活动签到需要提供位置信息"];
            }

            $checkin_lat = (float)$lat;
            $checkin_lng = (float)$lng;
            $activity_lat = (float)$huodong_info['lat'];
            $activity_lng = (float)$huodong_info['lng'];

            // 计算距离（使用Haversine公式）
            $distance = $this->calculateDistance($checkin_lat, $checkin_lng, $activity_lat, $activity_lng);

            // 验证距离是否在1000米内
            if ($distance > 1000) {
                return ["status" => "error", "msg" => "您距离活动地点太远，无法签到（需在1000米内）"];
            }
        }

        // {{ AURA-X: Modify - 根据签到时间计算积分奖励. Confirmed via 寸止. }}
        // 计算积分奖励（根据签到时间）
        $points_awarded = 0;

        // {{ AURA-X: Modify - 修正配置名称匹配SQL插入的配置. Confirmed via 寸止. }}
        // 获取积分配置
        $normal_points = (int)Db()->table("config")->where("name='checkin_normal_points'")->getColumn("val") ?: 3;
        $late_penalty = (int)Db()->table("config")->where("name='checkin_late_penalty'")->getColumn("val") ?: 10;

        // {{ AURA-X: Modify - 修正迟到签到扣分逻辑. Confirmed via 寸止. }}
        if ($current_time <= $start_time + (30 * 60)) {
            // 活动开始后30分钟内为正常签到
            $points_awarded = $normal_points;
            $checkin_type = "正常签到";
        } else {
            // 超过30分钟为迟到签到，扣分
            $points_awarded = -$late_penalty;
            $checkin_type = "迟到签到";
        }

        try {
            // 开始事务
            Db()::begin();

            // 插入签到记录
            $checkin_data = [
                "uid" => $uid,
                "huodong_id" => $huodong_id,
                "checkin_time" => date("Y-m-d H:i:s"),
                "checkin_lat" => $checkin_lat,
                "checkin_lng" => $checkin_lng,
                "distance" => $distance,
                "points_awarded" => $points_awarded,
                "checkin_ip" => $this->getClientIP(),
                "status" => 1
            ];

            Db()->table("huodong_checkin")->insert($checkin_data);

            // 更新用户积分
            Db()->table("user")->where("uid={$uid}")->update([
                "points" => "points + {$points_awarded}"
            ]);

            // 获取更新后的积分余额
            $points_balance = Db()->table("user")->where("uid={$uid}")->getColumn("points");

            // {{ AURA-X: Modify - 使用User类的add_points方法处理积分. Confirmed via 寸止. }}
            // 使用User类的add_points方法处理积分
            $description = "{$checkin_type}：{$huodong_info['name']}";
            \controller\User::add_points($uid, $points_awarded, 'activity_checkin', $huodong_id, $description);

            // 获取更新后的积分余额
            $points_balance = Db()->table("user")->where("uid={$uid}")->getColumn("points");

            // 提交事务
            Db::commit();

            // 记录用户操作日志
            $this->user_log($uid, "活动签到【{$huodong_info['name']}】");

            return [
                "status" => "ok",
                "msg" => "签到成功",
                "data" => [
                    "points_awarded" => $points_awarded,
                    "points_balance" => $points_balance,
                    "distance" => $distance
                ]
            ];

        } catch (\Throwable $e) {
            // 回滚事务
            Db::rollback();
            $this->exception_log($e->getMessage());
            return ["status" => "error", "msg" => "签到失败，请稍后重试"];
        }
    }

    /*
    * @apiName 处理活动缺席用户扣分（定时任务）
    * @method process_absence_penalty
    * @return array 处理结果
    */
    public function process_absence_penalty()
    {
        dbConn();

        try {
            // {{ AURA-X: Modify - 修正配置名称匹配SQL插入的配置. Confirmed via 寸止. }}
            // 获取缺席扣分配置
            $absence_penalty = (int)Db()->table("config")
                ->where("name='absence_penalty'")
                ->getColumn("val") ?: 20;

            // 查找已结束且启用签到的活动
            $sql = "SELECT h.id, h.name, h.uid as organizer_uid, h.start_time, h.end_time
                    FROM huodong h
                    WHERE h.enable_checkin = 1
                    AND h.status = 1
                    AND h.end_time < NOW()
                    AND h.end_time >= DATE_SUB(NOW(), INTERVAL 1 DAY)";

            $ended_activities = Db::_fetchAll($sql);

            $processed_count = 0;
            $total_penalties = 0;

            foreach ($ended_activities as $activity) {
                // 获取该活动的报名用户
                $registered_users = Db()->table("huodong_baoming_order")
                    ->select("uid")
                    ->where("huodong_id={$activity['id']} AND status=1")
                    ->fetchAll();

                foreach ($registered_users as $user) {
                    // 检查用户是否签到
                    $checkin_record = Db()->table("huodong_checkin")
                        ->where("uid={$user['uid']} AND huodong_id={$activity['id']}")
                        ->fetch();

                    // {{ AURA-X: Modify - 修正变量名使用. Confirmed via 寸止. }}
                    if (empty($checkin_record)) {
                        // 用户缺席，扣除积分
                        $description = "缺席活动：{$activity['name']}";
                        $absence_points = -$absence_penalty; // 扣分为负数
                        \controller\User::add_points($user['uid'], $absence_points, 'activity_absence', $activity['id'], $description);

                        $processed_count++;
                        $total_penalties += $absence_penalty;
                    }
                }
            }

            return [
                "status" => "ok",
                "msg" => "缺席处理完成",
                "data" => [
                    "processed_activities" => count($ended_activities),
                    "processed_users" => $processed_count,
                    "total_penalties" => $total_penalties
                ]
            ];

        } catch (\Throwable $e) {
            $this->exception_log("处理缺席扣分失败: " . $e->getMessage());
            return ["status" => "error", "msg" => "处理缺席扣分失败"];
        }
    }

    /*
    * 计算两点间距离（米）
    * 使用Haversine公式
    */
    private function calculateDistance($lat1, $lng1, $lat2, $lng2)
    {
        $earthRadius = 6371000; // 地球半径（米）

        $lat1Rad = deg2rad($lat1);
        $lat2Rad = deg2rad($lat2);
        $deltaLatRad = deg2rad($lat2 - $lat1);
        $deltaLngRad = deg2rad($lng2 - $lng1);

        $a = sin($deltaLatRad / 2) * sin($deltaLatRad / 2) +
             cos($lat1Rad) * cos($lat2Rad) *
             sin($deltaLngRad / 2) * sin($deltaLngRad / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return round($earthRadius * $c);
    }

    /*
    * 获取客户端IP地址
    */
    private function getClientIP()
    {
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            return $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            return $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
        }
    }

    public function _empty()
    {
        return ["status" => "error", "msg" => "URL error"];
    }

    function __destruct()
    {

    }

    public static function auth($uid, $token)
    {
        return parent::auth($uid, $token); // TODO: Change the autogenerated stub
    }

    public static function exception_log($msg)
    {
        return parent::exception_log($msg); // TODO: Change the autogenerated stub
    }

    /*
     * 创建通知的私有方法
     * @param int $uid 用户ID，如果为0则创建全局通知
     * @param string $type 通知类型
     * @param string $title 通知标题
     * @param string $content 通知内容
     * @param int $related_id 关联ID
     */
    private function create_notification($uid, $type, $title, $content, $related_id = null) {
        try {
            $is_global = ($uid == 0) ? 1 : 0;
            $data = [
                "uid" => $uid,
                "type" => ":type",
                "title" => ":title",
                "content" => ":content",
                "related_id" => $related_id,
                "is_read" => 0,
                "is_global" => $is_global
            ];
            $prepareParam = [
                ":type" => htmlspecialchars($type),
                ":title" => htmlspecialchars($title),
                ":content" => htmlspecialchars($content)
            ];

            Db()->table("user_notifications")
                ->prepareParam($prepareParam)
                ->insert($data);

        } catch (\Throwable $e) {
            $this->exception_log("创建通知失败：" . $e->getMessage());
        }
    }

    /**
     * {{ AURA-X: Add - 记录线下收款活动收入. Confirmed via 寸止. }}
     * 记录线下收款活动的收入
     * @param array $order_info 订单信息
     */
    private function record_offline_activity_income($order_info) {
        try {
            // 获取活动信息
            $activity = Db()->table("huodong")
                ->select("id, uid, name, pay_type")
                ->where("id={$order_info['huodong_id']}")
                ->fetch();

            if (empty($activity)) {
                $this->exception_log("线下活动收入记录失败：活动不存在，订单：{$order_info['order_id']}");
                return;
            }

            // 检查是否已经记录过收入
            $existing = Db()->table("activity_income_log")
                ->where("order_id='{$order_info['order_id']}'")
                ->fetch();

            if (!empty($existing)) {
                $this->exception_log("线下活动收入重复记录，跳过：{$order_info['order_id']}");
                return;
            }

            $total_amount = (float)$order_info['money'];

            // 线下收款：不记录到待结算金额，仅作记录
            $income_data = [
                "activity_id" => $order_info['huodong_id'],
                "publisher_uid" => $activity['uid'],
                "order_id" => $order_info['order_id'],
                "total_amount" => $total_amount,
                "platform_fee_rate" => 0.00,
                "platform_fee" => 0.00,
                "publisher_income" => $total_amount,
                "status" => 3, // 线下收款状态
                "time" => DATETIME,
                "remark" => "活动报名收入（线下收款），活动：{$activity['name']}"
            ];

            $income_id = Db()->table("activity_income_log")->insert($income_data);

            if (!$income_id) {
                $this->exception_log("线下活动收入记录插入失败，订单：{$order_info['order_id']}");
                return;
            }

            // {{ AURA-X: Remove - 移除普通执行流程中的exception_log. Confirmed via 寸止. }}

        } catch (\Throwable $e) {
            $this->exception_log("记录线下活动收入异常：" . $e->getMessage());
        }
    }

    // {{ AURA-X: Add - 活动相册功能API方法. Confirmed via 寸止. }}

    /*
     * @apiName 获取活动相册图片列表
     * @method get_activity_photos
     * @POST
     * @param activity_id int 活动ID
     * @param page int 页码，默认1
     * @param page_size int 每页数量，默认20
     * @return {"status":"ok","data":[{"id":1,"activity_id":1,"user_id":1,"photo_url":"http://example.com/photo.jpg","upload_time":"2024-01-01 12:00:00","user":{"uid":1,"nickname":"用户名","avatar":"头像"}}]}
     */
    public function get_activity_photos($activity_id, $page = 1, $page_size = 20)
    {
        try {
            // 参数验证
            if (empty($activity_id) || !check($activity_id, "intgt0")) {
                return ["status" => "error", "msg" => "参数错误"];
            }

            $activity_id = (int)$activity_id;
            $page = (int)$page;
            $page_size = (int)$page_size;

            // 验证活动是否存在
            dbConn();
            $activity = Db()->table("huodong")
                ->select("id,name,start_time")
                ->where("id=:activity_id")
                ->prepareParam([":activity_id" => $activity_id])
                ->fetch();

            if (empty($activity)) {
                return ["status" => "error", "msg" => "活动不存在"];
            }

            // 获取活动图片列表
            $photos = Db()->table("activity_photos")
                ->select("id,activity_id,user_id,photo_url,upload_time")
                ->where("activity_id=:activity_id")
                ->prepareParam([":activity_id" => $activity_id])
                ->order("upload_time DESC")
                ->page($page, $page_size);

            if (empty($photos)) {
                return ["status" => "empty", "msg" => "暂无图片"];
            }

            // 批量获取用户信息
            $user_ids = array_unique(array_column($photos, 'user_id'));
            $users = [];
            if (!empty($user_ids)) {
                $user_list = Db()->table("user")
                    ->select("uid,nickname,avatar")
                    ->where("uid IN (" . implode(',', $user_ids) . ")")
                    ->fetchAll();
                foreach ($user_list as $user) {
                    $users[$user['uid']] = $user;
                }
            }

            // 组装数据
            foreach ($photos as &$photo) {
                $photo['user'] = isset($users[$photo['user_id']]) ? $users[$photo['user_id']] : new \stdClass();
            }

            return ["status" => "ok", "data" => $photos, "count" => \core\Page::$count];

        } catch (\Throwable $e) {
            $this->logWriteError("获取活动相册失败", [
                'activity_id' => $activity_id,
                'exception_message' => $e->getMessage(),
                'exception_trace' => $e->getTraceAsString()
            ]);
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
     * @apiName 上传活动相册图片
     * @method upload_activity_photo
     * @POST
     * @param uid int 用户ID
     * @param token string 用户token
     * @param activity_id int 活动ID
     * @param photo_url string 图片URL
     * @return {"status":"ok","msg":"上传成功","data":{"id":1}}
     */
    public function upload_activity_photo($uid, $token, $activity_id, $photo_url)
    {
        try {
            // 参数验证
            if (
                empty($uid) || !check($uid, "intgt0") ||
                empty($token) || strlen($token) != 32 ||
                empty($activity_id) || !check($activity_id, "intgt0") ||
                empty($photo_url) || !check($photo_url, "url")
            ) {
                return ["status" => "error", "msg" => "参数错误"];
            }

            $uid = (int)$uid;
            $activity_id = (int)$activity_id;

            // 验证用户登录
            if (!$this->auth($uid, $token)) {
                return ["status" => "relogin", "msg" => "登录信息验证失败"];
            }

            dbConn();

            // 验证活动是否存在且已开始
            $activity = Db()->table("huodong")
                ->select("id,name,start_time,uid as publisher_uid")
                ->where("id=:activity_id")
                ->prepareParam([":activity_id" => $activity_id])
                ->fetch();

            if (empty($activity)) {
                return ["status" => "error", "msg" => "活动不存在"];
            }

            // 检查活动是否已开始
            $now = date('Y-m-d H:i:s');
            if ($now < $activity['start_time']) {
                return ["status" => "error", "msg" => "活动尚未开始，无法上传图片"];
            }

            // 检查用户上传数量限制（单个用户对单个活动最多9张）
            $user_photo_count = Db()->table("activity_photos")
                ->where("activity_id=:activity_id AND user_id=:user_id")
                ->prepareParam([
                    ":activity_id" => $activity_id,
                    ":user_id" => $uid
                ])
                ->count();

            if ($user_photo_count >= 9) {
                return ["status" => "error", "msg" => "您已上传9张图片，无法继续上传"];
            }

            // 插入图片记录
            $data = [
                "activity_id" => $activity_id,
                "user_id" => $uid,
                "photo_url" => ":photo_url",
                "upload_time" => date('Y-m-d H:i:s')
            ];

            $prepareParam = [
                ":photo_url" => htmlspecialchars(trim($photo_url))
            ];

            Db()->table("activity_photos")->prepareParam($prepareParam)->insert($data);
            $photo_id = Db()->insertId();

            if (!$photo_id) {
                return ["status" => "error", "msg" => "上传失败"];
            }

            return ["status" => "ok", "msg" => "上传成功", "data" => ["id" => $photo_id]];

        } catch (\Throwable $e) {
            $this->logWriteError("上传活动相册图片失败", [
                'uid' => $uid,
                'activity_id' => $activity_id,
                'photo_url' => $photo_url,
                'exception_message' => $e->getMessage(),
                'exception_trace' => $e->getTraceAsString()
            ]);
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }

    /*
     * @apiName 删除活动相册图片
     * @method delete_activity_photo
     * @POST
     * @param uid int 用户ID
     * @param token string 用户token
     * @param photo_id int 图片ID
     * @return {"status":"ok","msg":"删除成功"}
     */
    public function delete_activity_photo($uid, $token, $photo_id)
    {
        try {
            // 参数验证
            if (
                empty($uid) || !check($uid, "intgt0") ||
                empty($token) || strlen($token) != 32 ||
                empty($photo_id) || !check($photo_id, "intgt0")
            ) {
                return ["status" => "error", "msg" => "参数错误"];
            }

            $uid = (int)$uid;
            $photo_id = (int)$photo_id;

            // 验证用户登录
            if (!$this->auth($uid, $token)) {
                return ["status" => "relogin", "msg" => "登录信息验证失败"];
            }

            dbConn();

            // 获取图片信息
            $photo = Db()->table("activity_photos")
                ->select("id,activity_id,user_id,photo_url")
                ->where("id=:photo_id")
                ->prepareParam([":photo_id" => $photo_id])
                ->fetch();

            if (empty($photo)) {
                return ["status" => "error", "msg" => "图片不存在"];
            }

            // 获取活动信息
            $activity = Db()->table("huodong")
                ->select("id,uid as publisher_uid")
                ->where("id=:activity_id")
                ->prepareParam([":activity_id" => $photo['activity_id']])
                ->fetch();

            if (empty($activity)) {
                return ["status" => "error", "msg" => "活动不存在"];
            }

            // 权限检查：只有图片上传者或活动发布者可以删除
            if ($photo['user_id'] != $uid && $activity['publisher_uid'] != $uid) {
                return ["status" => "error", "msg" => "无权限删除此图片"];
            }

            // 删除数据库记录
            $result = Db()->table("activity_photos")
                ->where("id=:photo_id")
                ->prepareParam([":photo_id" => $photo_id])
                ->del();

            if (!$result) {
                return ["status" => "error", "msg" => "删除失败"];
            }

            // TODO: 这里可以添加删除实际文件的逻辑
            // 由于图片可能存储在不同位置，暂时只删除数据库记录

            return ["status" => "ok", "msg" => "删除成功"];

        } catch (\Throwable $e) {
            $this->logWriteError("删除活动相册图片失败", [
                'uid' => $uid,
                'photo_id' => $photo_id,
                'exception_message' => $e->getMessage(),
                'exception_trace' => $e->getTraceAsString()
            ]);
            return ["status" => "error", "msg" => "系统繁忙，请稍后再试"];
        }
    }
}

